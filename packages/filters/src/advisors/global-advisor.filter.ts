import { ErrorCodeEnum, UserLanguage } from "@enums";
import {
  ApiValidationException,
  BusinessValidationException,
} from "@exceptions";
import { I18nLookupService } from "@modules";
import {
  Catch,
  ArgumentsHost,
  HttpException,
  Injectable,
  Logger,
} from "@nestjs/common";
import { BaseExceptionFilter } from "@nestjs/core";

@Catch()
@Injectable()
export class GlobalAdvisorFilter extends BaseExceptionFilter {
  private readonly logger = new Logger(GlobalAdvisorFilter.name);

  constructor(private readonly i18n: I18nLookupService) {
    super();
  }

  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse();

    if (exception instanceof ApiValidationException) {
      const request = exception.getRequest();
      const errorCode = exception.getErrorCode();
      const statusCode = exception.getStatusCode();
      const language =
        (request?.options?.language as UserLanguage) ?? UserLanguage["en-US"];

      const customI18nKey = `exceptions.${errorCode}.${request.requestId}`;

      const messageKey = this.i18n.has(customI18nKey)
        ? customI18nKey
        : `exceptions.${errorCode}`;
      const message = this.i18n.translate(messageKey, language);

      this.logger.error(errorCode);

      return response.status(statusCode).send({
        timestamp: new Date().toISOString(),
        message,
        errorCode,
      });
    } else if (exception instanceof BusinessValidationException) {
      const errorCode = exception.getErrorCode();
      const i18nKey = exception.getI18nKey();
      const language = exception.getLanguage() as UserLanguage;
      const statusCode = exception.getStatusCode();

      const messageKey = i18nKey ? i18nKey : `exceptions.${errorCode}`;
      const message = this.i18n.translate(messageKey, language);

      this.logger.error(errorCode);

      return response.status(statusCode).send({
        timestamp: new Date().toISOString(),
        message,
        errorCode,
      });
    } else if (exception instanceof HttpException) {
      const statusCode = exception.getStatus();
      const errorResponse = exception.getResponse();

      const message =
        typeof errorResponse === "object"
          ? (errorResponse as any).message || exception.message
          : errorResponse;

      this.logger.error(`HTTP Exception: ${message}`, {
        statusCode,
        exceptionName: exception.name,
        stack: exception.stack,
        errorResponse,
      });

      return response.status(statusCode).send({
        timestamp: new Date().toISOString(),
        message,
        errorCode: exception.name,
      });
    } else {
      this.logger.error("Unhandled exception occurred");
      this.logger.error(exception);

      response.status(500).send({
        timestamp: new Date().toISOString(),
        message: "UNKNOWN ERROR",
        errorCode: ErrorCodeEnum.UnknownError,
      });
    }
  }
}
