import { HttpException } from "@nestjs/common";
import { ApiAdapterRequestDto } from "@dtos";

export class ApiValidationException extends HttpException {
  private readonly _request: Partial<ApiAdapterRequestDto>;
  private readonly _errorCode: string;
  private readonly _statusCode: number;

  constructor(
    request: Partial<ApiAdapterRequestDto>,
    errorCode: string,
    statusCode: number
  ) {
    super(errorCode, statusCode);
    this._request = request;
    this._errorCode = errorCode;
    this._statusCode = statusCode;
  }

  getRequest(): Partial<ApiAdapterRequestDto> {
    return this._request;
  }

  getErrorCode(): string {
    return this._errorCode;
  }

  getStatusCode(): number {
    return this._statusCode;
  }
}
