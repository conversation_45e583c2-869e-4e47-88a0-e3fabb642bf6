import { UserLanguage } from "@enums";
import { HttpException, HttpStatus } from "@nestjs/common";

export class BusinessValidationException extends HttpException {
  private readonly _errorCode: string;
  private readonly _i18nKey: string;
  private readonly _statusCode: number;
  private readonly _language: string;

  constructor(
    errorCode: string,
    language: string,
    i18nKey?: string,
    statusCode: number = HttpStatus.BAD_REQUEST
  ) {
    super(errorCode, statusCode);
    this._errorCode = errorCode;
    this._i18nKey = i18nKey;
    this._statusCode = statusCode;
    this._language = language;
  }

  getErrorCode(): string {
    return this._errorCode;
  }

  getI18nKey(): string {
    return this._i18nKey;
  }

  getStatusCode(): number {
    return this._statusCode;
  }

  getLanguage(): string {
    return this._language;
  }
}
