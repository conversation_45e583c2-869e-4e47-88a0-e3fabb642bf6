import { PostingRestrictionCodeEnum, PostingRestrictionTypeEnum } from "@enums";

export const PostingRestrictions: Record<PostingRestrictionCodeEnum, any> = {
  [PostingRestrictionCodeEnum.POST_NO_DEBITS]: {
    description: "Post No Debits",
    type: PostingRestrictionTypeEnum.DEBIT,
  },
  [PostingRestrictionCodeEnum.POST_NO_CREDITS]: {
    description: "Post No Credits",
    type: PostingRestrictionTypeEnum.CREDIT,
  },
  [PostingRestrictionCodeEnum.POST_NO_ENTRIES]: {
    description: "Post No Entries",
    type: PostingRestrictionTypeEnum.ALL,
  },
  [PostingRestrictionCodeEnum.REFER_CREDITS]: {
    description: "Refer Credits to Supervisor",
    type: PostingRestrictionTypeEnum.CREDIT,
  },
  [PostingRestrictionCodeEnum.REFER_DEBITS]: {
    description: "Refer Debits to Supervisor",
    type: PostingRestrictionTypeEnum.DEBIT,
  },
  [PostingRestrictionCodeEnum.REFER]: {
    description: "Refer",
    type: PostingRestrictionTypeEnum.ALL,
  },
  [PostingRestrictionCodeEnum.REFERRAL_LIST]: {
    description: "Account on Referral List",
    type: PostingRestrictionTypeEnum.ALL,
  },
  [PostingRestrictionCodeEnum.REFERRAL_LIST_CR]: {
    description: "Account on Referral List - CR",
    type: PostingRestrictionTypeEnum.CREDIT,
  },
  [PostingRestrictionCodeEnum.REFERRAL_LIST_DR]: {
    description: "Account on Referral List - DR",
    type: PostingRestrictionTypeEnum.DEBIT,
  },
  [PostingRestrictionCodeEnum.POST_CREDIT_TO_SAVINGS]: {
    description: "Post Credits to Savings A/C",
    type: PostingRestrictionTypeEnum.CREDIT,
  },
  [PostingRestrictionCodeEnum.REFER_CREDIT_BLOCK_DEBIT]: {
    description: "Refer Credit & Block Debit",
    type: PostingRestrictionTypeEnum.ALL,
  },
  [PostingRestrictionCodeEnum.REFER_DEBIT_CREDIT_BLOCK_DEBIT]: {
    description: "Refer Debit, Credit & Block Debit",
    type: PostingRestrictionTypeEnum.ALL,
  },
  [PostingRestrictionCodeEnum.BLOCK_DEBIT]: {
    description: "Block Debit",
    type: PostingRestrictionTypeEnum.DEBIT,
  },
  [PostingRestrictionCodeEnum.BLOCK_CREDIT]: {
    description: "Block Credit",
    type: PostingRestrictionTypeEnum.CREDIT,
  },
  [PostingRestrictionCodeEnum.REFER_BLOCK_DEBIT]: {
    description: "Refer & Block Debit",
    type: PostingRestrictionTypeEnum.DEBIT,
  },
  [PostingRestrictionCodeEnum.BAD_DEBT]: {
    description: "Bad Debt",
    type: PostingRestrictionTypeEnum.ALL,
  },
  [PostingRestrictionCodeEnum.REFER_DEBIT_CREDIT_BLOCK_CREDIT]: {
    description: "Refer Debit, Credit & Block Credit",
    type: PostingRestrictionTypeEnum.ALL,
  },
  [PostingRestrictionCodeEnum.BLOCK_DEBIT_CREDIT_REFER_DEBIT]: {
    description: "Block Debit, Credit & Refer Debit",
    type: PostingRestrictionTypeEnum.ALL,
  },
  [PostingRestrictionCodeEnum.REFER_BLOCK_CREDIT]: {
    description: "Refer & Block Credit",
    type: PostingRestrictionTypeEnum.CREDIT,
  },
  [PostingRestrictionCodeEnum.BLOCK_DEBIT_CREDIT]: {
    description: "Block Debit & Credit",
    type: PostingRestrictionTypeEnum.ALL,
  },
  [PostingRestrictionCodeEnum.REFER_DEBIT_BLOCK_CREDIT]: {
    description: "Refer Debit & Block Credit",
    type: PostingRestrictionTypeEnum.ALL,
  },
  [PostingRestrictionCodeEnum.REFER_BLOCK_ALL]: {
    description: "Refer Dr., Cr. & Block Dr., Cr.",
    type: PostingRestrictionTypeEnum.ALL,
  },
  [PostingRestrictionCodeEnum.BLOCK_ALL_REFER_CREDIT]: {
    description: "Block Debit, Credit & Refer Credit",
    type: PostingRestrictionTypeEnum.ALL,
  },
  [PostingRestrictionCodeEnum.DEPOSITOR_NAME_REQUIRED]: {
    description: "Depositer Name Must Be Entered",
    type: PostingRestrictionTypeEnum.CREDIT,
  },
  [PostingRestrictionCodeEnum.BLOCK_CREDIT_DEPOSITOR_NAME]: {
    description: "Refer & Block Credit + Depositer Name",
    type: PostingRestrictionTypeEnum.CREDIT,
  },
  [PostingRestrictionCodeEnum.REFER_CR_BLOCK_DR_DEPOSITOR_NAME]: {
    description: "Refer Cr & Block Dr + Depositer Name",
    type: PostingRestrictionTypeEnum.ALL,
  },
  [PostingRestrictionCodeEnum.CUSTODY_PROFILE_UPDATE]: {
    description: "Custody Profile Update",
    type: PostingRestrictionTypeEnum.CREDIT,
  },
  [PostingRestrictionCodeEnum.CASH_DEPOSIT_WITH_VD]: {
    description: "Post Cash Deposit with Proper VD",
    type: PostingRestrictionTypeEnum.CREDIT,
  },
  [PostingRestrictionCodeEnum.TRADE_FINANCE_RESTRICTION]: {
    description: "Restricted for Trade Finance Dept",
    type: PostingRestrictionTypeEnum.ALL,
  },
  [PostingRestrictionCodeEnum.REFER_DR_CASH_DEPOSIT]: {
    description: "R Dr + Post Cash Deposit with",
    type: PostingRestrictionTypeEnum.ALL,
  },
  [PostingRestrictionCodeEnum.REFER_CR_CASH_DEPOSIT]: {
    description: "R Cr + Post Cash Deposit with",
    type: PostingRestrictionTypeEnum.CREDIT,
  },
  [PostingRestrictionCodeEnum.NOT_TO_BE_USED_1]: {
    description: "Not to be Used",
    type: PostingRestrictionTypeEnum.ALL,
  },
  [PostingRestrictionCodeEnum.HR_CREDIT_RESTRICTION]: {
    description: "HR Account Restriction Credit",
    type: PostingRestrictionTypeEnum.CREDIT,
  },
  [PostingRestrictionCodeEnum.NOT_TO_BE_USED_2]: {
    description: "Not to be Used",
    type: PostingRestrictionTypeEnum.ALL,
  },
  [PostingRestrictionCodeEnum.EXPIRED_KYC]: {
    description: "Refer Debit Expired KYC",
    type: PostingRestrictionTypeEnum.DEBIT,
  },
  [PostingRestrictionCodeEnum.PERSONAL_LOANS_DIRECT_SALES]: {
    description: "Personal Loans from Direct Sales",
    type: PostingRestrictionTypeEnum.DEBIT,
  },
  [PostingRestrictionCodeEnum.PERSONAL_LOANS_REFER_ALL]: {
    description: "Personal Loan Refer Debit & Credit",
    type: PostingRestrictionTypeEnum.ALL,
  },
  [PostingRestrictionCodeEnum.LEGAL_BLOCK_DEBIT]: {
    description: "Legal Block Debit",
    type: PostingRestrictionTypeEnum.DEBIT,
  },
  [PostingRestrictionCodeEnum.DUPLICATE_CUSTOMER_REFER]: {
    description: "Duplicate Customer Refer DR. & CR.",
    type: PostingRestrictionTypeEnum.ALL,
  },
  [PostingRestrictionCodeEnum.HR_ACCOUNT_RESTRICTIONS]: {
    description: "HR Account Restrictions C/D",
    type: PostingRestrictionTypeEnum.ALL,
  },
  [PostingRestrictionCodeEnum.AML_RESTRICTION]: {
    description: "RD Based on AML Decision",
    type: PostingRestrictionTypeEnum.DEBIT,
  },
  [PostingRestrictionCodeEnum.EASY_LITE_LIMIT_BREACH]: {
    description: "Refer Debit Easy Lite Limits Breach",
    type: PostingRestrictionTypeEnum.DEBIT,
  },
  [PostingRestrictionCodeEnum.SANCTION_BLOCK_DEBIT]: {
    description: "Sanctions Block Debit",
    type: PostingRestrictionTypeEnum.DEBIT,
  },
  [PostingRestrictionCodeEnum.EXPIRED_CR_REFER_DEBIT]: {
    description: "Refer Debit - Expired CR",
    type: PostingRestrictionTypeEnum.DEBIT,
  },
  [PostingRestrictionCodeEnum.CUSTOMER_DEAD]: {
    description: "No Postings. Customer Dead.",
    type: PostingRestrictionTypeEnum.ALL,
  },
  [PostingRestrictionCodeEnum.PENDING_CLOSURE]: {
    description: "Pending Closure",
    type: PostingRestrictionTypeEnum.ALL,
  },
  [PostingRestrictionCodeEnum.AUTO_CLOSURE]: {
    description: "Automatic Closing",
    type: PostingRestrictionTypeEnum.ALL,
  },
};
