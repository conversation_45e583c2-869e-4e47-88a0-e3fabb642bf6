import { invert, mapValues } from "lodash";
import { TdRenewalInstructionsEnum } from "../../enums/src/deposits.enums";

export const FccTdRenewalInstructionsMap: Record<
  TdRenewalInstructionsEnum,
  string
> = {
  [TdRenewalInstructionsEnum.CloseOnMaturity]:
    "Credit Principal and Interest to Account",
  [TdRenewalInstructionsEnum.PrincipleOnly]:
    "Auto Renewal of Principal and Credit Interest to Account",
  [TdRenewalInstructionsEnum.PrincipleAndInterest]:
    "Auto Renewal of Principal and Interest",
};

export const AppTdRenewalInstructionsMap: Record<string, string> = invert(
  FccTdRenewalInstructionsMap
);
