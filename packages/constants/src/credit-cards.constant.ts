import { Card400StatusCodeEnum, CardStatusEnum } from "@enums";

export const CreditCardStatusMap: Record<
  Card400StatusCodeEnum,
  CardStatusEnum
> = {
  [Card400StatusCodeEnum.NOT_EMOBSSED]: CardStatusEnum.PendingIssuance,
  [Card400StatusCodeEnum.REPLACEMENT_PENDING_ACTIVATION]:
    CardStatusEnum.PendingActivation,
  [Card400StatusCodeEnum.RENEWAL_PENDING_ACTIVATION]:
    CardStatusEnum.PendingActivation,
  [Card400StatusCodeEnum.ACTIVATED_NEW_CARD_PIN_NOT_SET]:
    CardStatusEnum.PendingActivation,
  [Card400StatusCodeEnum.ACTIVE]: CardStatusEnum.Active,
  [Card400StatusCodeEnum.RENEWAL_FOR_VALID_CARD]: CardStatusEnum.Active,
  [Card400StatusCodeEnum.STOPPED]: CardStatusEnum.Stopped,
  [Card400StatusCodeEnum.STOPPED_FOR_RENEWAL_NUMBER_OF_ACTIVE_CARDS_NOT_DECREASED_BY_1_YET]:
    CardStatusEnum.Stopped,
  [Card400StatusCodeEnum.STOPPED_FOR_RENEWAL_AND_NUMBER_OF_ACTIVE_CARDS_DECREASED_BY_1]:
    CardStatusEnum.Stopped,
  [Card400StatusCodeEnum.FROZEN]: CardStatusEnum.Frozen,
  [Card400StatusCodeEnum.CLOSED]: CardStatusEnum.Closed,
  [Card400StatusCodeEnum.CLOSED11]: CardStatusEnum.Closed,
  [Card400StatusCodeEnum.CLOSED12]: CardStatusEnum.Closed,
  [Card400StatusCodeEnum.CLOSED99]: CardStatusEnum.Closed,
};
