import {
  FccTransfersTypesEnum,
  ProductStatusCodeEnum,
  SubTransactionStatusCodeEnum,
  TransactionStatusCodeEnum,
  TransactionStatusEnum,
  TransactionStatusFieldsEnum,
} from "@enums";

export const TransactionSubStatusMap: Record<
  SubTransactionStatusCodeEnum,
  string
> = {
  [SubTransactionStatusCodeEnum.Unknown]: "unknown",
  [SubTransactionStatusCodeEnum.Draft]: "draft",
  [SubTransactionStatusCodeEnum.PendingVerify]: "pendingVerify",
  [SubTransactionStatusCodeEnum.PendingAuthorise]: "pendingAuthorise",
  [SubTransactionStatusCodeEnum.PendingSend]: "pendingSend",
  [SubTransactionStatusCodeEnum.Sent]: "sent",
  [SubTransactionStatusCodeEnum.PostDated]: "postDated",
  [SubTransactionStatusCodeEnum.Completed]: "completed",
  [SubTransactionStatusCodeEnum.Rejected]: "rejected",
  [SubTransactionStatusCodeEnum.Entry]: "entry",
  [SubTransactionStatusCodeEnum.InProcess]: "inProcess",
  [SubTransactionStatusCodeEnum.Recurring]: "recurring",
  [SubTransactionStatusCodeEnum.RecurringPending]: "recurringPending",
  [SubTransactionStatusCodeEnum.PostDatedPending]: "postDatedPending",
  [SubTransactionStatusCodeEnum.SentToTheBank]: "sentToTheBank",
  [SubTransactionStatusCodeEnum.SendingFailed]: "sendingFailed",
  [SubTransactionStatusCodeEnum.Cancelled]: "cancelled",
  [SubTransactionStatusCodeEnum.Undefined]: "undefined",
  [SubTransactionStatusCodeEnum.InProcessAgain]: "inProcess",
  [SubTransactionStatusCodeEnum.Returned]: "returned",
  [SubTransactionStatusCodeEnum.CustomerDeleted]: "customerDeleted",
  [SubTransactionStatusCodeEnum.ProcessFailed]: "processFailed",
  [SubTransactionStatusCodeEnum.Received]: "received",
  [SubTransactionStatusCodeEnum.ReceiveFailed]: "receiveFailed",
  [SubTransactionStatusCodeEnum.AwaitingBankConfirmation]:
    "awaitingBankConfirmation",
  [SubTransactionStatusCodeEnum.Failed]: "failed",
} as const;

export const TransactionStatusMap: Record<TransactionStatusCodeEnum, string> = {
  [TransactionStatusCodeEnum.Unknown]: "unknown",
  [TransactionStatusCodeEnum.Incomplete]: "incomplete",
  [TransactionStatusCodeEnum.Uncontrolled]: "uncontrolled",
  [TransactionStatusCodeEnum.Controlled]: "controlled",
  [TransactionStatusCodeEnum.Acknowledged]: "acknowledged",
  [TransactionStatusCodeEnum.IncompleteBank]: "incompleteBank",
  [TransactionStatusCodeEnum.UncontrolledBank]: "uncontrolledBank",
  [TransactionStatusCodeEnum.Error]: "error",
  [TransactionStatusCodeEnum.IncompleteSession]:
    "transactionIncompleteInSession",
  [TransactionStatusCodeEnum.Cancelled]: "cancelled",
  [TransactionStatusCodeEnum.Revise]: "revise",
  [TransactionStatusCodeEnum.Failed]: "failed",
  [TransactionStatusCodeEnum.BusinessValidated]: "businessValidated",
  [TransactionStatusCodeEnum.BusinessRejected]: "businessRejected",
  [TransactionStatusCodeEnum.FinancialRejected]: "financialRejected",
  [TransactionStatusCodeEnum.CheckerReturned]: "checkerReturned",
  [TransactionStatusCodeEnum.CheckerReturnAcknowledged]:
    "checkerReturnAcknowledged",
  [TransactionStatusCodeEnum.MakerDraftAcknowledged]: "makerDraftAcknowledged",
} as const;

/**
 * Unified status to FCC codes mapping for filtering
 * Based on FCC status codes and Excel sheet
 *
 * Uncontrolled Pending Verify                --> 02	02  --
 * Uncontrolled Pending Authorise             --> 02  03  --
 *
 * Incomplete Draft (Exclude)                 --> 01	01	02
 * Incomplete Returned (RejectedCompany)      --> 01	18	--
 *
 * submitted sent                             --> 03  05  02
 * Pending                                    --> 03  05  02
 * Acknowledged In Progress                   --> 04	05	18
 * Uncontrolled Bank (TODO: add in inProgress)--> 06  05  01
 *
 * Acknowledged Not Processed (RejectedBank)  --> 04	05	01
 *
 * Acknowledged New                           --> 04	05	03
 */

export const TransactionStatusFilterMap: Partial<
  Record<
    TransactionStatusEnum,
    {
      [TransactionStatusFieldsEnum.TnxStatCode]?: TransactionStatusCodeEnum[];
      [TransactionStatusFieldsEnum.SubTnxStatCode]?: SubTransactionStatusCodeEnum[];
      [TransactionStatusFieldsEnum.ProdStatCode]?: ProductStatusCodeEnum[];
    }
  >
> = {
  [TransactionStatusEnum.PendingVerification]: {
    [TransactionStatusFieldsEnum.TnxStatCode]: [
      TransactionStatusCodeEnum.Uncontrolled,
    ],
    [TransactionStatusFieldsEnum.SubTnxStatCode]: [
      SubTransactionStatusCodeEnum.PendingVerify,
    ],
  },

  [TransactionStatusEnum.PendingAuthorization]: {
    [TransactionStatusFieldsEnum.TnxStatCode]: [
      TransactionStatusCodeEnum.Uncontrolled,
    ],
    [TransactionStatusFieldsEnum.SubTnxStatCode]: [
      SubTransactionStatusCodeEnum.PendingAuthorise,
    ],
  },

  [TransactionStatusEnum.RejectedCompany]: {
    [TransactionStatusFieldsEnum.TnxStatCode]: [
      TransactionStatusCodeEnum.Incomplete,
    ],
    [TransactionStatusFieldsEnum.SubTnxStatCode]: [
      SubTransactionStatusCodeEnum.Returned,
    ],
  },

  [TransactionStatusEnum.RejectedBank]: {
    [TransactionStatusFieldsEnum.TnxStatCode]: [
      TransactionStatusCodeEnum.Acknowledged,
    ],
    [TransactionStatusFieldsEnum.SubTnxStatCode]: [
      SubTransactionStatusCodeEnum.Sent,
    ],
    [TransactionStatusFieldsEnum.ProdStatCode]: [
      ProductStatusCodeEnum.Rejected,
    ],
  },

  [TransactionStatusEnum.InProgress]: {
    [TransactionStatusFieldsEnum.TnxStatCode]: [
      TransactionStatusCodeEnum.Acknowledged,
      TransactionStatusCodeEnum.Controlled,
    ],
    [TransactionStatusFieldsEnum.SubTnxStatCode]: [
      SubTransactionStatusCodeEnum.Sent,
    ],
    [TransactionStatusFieldsEnum.ProdStatCode]: [
      ProductStatusCodeEnum.InProgress,
      ProductStatusCodeEnum.Pending,
    ],
  },

  [TransactionStatusEnum.Approved]: {
    [TransactionStatusFieldsEnum.TnxStatCode]: [
      TransactionStatusCodeEnum.Acknowledged,
    ],
    [TransactionStatusFieldsEnum.SubTnxStatCode]: [
      SubTransactionStatusCodeEnum.Sent,
    ],
    [TransactionStatusFieldsEnum.ProdStatCode]: [ProductStatusCodeEnum.New],
  },
} as const;
