import { ErrorCodeEnum, FccErrorCodeEnum } from "@enums";

export const FccErrorCodeMap: Record<FccErrorCodeEnum, ErrorCodeEnum> = {
  //Common
  [FccErrorCodeEnum.AUTHENTICATION_FAILED]: ErrorCodeEnum.WrongOtp,
  [FccErrorCodeEnum.REAUTHENTICATION_FAILED]: ErrorCodeEnum.WrongOtp,
  [FccErrorCodeEnum.AUTHORISATION_FAILURE]: ErrorCodeEnum.UnauthorizedAccess,
  [FccErrorCodeEnum.REAUTH_REQUIRED]: ErrorCodeEnum.OtpRequired,
  [FccErrorCodeEnum.REAUTH_MODE_UNDEFINED]: ErrorCodeEnum.OtpReauthRequired,
  [FccErrorCodeEnum.TRANSACTION_NOT_FOUND]: ErrorCodeEnum.NotFound,
  [FccErrorCodeEnum.ACTIVE_TRANSACTIONS_EXISTS]:
    ErrorCodeEnum.ActiveTransactionsExists,
  [FccErrorCodeEnum.INVALID_PERMISSION]: ErrorCodeEnum.InvalidPermission,
  [FccErrorCodeEnum.REAUTH_NOT_REQUIRED]: ErrorCodeEnum.OtpNotRequired,
  [FccErrorCodeEnum.GET_DETAILS_EVENTID_INVALID]: ErrorCodeEnum.InvalidEventid,
  [FccErrorCodeEnum.INVALID_ACCOUNT_ID]: ErrorCodeEnum.ValidationError,

  //IPN
  [FccErrorCodeEnum.IPN_VALIDATION_ERROR]: ErrorCodeEnum.ValidationError,
  [FccErrorCodeEnum.IPN_BENEFICIARY_NOT_IPN]:
    ErrorCodeEnum.IpnBeneficiaryNotIpn,
  [FccErrorCodeEnum.IPN_ACCOUNT_RESTRICTED]: ErrorCodeEnum.IpnAccountRestricted,
  [FccErrorCodeEnum.IPN_INSUFFICIENT_FUNDS]: ErrorCodeEnum.IpnInsufficientFunds,
  [FccErrorCodeEnum.IPN_TRANSACTION_NOT_FOUND]:
    ErrorCodeEnum.IpnTransactionNotFound,
  [FccErrorCodeEnum.IPN_INVALID_STATUS]: ErrorCodeEnum.IpnInvalidStatus,
  [FccErrorCodeEnum.IPN_PERMISSION_DENIED]: ErrorCodeEnum.InvalidPermission,
  [FccErrorCodeEnum.IPN_SIMULATION_FAILED]: ErrorCodeEnum.IpnSimulationFailed,
  [FccErrorCodeEnum.IPN_PAYMENT_FAILED]: ErrorCodeEnum.IpnPaymentFailed,
  [FccErrorCodeEnum.IPN_AUTHENTICATION_FAILED]:
    ErrorCodeEnum.IpnAuthenticationFailed,
  [FccErrorCodeEnum.IPN_OTP_VALIDATION_FAILED]: ErrorCodeEnum.WrongOtp,
  [FccErrorCodeEnum.IPN_TRANSACTION_FAILED]: ErrorCodeEnum.IpnTransactionFailed,
  [FccErrorCodeEnum.IPN_OVERRIDE_REQUIRED]: ErrorCodeEnum.IpnOverrideRequired,
  [FccErrorCodeEnum.IPN_SYSTEM_ERROR]: ErrorCodeEnum.IpnSystemError,
  [FccErrorCodeEnum.IPN_BENEFICIARY_UPDATE_FAILED]:
    ErrorCodeEnum.IpnBeneficiaryUpdateFailed,
  [FccErrorCodeEnum.IPN_BENEFICIARY_DELETION_FAILED]:
    ErrorCodeEnum.IpnBeneficiaryDeletionFailed,
};
