import {
  AccountsListContextEnum,
  IpnClearingSystems,
  IpnPaymentMethods,
} from "@enums";

export const AccountsListContextMap: Record<AccountsListContextEnum, string> = {
  [AccountsListContextEnum.OWN_TRANSFERS]: "FT:INTERNAL-TRANSFER",
  [AccountsListContextEnum.LOCAL_TRANSFERS]: "FT:THIRD-PARTY-TRANSFER",
  [AccountsListContextEnum.DOMESTIC_TRANSFERS]: "FT:MT103",
  [AccountsListContextEnum.INTERNATIONAL_TRANSFERS]: "FT:MT103",
  [AccountsListContextEnum.IPN_TRANSFERS]: "FT:IPN",
  [AccountsListContextEnum.SECURE_MAIL]: "SECURE-MAIL:ULOAD",
  [AccountsListContextEnum.CREDIT_CARD]: "FT:CRD",
  [AccountsListContextEnum.TERM_DEPOSIT]: "TD:CSTD",
};

/**
 * Maps IPN clearing systems to their corresponding payment methods
 * Based on below mapping shared by COE:
 * - ACCOUNT: IPA, BANK_ACCOUNT, IBAN
 * - WALLET: IMWALL
 * - CARD: ICARD
 * - MOBILEACCOUNT: IMNUM
 */
export const IpnPaymentMethodMapping: Record<
  IpnClearingSystems,
  IpnPaymentMethods
> = {
  [IpnClearingSystems.IIPA]: IpnPaymentMethods.ACCOUNT,
  [IpnClearingSystems.BANK_ACCOUNT]: IpnPaymentMethods.ACCOUNT,
  [IpnClearingSystems.IBAN]: IpnPaymentMethods.ACCOUNT,
  [IpnClearingSystems.IMWALL]: IpnPaymentMethods.WALLET,
  [IpnClearingSystems.ICARD]: IpnPaymentMethods.CARD,
  [IpnClearingSystems.IMNUM]: IpnPaymentMethods.MOBILEACCOUNT,
};

export const IpnDefaults = {
  COMMISSION_CODE: "DEBIT PLUS CHARGES",
  COMMISSION_FOR: "SENDER",
  COMMISSION_TYPE: "IPNCORFEE",
  ORDERING_CUSTOMER_BANK_DEFAULT: "7",
  TRANSFER_TYPE: "ACP8",
  CURRENCY: "EGP",
  FT_CUR_CODE: "EGP",
  APPLICANT_ACT_CUR_CODE: "EGP",
} as const;

export const IpnConstants = {
  // Product details
  PRODUCT: {
    CODE: "FT",
    SUB_PRODUCT_CODE: "IPN",
    PRODUCT_TYPE: "IPN",
    TNX_TYPE: "01",
  },

  // Bank details
  BANK: {
    ISSUING_BANK_NAME: "Commercial International Bank",
    ISSUING_BANK_ABBV_NAME: "CIB",
    BRANCH_CODE: "00001",
  },

  // Pre-approval
  APPROVAL: {
    PRE_APPROVED_STATUS: "Y",
    PRE_APPROVED: "Y",
    APPLICANT_ACT_PAB: "Y",
  },

  REAUTH: {
    PERFORM: "Y",
  },

  DEFAULTS: {
    ENTITY: "",
    BENEFICIARY_CLEARING_SYSTEM_ID: "",
    CLEARING_SYSTEM_ID: "",
  },
} as const;
