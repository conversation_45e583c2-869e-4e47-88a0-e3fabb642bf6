//App error codes
export enum ErrorCodeEnum {
  UnknownError = "unknownError",
  WrongOtp = "wrongOtp",
  UnauthorizedAccess = "unauthorizedAccess",
  OtpRequired = "otpRequired",
  OtpNotRequired = "otpNotRequired",
  OtpReauthRequired = "otpReauthRequired",
  NotFound = "notFound",
  ActiveTransactionsExists = "activeTransactionsExists",
  SessionTerminated = "sessionTerminated",
  InvalidPermission = "invalidPermission",
  NotEligible = "notEligible",
  InvalidEventid = "invalidEventid",
  RequestRejected = "requestRejected",
  EndOfDayRunning = "endOfDayRunning",
  ValidationError = "validationError",

  //Accounts
  ActiveAccountExists = "activeAccountExists",

  //Ipn
  IpnBeneficiaryNotIpn = "ipnBeneficiaryNotIpn",
  IpnAccountRestricted = "ipnAccountRestricted",
  IpnInsufficientFunds = "ipnInsufficientFunds",
  IpnTransactionNotFound = "ipnTransactionNotFound",
  IpnInvalidStatus = "ipnInvalidStatus",
  IpnSimulationFailed = "ipnSimulationFailed",
  IpnPaymentFailed = "ipnPaymentFailed",
  IpnAuthenticationFailed = "ipnAuthenticationFailed",
  IpnOtpValidationFailed = "ipnOtpValidationFailed",
  IpnTransactionFailed = "ipnTransactionFailed",
  IpnOverrideRequired = "ipnOverrideRequired",
  IpnSystemError = "ipnSystemError",
  IpnBeneficiaryUpdateFailed = "ipnBeneficiaryUpdateFailed",
  IpnBeneficiaryDeletionFailed = "ipnBeneficiaryDeletionFailed",
  //Deposits
  LessThanMinimumBookingAmount = "lessThanMinimumBookingAmount",
  LessThanMinimumRedemptionAmount = "lessThanMinimumRedemptionAmount",
  MoreThanDepositAmount = "moreThanDepositAmount",
  IneligibleRedemptionAccount = "ineligibleRedemptionAccount",
  FullDepositAmountBlocked = "fullDepositAmountBlocked",
}

//FCC error codes
export enum FccErrorCodeEnum {
  AUTHENTICATION_FAILED = "AUTHENTICATION_FAILED",
  REAUTHENTICATION_FAILED = "Reauthentication Failed",
  AUTHORISATION_FAILURE = "AUTHORISATION_FAILURE",
  REAUTH_REQUIRED = "REAUTH_REQUIRED",
  REAUTH_MODE_UNDEFINED = "REAUTH_MODE_UNDEFINED",
  TRANSACTION_NOT_FOUND = "ERROR_ACTION_TRANSACTION_NOT_FOUND",
  ACTIVE_TRANSACTIONS_EXISTS = "DATA_MAINTENANCE_B_ERROR_002",
  INVALID_PERMISSION = "INVALID_PERMISSION",
  REAUTH_NOT_REQUIRED = "REAUTH_NOT_REQUIRED_FORNOREAUTH",
  GET_DETAILS_EVENTID_INVALID = "GET_DETAILS_EVENTID_INVALID",
  INVALID_ACCOUNT_ID = "INVALID_ACCOUNT_ID",
  //INVALID_INPUT_PARAMETER = "INVALID_INPUT_PARAMETER", //no need for this (it will break the fcc mapping logic)

  //IPN
  IPN_VALIDATION_ERROR = "IPN001",
  IPN_BENEFICIARY_NOT_IPN = "IPN003",
  IPN_ACCOUNT_RESTRICTED = "IPN004",
  IPN_INSUFFICIENT_FUNDS = "IPN005",
  IPN_TRANSACTION_NOT_FOUND = "IPN008",
  IPN_INVALID_STATUS = "IPN009",
  IPN_PERMISSION_DENIED = "IPN012",
  IPN_SIMULATION_FAILED = "IPN022",
  IPN_PAYMENT_FAILED = "IPN023",
  IPN_AUTHENTICATION_FAILED = "IPN026",
  IPN_OTP_VALIDATION_FAILED = "IPN027",
  IPN_TRANSACTION_FAILED = "IPN028",
  IPN_OVERRIDE_REQUIRED = "IPN029",
  IPN_SYSTEM_ERROR = "IPN030",
  IPN_BENEFICIARY_UPDATE_FAILED = "IPN031",
  IPN_BENEFICIARY_DELETION_FAILED = "IPN032",
}
