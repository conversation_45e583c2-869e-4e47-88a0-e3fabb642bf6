export enum RequestModuleEnum {
  ESB = "esb",
  FCC = "fcc",
  CIBEG = "cibeg",
  ESTATEMENT = "estatement",
}

export enum FccRequestsEnum {
  CHANGE_LANGUAGE = "change-language",
  CHANGE_CURRENCY = "change-currency",
  CHANGE_DEFAULT_ACCOUNT = "change-default-account",

  MY_PENDING_TRANSACTIONS = "my-pending-transactions",
  ALL_TRANSACTIONS = "all-transactions",
  REJECT_TRANSACTION = "reject-transaction",
  APPROVE_TRANSACTION = "approve-transaction",
  TRANSACTION_DETAILS = "transaction-details",
  TRANSACTION_DETAILS_SE = "transaction-details-se",
  TRANSACTION_JOURNEY_DETAILS = "transaction-journey",
  TRANSACTION_HISTORY = "transaction-history",

  USER_PERMISSIONS = "user-permissions",
  USER_DETAILS = "user-details",
  USER_REAUTHENTICATION_TYPE = "user-reauthentication-type",

  USERS_LIST = "users-list",

  UPLOAD_ATTACHMENT = "upload-attachment",
  DELETE_ATTACHMENT = "delete-attachment",
  GET_ATTACHMENT_LIST = "get-attachment-list",
  DOWNLOAD_ATTACHMENT = "download-attachment",

  UPLOAD_DOCUMENT = "upload-document",

  CUT_OFF_TIME = "cut-off-time",

  BANKS_LIST = "banks-list",
  GENERIC_AUTH_TYPE = "generic-auth-type",
  CONFIGURATION_DETAILS = "get-configuration-details",

  INTERNAL_TRANSFER_INITIATE = "internal-transfer-initiate",
  INTERNAL_TRANSFER_SUBMIT = "internal-transfer-submit",
  LOCAL_TRANSFER_INITIATE = "tpt-transfer-initiate",
  LOCAL_TRANSFER_SUBMIT = "tpt-transfer-submit",

  SWIFT_TRANSFER_INITIATE = "swift-transfer-initiate",
  SWIFT_TRANSFER_SUBMIT = "swift-transfer-submit",

  IPN_TRANSFER_INITIATE = "ipn-transfer-initiate",
  IPN_TRANSFER_SUBMIT = "ipn-transfer-submit",
  IPN_TRANSFER_SIMULATE = "ipn-transfer-simulate",
  IPN_ACCOUNTS_LIST = "ipn-accounts-list",

  CREDIT_CARD_SETTLEMENT_INITIATE = "credit-card-settlement-initiate",
  CREDIT_CARD_SETTLEMENT_SUBMIT = "credit-card-settlement-submit",

  TRANSFERS_BENEFICIARIES_LIST = "transfers-beneficiaries-list",
  TRANSFERS_BENEFICIARIES_DELETE = "transfers-beneficiaries-delete",
  TRANSFERS_BENEFICIARIES_CREATE_TPT = "transfers-beneficiaries-create-tpt",
  TRANSFERS_BENEFICIARIES_UPDATE_TPT = "transfers-beneficiaries-update-tpt",
  TRANSFERS_BENEFICIARIES_CREATE_MT103 = "transfers-beneficiaries-create-mt103",
  TRANSFERS_BENEFICIARIES_UPDATE_MT103 = "transfers-beneficiaries-update-mt103",
  TRANSFERS_BENEFICIARIES_CREATE_IPN = "transfers-beneficiaries-create-ipn",
  TRANSFERS_BENEFICIARIES_UPDATE_IPN = "transfers-beneficiaries-update-ipn",
  TRANSFERS_BENEFICIARIES_LIST_IPN = "transfers-beneficiaries-list-ipn",
  TRANSFERS_BENEFICIARIES_DELETE_IPN = "transfers-beneficiaries-delete-ipn",

  TERM_DEPOSIT_INITIATE = "term-deposit-initiate",
  TERM_DEPOSIT_SUBMIT = "term-deposit-submit",
  TERM_DEPOSIT_DETAILS = "term-deposit-details",
  TERM_DEPOSIT_LIST = "term-deposit-list",

  DEPOSITS_MAP = "deposits-map",

  COUNTRY_LIST = "country-list",
  CURRENCY_LIST = "currency-list",

  BANKS_SWIFT_DETAILS = "swift-details",
  CLEARING_SYSTEM = "clearing-system",

  ACCOUNTS_LIST = "accounts-list",
  DETAILED_ACCOUNTS_LIST = "detailed-accounts-list",
  ACCOUNTS_HOME_SUMMARY = "accounts-home-summary",
  ACCOUNTS_BALANCE_SUMMARY = "accounts-balance-summary",
  ACCOUNT_STATEMENT = "account-statement",

  ACCOUNT_DETAILS = "account-details",

  MUTUAL_FUND_SUMMARY = "mutual-fund-summary",

  LOANS_SUMMARY = "loans-summary",
  LOANS_LIST = "loans-list",
  LOAN_DETAILS = "loan-details",

  CREDIT_CARDS_SUMMARY = "credit-cards-summary",
  CREDIT_CARDS_LIST = "credit-cards-list",

  FX_CONVERT = "fx-convert",
  FX_RATE = "fx-rate",

  SECURE_MAIL_INITIATE = "secure-mail-initiate",
  SECURE_MAIL_SUBMIT = "secure-mail-submit",
  SECURE_MAIL_REJECTED_LIST = "secure-mail-rejected-list",
  SECURE_MAIL_APPROVED_LIST = "secure-mail-approved-list",

  ACCOUNT_MOVEMENTS_DOWNLOAD = "account-movements-download",
  ACCOUNT_MOVEMENTS_LIST = "account-movements-list",

  CREDIT_CARD_MOVEMENTS_LIST = "credit-card-movements-list",

  MOVEMENT_ADVICE_DOWNLOAD = "movement-advice-download",

  DOWNLOAD_TRANSACTION = "download-transaction",
}

export enum EsbRequestsEnum {
  CUSTOMER_DETAILS = "customer-details",

  ACCOUNT_DETAILS = "account-details",
  ACCOUNTS_LIST = "accounts-list",

  LINKED_DEBIT_CARDS = "linked-debit-cards",
  FOREX_RATE = "forex-rate",

  CREDIT_CARD_LIST = "credit-card-list",
  CREDIT_CARD_DETAILS = "credit-card-details",
  CREDIT_CARD_INSTALLMENTS = "credit-card-installments",

  SWIFT_CONFIRMATION = "swift-confirmation",
  DEPOSIT_LIST = "deposit-list",
  DEPOSIT_DETAILS = "deposit-details",
  DEPOSIT_INTEREST_RATE = "deposit-interest-rate",
  DEPOSIT_SIMULATE_REDEMPTION = "deposit-simulate-redemption",

  LOANS_LIST = "loans-list",
  LOANS_DETAILS = "loan-details",

  IPN_BANK_LIST = "ipn-bank-list",
}

export enum CibegRequestsEnum {
  BRANCH_LOCATIONS = "branch-locations",
  BRANCH_LOCATION_DETAILS = "branch-location-details",
}

export enum EstatementRequestsEnum {
  ENCRYPTION = "encryption",
}
