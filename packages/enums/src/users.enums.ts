export enum UserSegmentEnum {
  Growth = "growth",
  FinancialInclusion = "financial_inclusion",
  BusinessBanking = "business_banking",
  Commercial = "commercial",
}

export enum UserRiskLevelEnum {
  LOW = "LOW",
  MEDIUM = "MEDIUM",
  HIGH = "HIGH",
}

export enum UserTypeCodeEnum {
  BadClient = "B", // Bad Client
  FatcaRecalcitrant = "FR", // FATCA Recalcitrant Customer
  UncooperativeCustomer = "U", // Uncooperative Customer
  UncooperativePAUB = "UP", // Uncooperative PAUB
  OffshoreEntity = "OS",
}

export enum BlockedSectorCodeEnum {
  NonProfitOrganization = "4200",
}
