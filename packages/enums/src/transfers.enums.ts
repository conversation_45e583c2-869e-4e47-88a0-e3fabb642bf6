export enum TransfersSubProductCodes {
  TPT = "TPT",
  MT103 = "MT103",
  IPN = "IPN",
}

export enum TransfersChargeOptionEnum {
  OUR = "OUR",
  BEN = "BEN",
  SHA = "SHA",
}

export enum FccTransfersTypesEnum {
  INTERNAL_TRANSFER = "INTERNAL-TRANSFER",
  THIRD_PARTY_TRANSFER = "THIRD-PARTY-TRANSFER",
  MT103 = "MT103",
  CRD = "CRD",
}

export enum AccountsListContextEnum {
  OWN_TRANSFERS = "own-transfers",
  LOCAL_TRANSFERS = "local-transfers",
  INTERNATIONAL_TRANSFERS = "international-transfers",
  DOMESTIC_TRANSFERS = "domestic-transfers",
  IPN_TRANSFERS = "ipn-transfers",
  SECURE_MAIL = "secure-mail",
  CREDIT_CARD = "credit-card",
  TERM_DEPOSIT = "term-deposit",
}

export enum BicClearingSystems {
  SWIFT = "SWIFT",
  SORTCODE = "SORTCODE",
  FEDWIRE = "FEDWIRE",
  CHIPS = "CHIPS",
  BLZ = "BLZ",
}

export enum IpnClearingSystems {
  IMNUM = "IMNUM",
  IMWALL = "IMWALL",
  IIPA = "IIPA",
  BANK_ACCOUNT = "BANK_ACCOUNT",
  IBAN = "IBAN",
  ICARD = "ICARD",
}

export enum TransfersDynamicListEnum {
  IPN_TRANSFER_REASONS = "IPN_TRANSFER_REASONS",
}

export enum IpnPaymentMethods {
  ACCOUNT = "ACCOUNT",
  WALLET = "WALLET",
  CARD = "CARD",
  MOBILEACCOUNT = "MOBILEACCOUNT",
}

export enum IpnCommissionTypes {
  IPNCORFEE = "IPNCORFEE",
  IPNTRANSFEE = "IPNTRANSFEE",
}

export enum IpnCommissionFor {
  SENDER = "SENDER",
  BENEFICIARY = "BENEFICIARY",
}
