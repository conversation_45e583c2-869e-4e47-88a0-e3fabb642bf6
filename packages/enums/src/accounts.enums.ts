export enum AccountType {
    CURRENT = 'Current Account',
    SAVINGS = 'Savings Account',
    LOAN = 'Loan Account',
    TERMDEPOSIT = 'Term Deposit Account',
    CREDITCARD = 'Credit Card Account',
    INQUIRY = 'Inquiry Account Retail / Ledger',
  }


export enum DetailedAccountTypeWithBalanceQueryEnum {
    CURRENT = 'CURRENT',
    OTHER = 'OTHER',
    TERM_DEPOSIT = 'TERMDEPOSIT',
    LOAN = 'LOAN',
}