export enum UserLanguage {
  "en-US" = "en-US",
  "ar-EG" = "ar-EG",
}

export enum CacheScope {
  GLOBAL = "global",
  USER = "user",
}

export enum CacheTTL {
  OneMinute = 60,
  FiveMinutes = 5 * 60,
  TenMinutes = 10 * 60,
  ThirtyMinutes = 30 * 60,
  OneHour = 60 * 60,
  SixHours = 6 * 60 * 60,
  TwelveHours = 12 * 60 * 60,
  OneDay = 24 * 60 * 60,
  TwoDays = 2 * 24 * 60 * 60,
  OneWeek = 7 * 24 * 60 * 60,
}

export enum ResponseTypeEnum {
  ArrayBuffer = "arraybuffer",
  Blob = "blob",
  Document = "document",
  Json = "json",
  Text = "text",
  Stream = "stream",
  FormData = "formdata",
}

export enum EnvironmentEnum {
  Production = "production",
  Development = "development",
  Test = "test",
  Local = "local",
}

export enum CutOffTimeContextEnum {
  TERM_DEPOSIT = "term-deposit",
}

export enum IbanTypeEnum {
  Domestic = "domestic",
  International = "international",
}
