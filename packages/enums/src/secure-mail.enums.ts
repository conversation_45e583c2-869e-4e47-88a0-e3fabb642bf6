export enum SecureMailTypesEnum {
  SUPPORTING_DOCUMENTS = "Supporting Documents",
  // EKYC_FORM_UPLOAD = "EKYC Form Upload",
  // ADD_NEW_CUSTOMER_NUMBER = "Add New Customer Number",
  STOP_MI_PAYMENTS = "Stop mi Payments",
  ADD_NEW_PERMISSION = "Add New Permission",
  // TOKEN_REQUEST = "Token Request",
  DELETE_USERS = "Delete Users",
  // IMPORT_COLLECTION = "Import Collection",
  // EXPORT_COLLECTION = "Export Collection",
  // BANKERS_GUARANTEE = "Banker's Guarantee",
  // SHIPPING_GUARANTEE = "Shipping Guarantee",
  // EXPORT_LETTER_OF_CREDIT = "Export Letter of Credit",
  // IMPORT_LETTER_OF_CREDIT = "Import Letter of Credit",
  // ADDING_NEW_BENEFICIARY = "Adding New Beneficiary",
  // ONLINE_BOOKING_ESCF = "Online Booking / eSCF",

  //Refactor ASSIGN_ACCOUNT to be ASSIGN_NEW_PRODUCT
  //ASSIGN_ACCOUNT = "Assign Account",
  ASSIGN_NEW_PRODUCT = "Assign New Product",

  // MERGE_PROFILE = "Merge profile",
  CANCEL_BANK_DRAFT = "Cancel bank draft",
  CPS_CHARGE_BACK_REQUEST = "CPS charge back request",
  CPS_AMEND_ADD_SERVICE = "CPS Amend / Add service",
  // TD_BREAK = "TD break",
  // ISSUE_CERTIFICATE_SHIPPING = "Issue Certificate ( Shipping )",
  CANCEL_SECURE_EMAIL = "Cancel Secure email",
  CHANGE_USER_PRIVILEGES = "Change user privileges",
  // AMEND_BENEFICIARY = "Amend Beneficiary",
  ADD_NEW_USER = "Add New User",

  OTHER_PORTALS_RELATED_REQUEST = "Other Portals Related Request",
  REQUEST_BANK_DRAFT = "Request Bank Draft",
  AMEND_USER_DATA = "Amend User Data",

  OPEN_NEW_ACCOUNT = "Open a new Account",
  DISPUTE_CREDIT_CARD = "Dispute Credit Card",
  MANAGE_DIRECT_DEBIT = "Manage Direct Debit",

  REDEEM_TD = "TD Break",
}

export enum SecureMailDynamicListEnum {
  USER_PRIVILEGES = "USER_PRIVILEGES",
  PORTAL_TYPES = "PORTAL_TYPES",
  PRODUCT_TYPES = "PRODUCT_TYPES",
  SECURE_MAIL_TYPES = "SECURE_MAIL_TYPES",
  TOKEN_TYPES = "TOKEN_TYPES",
  ACCESS_PERMISSIONS = "ACCESS_PERMISSIONS",
  TD_REDEMPTION_TYPES = "TD_REDEMPTION_TYPES",
}

export enum SecureMailFieldTypeEnum {
  String = "string",
  Number = "number",
  Boolean = "boolean",

  //Custom types (be careful)
  Currency = "currency",
  AccountProduct = "accountProduct",
}

export enum SecureMailFieldNamesEnum {
  DeliveryBranchCity = "deliveryBranchCity",
  DeliveryBranch = "deliveryBranch",

  Currency = "currency",

  AccountProductCode = "accountProductCode",
  SignatureAccountNumber = "signatureAccountNumber",

  DepositId = "depositId",
  AccountId = "accountId",
  RedemptionAccountNumber = "redemptionAccountNumber",
  RedemptionAmount = "redemptionAmount",
  RedemptionType = "redemptionType",
}
