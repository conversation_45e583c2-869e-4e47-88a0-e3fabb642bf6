export enum CardTypeEnum {
  CREDIT_CARD = "CreditCard",
  DEBIT_CARD = "DebitCard",
  PREPAID_CARD = "PrepaidCard",
}

export enum Card400StatusCodeEnum {
  NOT_EMOBSSED = 0,
  ACTIVE = 1,
  REPLACEMENT_PENDING_ACTIVATION = 5,
  RENEWAL_PENDING_ACTIVATION = 6,
  STOPPED = 9,
  FROZEN = 19,
  CLOSED = 10,
  CLOSED11 = 11,
  CLOSED12 = 12,
  CLOSED99 = 99,
  RENEWAL_FOR_VALID_CARD = 30,
  ACTIVATED_NEW_CARD_PIN_NOT_SET = 8,
  STOPPED_FOR_RENEWAL_NUMBER_OF_ACTIVE_CARDS_NOT_DECREASED_BY_1_YET = 2,
  STOPPED_FOR_RENEWAL_AND_NUMBER_OF_ACTIVE_CARDS_DECREASED_BY_1 = 3,
}

//for Credit & Debit
export enum CardStatusEnum {
  Unknown = "unknown",
  PendingIssuance = "pendingIssuance",
  PendingActivation = "pendingActivation",
  Active = "active",
  Inactive = "inactive",
  Stopped = "stopped",
  Frozen = "frozen",
  Closed = "closed",
}

//for Credit & Debit
export enum CardOperationEnum {
  List = "list",
  Freeze = "freezeCard",
  Unfreeze = "unfreezeCard",
  Stop = "stopCard",
  Close = "closeCard",
  DisputeTransaction = "disputeCardTransaction",
  ChangeLimit = "changeCardLimit",
  ResetPin = "resetCardPin",
  Active = "activeCard",
}

export enum InstallmentsCampaignStatusEnum {
  EarlySettlement = "1",
  CancelledOrRefunded = "2",
}
