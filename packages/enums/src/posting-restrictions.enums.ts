export enum PostingRestrictionTypeEnum {
  DEBIT = "DEBIT",
  CREDIT = "CREDIT",
  ALL = "ALL",
}

export enum PostingRestrictionCodeEnum {
  POST_NO_DEBITS = 1,
  POST_NO_CREDITS = 2,
  POST_NO_ENTRIES = 3,
  REFER_CREDITS = 4,
  REFER_DEBITS = 5,
  REFER = 6,
  REFERRAL_LIST = 7,
  REFERRAL_LIST_CR = 8,
  REFERRAL_LIST_DR = 9,
  POST_CREDIT_TO_SAVINGS = 10,
  REFER_CREDIT_BLOCK_DEBIT = 11,
  REFER_DEBIT_CREDIT_BLOCK_DEBIT = 12,
  BLOCK_DEBIT = 13,
  BL<PERSON>K_CREDIT = 14,
  REFER_BLOCK_DEBIT = 15,
  BAD_DEBT = 16,
  REFER_DEBIT_CREDIT_BLOCK_CREDIT = 17,
  BLOCK_DEBIT_CREDIT_REFER_DEBIT = 18,
  REFER_BLOCK_CREDIT = 19,
  BLOCK_DEBIT_CREDIT = 20,
  REFER_DEBIT_BLOCK_CREDIT = 21,
  REFER_BLOCK_ALL = 22,
  BL<PERSON>K_ALL_REFER_CREDIT = 23,
  DEPOSITOR_NAME_REQUIRED = 24,
  BLOCK_CREDIT_DEPOSITOR_NAME = 25,
  REFER_CR_BLOCK_DR_DEPOSITOR_NAME = 26,
  CUSTODY_PROFILE_UPDATE = 27,
  CASH_DEPOSIT_WITH_VD = 28,
  TRADE_FINANCE_RESTRICTION = 29,
  REFER_DR_CASH_DEPOSIT = 30,
  REFER_CR_CASH_DEPOSIT = 31,
  NOT_TO_BE_USED_1 = 32,
  HR_CREDIT_RESTRICTION = 33,
  NOT_TO_BE_USED_2 = 34,
  EXPIRED_KYC = 35,
  PERSONAL_LOANS_DIRECT_SALES = 36,
  PERSONAL_LOANS_REFER_ALL = 37,
  LEGAL_BLOCK_DEBIT = 38,
  DUPLICATE_CUSTOMER_REFER = 39,
  HR_ACCOUNT_RESTRICTIONS = 40,
  AML_RESTRICTION = 41,
  EASY_LITE_LIMIT_BREACH = 42,
  SANCTION_BLOCK_DEBIT = 43,
  EXPIRED_CR_REFER_DEBIT = 44,
  CUSTOMER_DEAD = 70,
  PENDING_CLOSURE = 80,
  AUTO_CLOSURE = 90,
}
