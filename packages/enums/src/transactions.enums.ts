export enum TransactionStatusEnum {
  InProgress = "inProgress",
  RejectedBank = "rejectedBank",
  RejectedCompany = "rejectedCompany",
  PendingAuthorization = "pendingAuthorization",
  PendingVerification = "pendingVerification",
  Approved = "approved",

  // Submitted = "submitted",
  //To Be Continued
}

//N015
export enum SubTransactionStatusCodeEnum {
  Unknown = "00",
  Draft = "01",
  PendingVerify = "02",
  PendingAuthorise = "03",
  PendingSend = "04",
  Sent = "05",
  PostDated = "06",
  Completed = "07",
  Rejected = "08",
  Entry = "09",
  InProcess = "10",
  Recurring = "11",
  RecurringPending = "12",
  PostDatedPending = "13",
  SentToTheBank = "14",
  SendingFailed = "15",
  Cancelled = "16",
  Undefined = "99",
  InProcessAgain = "17",
  Returned = "18",
  CustomerDeleted = "19",
  ProcessFailed = "21",
  Received = "22",
  ReceiveFailed = "23",
  AwaitingBankConfirmation = "24",
  Failed = "25",
}

//N004
export enum TransactionStatusCodeEnum {
  Unknown = "00",
  Incomplete = "01",
  Uncontrolled = "02",
  Controlled = "03",
  Acknowledged = "04",
  IncompleteBank = "05",
  UncontrolledBank = "06",
  Error = "07",
  IncompleteSession = "08",
  Cancelled = "09",
  Revise = "11",
  Failed = "12",
  BusinessValidated = "51",
  BusinessRejected = "52",
  FinancialRejected = "53",
  CheckerReturned = "54",
  CheckerReturnAcknowledged = "55",
  MakerDraftAcknowledged = "56",
}

//N005
export enum ProductStatusCodeEnum {
  Rejected = "01",
  Pending = "02",
  New = "03",
  Accepted = "04",
  Settled = "05",
  Cancelled = "06",
  Updated = "07",
  Amended = "08",
  Extended = "09",
  Purged = "10",
  Released = "11",
  Discrepant = "12",
  PartSettled = "13",
  PartSightPaid = "14",
  FullSightPaid = "15",
  VersionDone = "17",
  InProgress = "18",
  Tracer = "19",
  PendingConsent = "20",
  Utilized = "21",
  Expire = "22",
  Paid = "23",
  PartApproved = "25",
  BillClean = "26",
  ApprovedByConsent = "28",
  ReleaseAccepted = "29",
  DecreaseAccepted = "30",
  AmendmentAwaitingBeneficiaryApproval = "31",
  AmendmentRefused = "32",
  PaymentRefused = "33",
  GeneralRequest = "34",
  RecurringTransfer = "35",
  LastRecurringTransfer = "36",
  LastRecurringTransferRejected = "37",
  PostDatedPending = "38",
  RecurringPending = "39",
  Printed = "40",
  ProductRejected = "41",
  Expired = "42",
  RequestAcceptance = "44",
  Presented = "45",
  Eligible = "46",
  NotEligible = "47",
  InvoiceAccepted = "48",
  InvoiceRejected = "49",
  EarlyPaymentRequest = "50",
  Returned = "51",
  EarlyPaymentAck = "52",
  EarlyPaymentNack = "53",
  FinancingReq = "54",
  Resubmitted = "55",
  FinancingRefusal = "56",
  PostdatedRecurringInProgress = "57",
  PostdatedRejected = "58",
  RecurringNotLastTransferRejected = "59",
  EarlyPaymentRefusal = "60",
  Initial = "61",
  PartiallyMatched = "65",
  Established = "66",
  Proposed = "67",
  ReactivateRequested = "68",
  RequestPayment = "69",
  Registered = "70",
  Unregistered = "71",
  PoEstablished = "72",
  LcRegistered = "73",
  Match = "74",
  Mismatch = "75",
  Approved = "76",
  Closed = "77",
  WordingUnderReview = "78",
  FinalWording = "79",
  CancelAwaitingCounterpartyResponse = "81",
  CancelRefused = "82",
  WarrantyClaim = "84",
  ClaimSettlement = "85",
  ExtendPay = "86",
  AwaitingDocuments = "89",
  Active = "90",
  ActiveLinked = "91",
  Confirm = "92",
  Filled = "93",
  InterimExpired = "94",
  PendingFill = "95",
  ApprovedFinancingRequest = "96",
  ApprovedFinancingRefusal = "97",
  Provisional = "98",
  Other = "99",
  ClaimInProgress = "B0",
  GeneralClaimRequest = "B1",
  ClaimWithdrawn = "B2",
  InvalidClaimRejected = "B3",
  ClaimPending = "B4",
  AdviceOfRenewal = "B5",
  AdviceOfDiariesedAmendment = "B6",
  PeriodicChargeAdvise = "B7",
  ClaimClosed = "B8",
}

export enum TransactionApprovalStatusEnum {
  Unknown = "unknown",
  Rejected = "rejected",
  Approved = "approved",
}

export enum TransactionFieldGroupEnum {
  System = "system",
  Product = "product",
  Account = "account",
  Branch = "branch",
  BackOffice = "backoffice",
  Beneficiary = "beneficiary",
  Applicant = "applicant",
  Company = "company",
  Amount = "amount",
  Fx = "fx",
}

export enum TransactionStatusFieldsEnum {
  TnxStatCode = "tnxStatCode",
  SubTnxStatCode = "subTnxStatCode",
  ProdStatCode = "prodStatCode",
}
