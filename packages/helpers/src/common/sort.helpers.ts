import { DateTime } from "luxon";

export function sortByDate(
  array: any[],
  sort: "asc" | "desc" = "desc",
  datePropertyName: string
) {
  return array.sort((a, b) => {
    const dateA = DateTime.fromISO(a[datePropertyName]);
    const dateB = DateTime.fromISO(b[datePropertyName]);

    return sort === "asc"
      ? dateA.toMillis() - dateB.toMillis()
      : dateB.toMillis() - dateA.toMillis();
  });
}
