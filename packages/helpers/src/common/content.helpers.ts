import { TdRenewalInstructionsEnum } from "@enums";

export const pluralize = (str: string, count: number): string => {
  return Number(count) === 1 ? str : str + "s";
};

export const esbRenewalInstruction = (
  renewalOption: boolean,
  autoCapitalize: string
): TdRenewalInstructionsEnum => {
  if (!renewalOption && autoCapitalize === "N") {
    return TdRenewalInstructionsEnum.CloseOnMaturity;
  } else if (renewalOption && autoCapitalize === "Y") {
    return TdRenewalInstructionsEnum.PrincipleAndInterest;
  } else if (renewalOption && autoCapitalize === "N") {
    return TdRenewalInstructionsEnum.PrincipleOnly;
  }
};
