import { castArray, chunk, join } from "lodash";

export const jsonToUrlEncoded = (json: Record<string, any>): string => {
  const params = new URLSearchParams();
  for (const key in json) {
    if (json[key] !== undefined && json[key] !== null) {
      params.append(key, String(json[key]));
    }
  }
  return params.toString();
};

export const toJoinedString = (value: unknown, separator = "|") =>
  join(castArray(value), separator);

export const insertLineBreaks = (text: string, size: number = 35): string => {
  return chunk(text, size)
    .map((chars) => chars.join(""))
    .join("\n");
};

export const mapSortField = (
  sortField: string,
  sortOrder: string | number,
  fieldMap: Record<string, string>
): string => {
  const mappedField = fieldMap[sortField] ?? sortField;
  const prefix = sortOrder === "-1" || sortOrder === -1 ? "-" : "";
  return `${prefix}${mappedField}`;
};
