export const tenorFromString = (
  tenor: string,
  matcher: RegExp
): {
  value: number;
  unit: string;
  isVariable: boolean;
} => {
  const match = tenor.match(matcher);
  if (!match) {
    throw new Error(`Invalid format: ${tenor}`);
  }
  const rawUnit = match[2];
  return {
    value: parseFloat(match[1]),
    unit: normalizeTenorUnit(rawUnit),
    isVariable: rawUnit.toLowerCase().endsWith("v"),
  };
};

export const normalizeTenorUnit = (unit: string): string => {
  switch (unit) {
    case "D":
    case "DAY":
      return "day";
    case "W":
    case "WEEK":
      return "week";
    case "M":
    case "MONTH":
      return "month";
    case "Y":
    case "YEAR":
      return "year";
  }
};

export const loanTenorFromString = (tenor: string) =>
  tenorFromString(tenor, /^(\d+(?:\.\d+)?) ([a-zA-Z]+)$/);

export const depositTenorFromString = (tenor: string) =>
  tenorFromString(tenor, /^(\d+(?:\.\d+)?)([a-zA-Z]+)$/);
