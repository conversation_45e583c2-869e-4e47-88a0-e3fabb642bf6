export const isValidIBAN = (iban: string): boolean => {
  const countryCode = iban.slice(0, 2);
  const providedChecksum = parseInt(iban.slice(2, 4), 10);
  const bban = iban.slice(4);

  const rearranged = `${bban}${countryCode}00`;
  const numericString = convertToNumericString(rearranged);
  const remainder = mod97(numericString);

  return 98 - remainder === providedChecksum;
};

const convertToNumericString = (input: string): string =>
  input
    .split("")
    .map((char) => {
      const code = char.charCodeAt(0);
      return code >= 65 ? (code - 55).toString() : char;
    })
    .join("");

const mod97 = (input: string): number => {
  let remainder = input;

  while (remainder.length > 2) {
    const segment = remainder.slice(0, 6);
    const segmentValue = parseInt(segment, 10);

    if (isNaN(segmentValue)) return NaN;

    remainder =
      (segmentValue % 97).toString() + remainder.slice(segment.length);
  }

  return parseInt(remainder, 10) % 97;
};
