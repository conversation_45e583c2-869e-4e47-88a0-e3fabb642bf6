import { IpnClearingSystems, IpnPaymentMethods } from "@enums";
import { IpnPaymentMethodMapping } from "@constants";

/**
 * Gets payment method from clearing system
 * @param clearingSystem - IPN clearing system
 * @returns The corresponding payment method
 */
export const getIpnPaymentMethod = (
  clearingSystem: IpnClearingSystems
): IpnPaymentMethods => {
  return IpnPaymentMethodMapping[clearingSystem];
};
