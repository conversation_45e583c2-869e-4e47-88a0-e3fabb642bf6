import { DateTime } from "luxon";

export function applyAmountFilter(
  array: any[],
  fromAmount?: number,
  toAmount?: number,
  amountPropertyName?: string
): any[] {
  if (!isNaN(fromAmount!) && !isNaN(toAmount!) && toAmount! > 0) {
    return array.filter(
      (item) =>
        item[amountPropertyName] >= fromAmount! &&
        item[amountPropertyName] <= toAmount!
    );
  } else if (!isNaN(fromAmount!) && fromAmount! > 0) {
    return array.filter((item) => item[amountPropertyName] >= fromAmount!);
  }
  return array;
}

export function applyDateFilter(
  array: any[],
  fromDate?: string,
  toDate?: string,
  datePropertyName?: string
): any[] {
  const parsedFromDate = fromDate
    ? DateTime.fromFormat(fromDate, "dd/MM/yyyy")
    : null;

  const parsedToDate = toDate
    ? DateTime.fromFormat(toDate, "dd/MM/yyyy")
    : null;

  return array.filter((item) => {
    const startDate = DateTime.fromISO(item[datePropertyName]);

    return (
      (!parsedFromDate || startDate >= parsedFromDate) &&
      (!parsedToDate || startDate <= parsedToDate)
    );
  });
}

export function applySearchFilter(
  array: any[],
  search?: string,
  searchPropertyName?: string
): any[] {
  if (!search) return array;

  const searchTerm = search.toLowerCase();
  return array.filter((item) =>
    item[searchPropertyName]?.toLowerCase()?.includes(searchTerm)
  );
}
