import { FccFtGenericTnxResponseDto, FccSEGenericTnxResponseDto } from "@dtos";

export const getTransfersReauthInputs = (tnx: FccFtGenericTnxResponseDto) => {
  return {
    productCode: tnx.productCode,
    subProductCode: tnx.subProductCode,
    operation: "SUBMIT",
    refId: tnx.refId,
    tnxId: tnx.tnxId,
  };
};

export const getSecureMailReauthInputs = (tnx: FccSEGenericTnxResponseDto) => {
  return {
    productCode: tnx.productCode,
    operation: "SUBMIT",
    refId: tnx.refId,
    tnxId: tnx.tnxId,
  };
};
