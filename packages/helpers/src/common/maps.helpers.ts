export const getDistance = (
  lat1: number,
  lng1: number,
  lat2: number,
  lng2: number
) => {
  const toRad = (value: number) => (value * Math.PI) / 180;

  const R = 6371; // Earth radius in km
  const dLat = toRad(lat2 - lat1);
  const dLng = toRad(lng2 - lng1);
  const a =
    Math.sin(dLat / 2) ** 2 +
    Math.cos(toRad(lat1)) * Math.cos(toRad(lat2)) * Math.sin(dLng / 2) ** 2;

  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c; // distance in km
};

export const sortByDistance = (locations: Record<string, any>[], myLat: number, myLng: number) =>  locations
  .map((loc) => ({
    ...loc,
    distance: getDistance(myLat, myLng, loc.lat, loc.lng),
  }))
  .sort((a, b) => a.distance - b.distance);
