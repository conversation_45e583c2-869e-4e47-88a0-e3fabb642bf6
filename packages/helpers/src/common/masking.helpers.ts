export const maskAccountTitle = (input: string) => {
  return input
    .split(" ")
    .map((word) => {
      if (word.length === 0) return word; // Handle single-letter names
      return word[0] + "*".repeat(word.length - 1); // Mask rest
    })
    .join(" ");
};

export const maskCardNumber = (
  cardNumber: string,
  maskCharacter: string = "*"
): string => {
  let numericOnly = cardNumber;
  if (numericOnly.length >= 8) {
    const firstDigits = numericOnly.slice(0, 4); // Leave the first 4 digits as is
    const lastDigits = numericOnly.slice(-4); // Leave the last 4 digits as is
    const maskedPart = maskCharacter.repeat(4); // Always use exactly 8 mask characters
    return `${firstDigits} ${maskedPart} ${maskedPart} ${lastDigits}`;
  }

  return cardNumber;
};
