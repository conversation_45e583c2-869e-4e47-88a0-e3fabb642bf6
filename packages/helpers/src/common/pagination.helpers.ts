import { PaginationResponse } from "@types";

export function applyPaginationOnArray<T>(
  data: Array<T>,
  pagination: {
    page: number;
    limit: number;
  },
  returnPaginationObject = true
): PaginationResponse<T> | Array<T> {
  const isValidPage =
    typeof pagination?.page === "number" &&
    Number.isFinite(pagination.page) &&
    pagination.page > 0;
  const isValidLimit =
    typeof pagination?.limit === "number" &&
    Number.isFinite(pagination.limit) &&
    pagination.limit > 0;

  const page = isValidPage ? pagination.page : 1;
  const limit = isValidLimit ? pagination.limit : 20;
  const total = data.length;
  return returnPaginationObject
    ? {
        data: data.slice((page - 1) * limit, page * limit),
        pagination: {
          itemsPerPage: limit,
          totalItems: total,
          page,
          totalPages: Math.ceil(total / limit),
        },
      }
    : data.slice((page - 1) * limit, page * limit);
}
