import {
  ClassConstructor,
  plainToInstance,
  Transform,
} from "class-transformer";
import { Matches } from "class-validator";
import { get } from "lodash";
import { DateTime } from "luxon";
import { maskAccountTitle, maskCardNumber } from "./masking.helpers";
import { insertLineBreaks } from "./json.helpers";

export const ArrayTransformer = (
  keyField: string,
  valuePath: string,
  defaultValue = null
) =>
  Transform(({ value }) => {
    if (!Array.isArray(value)) return {};
    return value.reduce((acc, item) => {
      const key = item[keyField];
      const amount = get(item, valuePath, defaultValue);
      if (key) acc[key] = amount;
      return acc;
    }, {});
  });

export const StringConcatTransformer = (joinChar: string = " ") =>
  Transform(({ value }) =>
    Array.isArray(value)
      ? value.join(joinChar).replace(/\s+/g, " ").trim()
      : value?.toString().trim()
  );

export const MoneyTransformer = () => {
  return Transform(({ value }) => {
    if (typeof value === "string") {
      const cleaned = value.replace(/,/g, "");
      return cleaned === "" ? null : parseFloat(cleaned);
    }
    return value;
  });
};

export const FloatNumberTransformer = () => {
  return Transform(({ value }) => {
    if (value === null || value === undefined) return value;
    return parseFloat(value);
  });
};

export const TransformDateTimeFromTimestamp = (
  timezone = "UTC",
  dateFormat = "yyyy-MM-dd",
  timeFormat = "HH:mm a"
) => {
  return Transform(({ value }) => {
    if (!value) return null;

    const timestamp = Number(value);
    if (isNaN(timestamp)) return null;

    const dt = DateTime.fromMillis(timestamp).setZone(timezone);

    if (!dt.isValid) return null;

    return {
      date: dt.toFormat(dateFormat),
      time: dt.toFormat(timeFormat),
      timestamp: value,
    };
  });
};

/**
 * Transforms HTML decoded string into clean string.
 */
export const DecodeHtmlTransformer = () => {
  return Transform(({ value }) => {
    if (value === null || value === undefined) return value;
    return value.replace(/&#(\d+);|&#x([\da-fA-F]+);/g, (_, dec, hex) => {
      const codePoint = dec ? parseInt(dec, 10) : parseInt(hex, 16);
      return String.fromCharCode(codePoint);
    });
  });
};

/**
 * Validate if date is 29/03/2024.
 */
export const IsFccDateFormat = (
  message = "Date must be in DD/MM/YYYY format (e.g., 29/03/2024)"
) => {
  return Matches(/^(0[1-9]|[12][0-9]|3[01])\/(0[1-9]|1[0-2])\/(19|20)\d{2}$/, {
    message,
  });
};

export const validateDateRange = (
  dateFrom?: string,
  dateTo?: string
): { isValid: boolean; error?: string } => {
  if (!dateFrom || !dateTo) {
    return { isValid: true };
  }

  try {
    const [fromDay, fromMonth, fromYear] = dateFrom.split('/').map(Number);
    const [toDay, toMonth, toYear] = dateTo.split('/').map(Number);
    
    const from = new Date(fromYear, fromMonth - 1, fromDay);
    const to = new Date(toYear, toMonth - 1, toDay);

    if (isNaN(from.getTime()) || isNaN(to.getTime())) {
      return {
        isValid: false,
        error: "Invalid date format. Please use DD/MM/YYYY format."
      };
    }

    if (from > to) {
      return {
        isValid: false,
        error: "From date must be before to date."
      };
    }

    const threeMonthsInMs = 90 * 24 * 60 * 60 * 1000; // 90 days
    if (to.getTime() - from.getTime() > threeMonthsInMs) {
      return {
        isValid: false,
        error: "Date range cannot exceed 3 months. Please select a period within 3 months."
      };
    }

    return { isValid: true };
  } catch (error) {
    return {
      isValid: false,
      error: "Invalid date format. Please use DD/MM/YYYY format."
    };
  }
};

/**
 * Transforms query param to an array.
 * Handles: ?param=a,b OR ?param=a&param=b
 */
export const QueryArrayTransformer = () => {
  return Transform(({ value }) => {
    // value can be: 'EGP,USD' or ['EGP', 'USD']
    if (Array.isArray(value)) return value.flatMap((v) => v.split(","));
    if (typeof value === "string") return value.split(",");
    return [];
  });
};

export function FlattenArrayTransformer<T>(
  dto: ClassConstructor<T>,
  path: string[]
) {
  return Transform(
    ({ obj }) => {
      let value = obj;
      for (const segment of path) {
        value = value?.[segment];
        if (!value) break;
      }

      if (!value) {
        return [];
      }

      const array = Array.isArray(value) ? value : [value];

      return plainToInstance(dto, array, {
        excludeExtraneousValues: true,
      });
    },
    { toClassOnly: true }
  );
}

export const MaskCardNumberTransformer = () =>
  Transform(({ value }) => maskCardNumber(value));

export const InsertLineBreaksTransformer = (size: number = 35) =>
  Transform(({ value }) => insertLineBreaks(value, size));
