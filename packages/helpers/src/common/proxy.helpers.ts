import { ProxyConfig } from "@types";
import { HttpsProxyAgent } from "https-proxy-agent";
import * as https from "node:https";

export function createHttpsAgent(proxy: ProxyConfig | false) {
  if (proxy) {
    const proxyUrl = `${proxy.protocol}://${proxy.host}:${proxy.port}`;
    return new HttpsProxyAgent(proxyUrl);
  }
  return new https.Agent({
    keepAlive: true,
    rejectUnauthorized: false,
  });
}
