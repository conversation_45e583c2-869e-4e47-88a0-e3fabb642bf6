import * as crypto from "crypto";
import * as zlib from "zlib";

const ALGORITHM = "aes-256-ecb";
const ENCODING: BufferEncoding = "base64";

const deriveUserKey = (globalKeyHex: string, userId: string): Buffer => {
  return crypto
    .createHash("sha256")
    .update(Buffer.from(globalKeyHex, "hex"))
    .update(userId)
    .digest();
};

export const encryptUserJson = (
  data: object,
  globalKeyHex: string,
  userId: string
): string => {
  const compressedBuffer = zlib.deflateSync(JSON.stringify(data));
  const keyBuffer = deriveUserKey(globalKeyHex, userId);
  const cipher = crypto.createCipheriv(ALGORITHM, keyBuffer, null);
  const encryptedBuffer = Buffer.concat([
    cipher.update(compressedBuffer),
    cipher.final(),
  ]);
  return encryptedBuffer.toString(ENCODING);
};

export const decryptUserJson = <T = any>(
  encrypted: string,
  globalKeyHex: string,
  userId: string
): T => {
  const keyBuffer = deriveUserKey(globalKeyHex, userId);
  const decipher = crypto.createDecipheriv(ALGORITHM, keyBuffer, null);
  const decryptedBuffer = Buffer.concat([
    decipher.update(Buffer.from(encrypted, ENCODING)),
    decipher.final(),
  ]);
  const decompressedBuffer = zlib.inflateSync(decryptedBuffer);
  return JSON.parse(decompressedBuffer.toString("utf8"));
};
