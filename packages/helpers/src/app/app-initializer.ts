import { NestFactory } from "@nestjs/core";
import {
  FastifyAdapter,
  NestFastifyApplication,
} from "@nestjs/platform-fastify";

import multipart from "@fastify/multipart";

import { Logger, ValidationPipe } from "@nestjs/common";
import { SecurityHeadersMiddleware } from "@middlewares";
import { defaultConfig, I18nLookupService } from "@modules";
import { GlobalAdvisorFilter } from "@filters";

export type AppInitializerOptions = {
  prefix?: string;
  filesUpload?: boolean;
  disableExceptionFilters?: boolean;
};

/**
 * Main class responsible for bootstrapping the NestJS application
 * with Fastify, configuring middleware, pipes, static assets,
 * and initializing tracing if enabled.
 */
const logger = new Logger("Boostrap server");

export class AppInitializer {
  /**
   * Initializes the application:``
   * - Configures tracing.
   * - Creates and configures the Fastify application.
   * - Starts the server.
   */
  async initialize(module: any, options: AppInitializerOptions = {}) {
    const app = await this.createApp(module, options); // Create the Fastify-based NestJS app
    this.configureApp(app, options); // Apply configurations like middlewares, pipes, static assets
    await this.startServer(app); // Start the Fastify server
  }

  /**
   * Creates a Fastify-based NestJS application.
   * - Sets Fastify-specific options such as `maxParamLength`.
   * - Configures CORS (Cross-Origin Resource Sharing) based on environment variables.
   * - Uses a custom logger (`CustomLogger`) for application-wide logging.
   *
   * @returns A promise that resolves to a `NestFastifyApplication` instance.
   */
  private async createApp(
    module: any,
    options: AppInitializerOptions
  ): Promise<NestFastifyApplication> {
    const config = defaultConfig();

    const bodyLimit = Number(config.fastify.bodyLimit);
    const fastifyOptions = {
      maxParamLength: 350,
      bodyLimit: 1024 * 1024 * bodyLimit,
    };

    const corsOptions = {
      origin: JSON.parse(process.env.CORS_URLS ?? "[]"), // Allowed CORS origins from env
      credentials: true, // Enable CORS credentials
    };

    // Create a Fastify-based Nest application with custom CORS and logging options
    const app = await NestFactory.create<NestFastifyApplication>(
      module,
      new FastifyAdapter(fastifyOptions),
      {
        cors: corsOptions,
      }
    );

    logger.log(`Fastify bodyLimit: ${bodyLimit}MB`);

    const { filesUpload = false } = options;

    if (filesUpload) {
      const maxSize = Number(config.upload.maxSize);
      logger.log(`Multipart uploader has been enabled - maxSize: ${maxSize}MB`);
      await app.register(multipart, {
        limits: {
          fileSize: 1024 * 1024 * maxSize, // 5MB limit
        },
        attachFieldsToBody: "keyValues",
        onFile: this.onFileUpload,
      });
    }

    return app;
  }

  private async onFileUpload(part) {
    const buffer = await part.toBuffer();
    part.value = {
      filename: part.filename,
      base64: Buffer.from(buffer).toString("base64"),
      mimetype: part.mimetype,
      filesize: buffer.length,
    };
  }

  /**
   * Configures the NestJS Fastify application with:
   * - Global pipes for validation.
   * - Custom middlewares (e.g., security headers).
   * - Static asset serving for files in the `/static` directory.
   * - Global prefix for versioning (e.g., `v1`).
   * - Enables shutdown hooks for graceful termination.
   *
   * @param app The NestFastifyApplication instance.
   */
  private configureApp(
    app: NestFastifyApplication,
    options: AppInitializerOptions
  ) {
    this.setupPipes(app); // Set up global validation pipes
    this.setupMiddlewares(app); // Set up custom middlewares
    this.setGlobalPrefix(app, options); // Apply global prefix for API versioning
    if (!options?.disableExceptionFilters) {
      this.setExceptionFilters(app);
    }
    app.enableShutdownHooks(); // Enable hooks to handle app shutdown gracefully
  }

  /**
   * Configures global validation pipes for the application.
   * - Uses the `ValidationPipe` to enforce input validation and whitelist allowed properties.
   *
   * @param app The NestFastifyApplication instance.
   */
  private setupPipes(app: NestFastifyApplication) {
    app.useGlobalPipes(
      new ValidationPipe({
        transform: true,
        whitelist: true, // Automatically remove properties not in DTOs
      })
    );
  }

  /**
   * Sets up middlewares for the application.
   * - Includes security headers middleware.
   *
   * @param app The NestFastifyApplication instance.
   */
  private setupMiddlewares(app: NestFastifyApplication) {
    app.use(new SecurityHeadersMiddleware().use); // Apply security headers
  }

  /**
   * Sets a global prefix for the API routes.
   * - Applies a version prefix (`v1`) to all routes except `_health`.
   *
   * @param app The NestFastifyApplication instance.
   */
  private setGlobalPrefix(
    app: NestFastifyApplication,
    options: AppInitializerOptions
  ) {
    const { prefix = "v1" } = options;
    app.setGlobalPrefix(prefix, { exclude: ["_health"] }); // Exclude health check route from versioning
  }

  /**
   * Starts the Fastify server.
   * - Listens on the port and host defined in environment variables, with fallback defaults.
   *
   * @param app The NestFastifyApplication instance.
   */
  private async startServer(app: NestFastifyApplication) {
    const config = defaultConfig();
    await app.listen(config.port, config.host); // Start the server
    logger.log(`Server running on port ${config.port}`); // Log the startup info
  }

  public setExceptionFilters(app: NestFastifyApplication) {
    logger.log(`useGlobalFilters is enabled`);
    app.useGlobalFilters(new GlobalAdvisorFilter(app.get(I18nLookupService)));
  }
}
