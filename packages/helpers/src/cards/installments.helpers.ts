import { DateTime } from "luxon";
import { InstallmentsCampaignStatusEnum } from "@enums";
import {
  applyAmountFilter,
  applyDateFilter,
  applySearchFilter,
  sortByDate,
} from "../common";

export function filterInstallments(
  installments: any[],
  options: {
    fromAmount?: number;
    toAmount?: number;
    fromDate?: string;
    toDate?: string;
    search?: string;
    sort?: "asc" | "desc";
  } = {}
) {
  let result = [...installments];

  // 1. Exclude Cancelled and Completed Installments
  result = result.filter(
    (i) => !isCancelledInstallment(i) && !isCompletedInstallment(i)
  );

  const count = result.length;
  // 2. Apply amount filter
  result = applyAmountFilter(
    result,
    options.fromAmount,
    options.toAmount,
    "originalAmount"
  );

  // 3. Apply date filter
  result = applyDateFilter(
    result,
    options.fromDate,
    options.toDate,
    "startDate"
  );

  // 4. Search by merchantName
  result = applySearchFilter(result, options.search, "merchantName");

  // 5. Sort installments by startDate
  result = sortByDate(result, options.sort ?? "desc", "startDate");

  return {
    count,
    installments: result,
  };
}

export function isCancelledInstallment(installment: any): boolean {
  return [
    InstallmentsCampaignStatusEnum.CancelledOrRefunded,
    InstallmentsCampaignStatusEnum.EarlySettlement,
  ].includes(
    String(installment.campaignStatus) as InstallmentsCampaignStatusEnum
  );
}

export function isCompletedInstallment(installment: any): boolean {
  if (installment.endDate) {
    const end = DateTime.fromISO(installment.endDate);
    const today = DateTime.now();
    return end < today;
  }
  return false;
}
