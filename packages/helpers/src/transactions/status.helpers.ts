import {
  TransactionSubStatusMap,
  TransactionStatusMap,
  TransactionStatusFilterMap,
} from "@constants";
import {
  ProductStatusCodeEnum,
  SubTransactionStatusCodeEnum,
  TransactionStatusCodeEnum,
  TransactionStatusEnum,
  TransactionStatusFieldsEnum,
} from "@enums";

export const getTransactionStatusByCode = (code: string) => {
  return TransactionStatusMap[code ?? TransactionStatusCodeEnum.Unknown];
};

export const getTransactionSubStatusByCode = (code: string) => {
  return TransactionSubStatusMap[code ?? SubTransactionStatusCodeEnum.Unknown];
};

export const transactionStatusMapper = (
  status: TransactionStatusEnum | TransactionStatusEnum[]
): Record<
  keyof (typeof TransactionStatusFilterMap)[TransactionStatusEnum],
  string[]
> => {
  const statusList = Array.isArray(status) ? status : [status];

  const result = {
    [TransactionStatusFieldsEnum.TnxStatCode]: new Set<string>(),
    [TransactionStatusFieldsEnum.SubTnxStatCode]: new Set<string>(),
    [TransactionStatusFieldsEnum.ProdStatCode]: new Set<string>(),
  };

  for (const s of statusList) {
    const data = TransactionStatusFilterMap[s];
    if (!data) continue;

    for (const value of data?.[TransactionStatusFieldsEnum.TnxStatCode] ?? []) {
      result[TransactionStatusFieldsEnum.TnxStatCode].add(String(value));
    }

    for (const value of data?.[TransactionStatusFieldsEnum.SubTnxStatCode] ??
      []) {
      result[TransactionStatusFieldsEnum.SubTnxStatCode].add(String(value));
    }

    for (const value of data?.[TransactionStatusFieldsEnum.ProdStatCode] ??
      []) {
      result[TransactionStatusFieldsEnum.ProdStatCode].add(String(value));
    }
  }

  return {
    [TransactionStatusFieldsEnum.TnxStatCode]: Array.from(
      result?.[TransactionStatusFieldsEnum.TnxStatCode]
    ),
    [TransactionStatusFieldsEnum.SubTnxStatCode]: Array.from(
      result?.[TransactionStatusFieldsEnum.SubTnxStatCode]
    ),
    [TransactionStatusFieldsEnum.ProdStatCode]: Array.from(
      result?.[TransactionStatusFieldsEnum.ProdStatCode]
    ),
  };
};
