import {
  <PERSON><PERSON><PERSON>y,
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsN<PERSON>ber,
  IsOptional,
  IsString,
  Min,
} from "class-validator";
import { IsFccDateFormat, QueryArrayTransformer } from "@helpers";
import { TdRenewalInstructionsEnum } from "@enums";
import { Transform, Type } from "class-transformer";
import { ListDefQueryDto } from "../common";

export class TermDepositSubmitRequestDto {
  @IsString()
  @IsNotEmpty()
  applicantActName: string;

  @IsString()
  @IsNotEmpty()
  applicantActNo: string;

  @IsString()
  @IsNotEmpty()
  applicantActCurCode: string;

  @IsString()
  @IsOptional()
  applicantActDescription: string;

  @IsString()
  @IsNotEmpty()
  tdAmount: string;

  @IsString()
  @IsNotEmpty()
  currencyCode: string;

  @IsString()
  @IsNotEmpty()
  creditActName: string;

  @IsString()
  @IsNotEmpty()
  creditActNo: string;

  @IsString()
  @IsNotEmpty()
  creditActCurCode: string;

  @IsOptional()
  @IsFccDateFormat()
  startDate: string;

  @IsOptional()
  @IsArray()
  attachments: string[];

  @IsString()
  @IsOptional()
  reauthPassword: string;

  @IsBoolean()
  @IsNotEmpty()
  autoForward: boolean;

  @IsString()
  @IsNotEmpty()
  productId: string;

  @IsEnum(TdRenewalInstructionsEnum)
  @IsNotEmpty()
  renewalInstruction: TdRenewalInstructionsEnum;
}
export class DepositListQueryDto extends ListDefQueryDto {
  // Financial Filters
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @QueryArrayTransformer()
  curCode?: string[];

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  fromAmount?: number;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  toAmount?: number;

  // Date filters
  @IsOptional()
  @IsString()
  valueDate?: string;

  @IsOptional()
  @IsString()
  maturityDate?: string;
}
