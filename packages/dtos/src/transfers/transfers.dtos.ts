import {
  IsArray,
  IsEnum,
  IsIn,
  IsNotEmpty,
  IsNumberString,
  IsOptional,
  IsString,
  MaxLength,
} from "class-validator";
import { AttachmentRefDto } from "../common";
import { IbanTypeEnum, TransfersChargeOptionEnum } from "@enums";
import { InsertLineBreaksTransformer, IsFccDateFormat } from "@helpers";

export class TransferSubmitRequestDto {
  @IsString()
  @IsNotEmpty()
  applicantActName: string;

  @IsString()
  @IsNotEmpty()
  applicantActNo: string;

  @IsString()
  @IsNotEmpty()
  applicantActCurCode: string;

  @IsString()
  @IsOptional()
  applicantActDescription: string;

  @IsNumberString()
  @IsNotEmpty()
  ftAmt: string;

  @IsString()
  @IsNotEmpty()
  ftCurCode: string;

  @IsString()
  @IsNotEmpty()
  @MaxLength(140) //T24 rule
  @InsertLineBreaksTransformer(35)
  paymentDetailsFt: string;

  @IsString()
  @IsOptional()
  reauthPassword: string;

  @IsOptional()
  @IsFccDateFormat()
  transferDate: string;

  @IsOptional()
  @IsArray()
  attachments: string[];

  @IsOptional()
  @IsString()
  fxRateCustom: string;

  @IsOptional()
  @IsString()
  fxDealerName: string;
}

export class SwiftTransferSubmitRequestDto extends TransferSubmitRequestDto {
  @IsString()
  @IsNotEmpty()
  beneficiaryName: string;

  @IsString()
  @IsNotEmpty()
  beneficiaryAccount: string;

  @IsString()
  @IsNotEmpty()
  beneficiaryAccountId: string;

  @IsEnum(TransfersChargeOptionEnum)
  @IsNotEmpty()
  chargeOption: TransfersChargeOptionEnum;
}

export class ValidateIbanDto {
  @IsNotEmpty()
  @IsString()
  iban: string;

  @IsOptional()
  @IsIn([IbanTypeEnum.Domestic, IbanTypeEnum.International])
  type: string;
}
