import { IsFccDateFormat } from "@helpers";
import { Expose } from "class-transformer";
import { IsIn, IsNotEmpty, IsOptional, IsString } from "class-validator";
import { ListDefQueryDto } from "./listdef.dtos";

export class MovementsListRequestDto extends ListDefQueryDto {
  @Expose()
  @IsOptional()
  @IsFccDateFormat()
  dateFrom: string;

  @Expose()
  @IsOptional()
  @IsFccDateFormat()
  dateTo: string;

  @Expose()
  @IsOptional()
  amountFrom: string;

  @Expose()
  @IsOptional()
  amountTo: string;

  @Expose()
  @IsOptional()
  search: string;

  @Expose()
  @IsOptional()
  accountId?: string;
}

export class CardsMovementsListRequestDto extends MovementsListRequestDto {
  @Expose()
  @IsOptional()
  searchTranId?: string;
}

export class MovementsDownloadRequestDto extends MovementsListRequestDto {
  @Expose()
  @IsNotEmpty()
  @IsIn(["pdf", "csv", "swift"])
  format: string;
}
