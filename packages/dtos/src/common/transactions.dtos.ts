import { QueryArrayTransformer } from "@helpers";
import { Expose, Type } from "class-transformer";
import {
  IsString,
  IsNumber,
  IsOptional,
  MaxLength,
  Min,
  IsArray,
  IsEnum,
  IsNotEmpty,
} from "class-validator";
import { ListDefQueryDto } from "./listdef.dtos";
import { TransactionStatusEnum } from "@enums";

/**
 * MyPending Transactions optional filter parameters.
 * DTO Inherits Listdata pagination parameters from ListDefQueryDto
 */
export class TransactionsListQueryDto extends ListDefQueryDto {
  // Products Filter
  @IsOptional()
  @IsString()
  @MaxLength(35)
  productCode?: string;

  @IsOptional()
  @IsString()
  @MaxLength(35)
  subProductCode?: string;

  // Financial Filters
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @QueryArrayTransformer()
  curCode?: string[];

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  fromAmount?: number;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  toAmount?: number;

  @IsOptional()
  @IsString()
  @MaxLength(34)
  accountNo?: string;

  // Date filters
  @IsOptional()
  @IsString()
  inpDateRange?: string;

  @IsOptional()
  @IsString()
  inpDateRange2?: string;

  // 1M,2M,...
  @IsOptional()
  @IsString()
  timeframe?: string;

  // Unified status
  @IsOptional()
  @IsEnum(TransactionStatusEnum)
  @IsString()
  status?: TransactionStatusEnum;

  // Search by reference ID
  @IsOptional()
  @IsString()
  @MaxLength(35)
  refId?: string;

  // Sorting
  // values for ASC: "inp_dttm", "tnx_amt", "ref_id"
  // values for DESC: "-inp_dttm", "-tnx_amt", "-ref_id"
  @IsOptional()
  @IsString()
  @MaxLength(50)
  sort?: string;

  // Inputter
  @IsOptional()
  @IsString()
  inputterUserId?: string;

  @IsOptional()
  @IsString()
  excludeInputterUserId?: string;
}

export class DownloadTransactionRequestDto {
  @Expose()
  @IsString()
  @IsNotEmpty()
  referenceId: string;

  @Expose()
  @IsString()
  @IsNotEmpty()
  tnxId: string;

  @Expose()
  @IsString()
  @IsNotEmpty()
  productCode: string;
}
