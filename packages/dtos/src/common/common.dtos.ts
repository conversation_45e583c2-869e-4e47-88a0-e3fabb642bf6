import { CutOffTimeContextEnum } from "@enums";
import { IsFccDateFormat } from "@helpers";
import { Expose, Type } from "class-transformer";
import {
  IsNotEmpty,
  IsEnum,
  IsString,
  IsNumber,
  IsOptional,
} from "class-validator";

export class CutOffTimeRequestDto {
  @Expose()
  @IsNotEmpty()
  @IsEnum(CutOffTimeContextEnum)
  context: CutOffTimeContextEnum;

  @Expose()
  @IsString()
  @IsOptional()
  @IsFccDateFormat()
  date: string;

  @Expose()
  @Type(() => Number)
  @IsNumber()
  amount: number;

  @Expose()
  @IsString()
  @IsOptional()
  curCode: string;
}
