import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>otEmpty, IsOptional } from "class-validator";

export type AttachmentFile = {
  base64: string;
  mimetype: string;
  filesize: number;
  filename: string;
};

export class UploadAttachmentDto {
  @IsNotEmpty()
  file: AttachmentFile;
  @IsNotEmpty()
  tnxId: string;
  @IsNotEmpty()
  refId: string;
  @IsOptional()
  fileTitle: string;
  @IsOptional()
  identifier: string;
  @IsOptional()
  fileName: string;
}

export class AttachmentRefDto {
  @IsNotEmpty()
  @IsArray()
  docId: string[];
}

