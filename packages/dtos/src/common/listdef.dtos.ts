import { Expose, Type } from "class-transformer";
import {
  <PERSON>N<PERSON>ber,
  IsOptional,
  IsString,
  MaxLength,
  Min,
} from "class-validator";

export class ListDefQueryDto {
  @Expose()
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  first?: number;

  @Expose()
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  rows?: number;

  // Sorting
  @Expose()
  @IsOptional()
  @IsString()
  @MaxLength(50)
  sort?: string;

  @Expose()
  @IsOptional()
  @IsString()
  @MaxLength(50)
  sortField?: string;

  @Expose()
  @IsOptional()
  @IsString()
  @MaxLength(50)
  sortOrder?: string;
}
