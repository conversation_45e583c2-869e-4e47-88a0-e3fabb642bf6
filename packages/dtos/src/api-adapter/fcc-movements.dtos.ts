import { MovementTypeEnum } from "@enums";
import { MoneyTransformer, DecodeHtmlTransformer } from "@helpers";
import { Expose, Transform } from "class-transformer";
import { DateTime } from "luxon";

export class MovementsItemResponseDto {
  @Expose({ name: "account_id" })
  accountId: string;

  @Expose({ name: "card_number" })
  cardNumber: string;

  @Expose({ name: "line@runbal_booked" })
  @MoneyTransformer()
  balance: number;

  @Expose({ name: "line@cur_code" })
  curCode: string;

  @Expose({ name: "line@cust_ref_id" })
  custRefId: string;

  @Expose({ name: "account@owner_type" })
  ownerType: string;

  @Expose({ name: "line@reference_1" })
  reference1: string;

  @Expose({ name: "_group_id" })
  groupId: string;

  @Expose({ name: "line@bo_ref_id" })
  boRefId: string;

  @Expose({ name: "line@reference_2" })
  reference2: string;

  @Expose({ name: "line@reference_3" })
  reference3: string;

  @Expose({ name: "line@reference_4" })
  reference4: string;

  @Expose({ name: "line@reference_5" })
  reference5: string;

  @Expose({ name: "line@cheque_number" })
  chequeNumber: string;

  @Expose({ name: "account@account_no" })
  accountNo: string;

  @Expose({ name: "line@value_date" })
  @DecodeHtmlTransformer()
  valueDate: string;

  @Expose({ name: "line@description" })
  description: string;

  @Expose({ name: "line@post_date" })
  @DecodeHtmlTransformer()
  postDate: string;

  @Expose({ name: "time" })
  @DecodeHtmlTransformer()
  time: string;

  @Expose({ name: "line@line_id" })
  lineId: string;

  @Expose({ name: "credit_limit" })
  @MoneyTransformer()
  creditLimit: string;

  @Expose({ name: "aggregate-0" })
  aggregate0: string;

  @Expose({ name: "aggregate-1" })
  aggregate1: string;

  @Expose({ name: "aggregate-2" })
  aggregate2: string;

  @Expose()
  @Transform(
    ({ obj }) => {
      const withdrawal = obj?.["line@withdrawal"];
      const isWithdrawal = withdrawal && withdrawal !== "";
      return isWithdrawal ? withdrawal : obj["line@deposit"];
    },
    { toClassOnly: true }
  )
  @MoneyTransformer()
  amount: number;

  @Expose()
  @Transform(
    ({ obj }) => {
      const withdrawal = obj?.["line@withdrawal"];
      const isWithdrawal = withdrawal && withdrawal !== "0";
      return isWithdrawal
        ? MovementTypeEnum.Withdrawal
        : MovementTypeEnum.Deposit;
    },
    { toClassOnly: true }
  )
  type: MovementTypeEnum;
}

export class AccountMovementsItemResponseDto extends MovementsItemResponseDto {}

export class CreditCardMovementsItemResponseDto extends MovementsItemResponseDto {
  @Expose({ name: "transaction_date" })
  @Transform(({ value }) =>
    DateTime.fromFormat(value, "dd-MM-yyyy").toFormat("dd/MM/yyyy")
  )
  transactionDate: string;

  @Expose({ name: "transaction_id" })
  transactionId: string;

  @Expose({ name: "currency" })
  currency: string;

  @Expose({ name: "description" })
  description: string;

  // Override cc amount & type calculation for now (withdrawal/deposit & line@withdrawal/line@deposit)
  @Expose()
  @Transform(
    ({ obj }) => {
      const withdrawal = obj?.["withdrawal"];
      const isWithdrawal = withdrawal && withdrawal !== "";
      return isWithdrawal ? withdrawal : obj["deposit"];
    },
    { toClassOnly: true }
  )
  @MoneyTransformer()
  amount: number;

  @Expose()
  @Transform(
    ({ obj }) => {
      const withdrawal = obj?.["withdrawal"];
      const isWithdrawal =
        withdrawal && withdrawal !== "" && withdrawal !== "0";
      return isWithdrawal
        ? MovementTypeEnum.Withdrawal
        : MovementTypeEnum.Deposit;
    },
    { toClassOnly: true }
  )
  type: MovementTypeEnum;
}
