import { MaskCardNumberTransformer } from "@helpers";
import { Expose } from "class-transformer";

export class CreditCardListItemDto {
  @Expose({ name: "Entity" })
  entity: string;

  @Expose({ name: "card_number" })
  cardNumber: string;

  @Expose({ name: "ObjectDataString@cc_card_type" })
  cardType: string;

  @Expose({ name: "account_no" })
  @MaskCardNumberTransformer()
  maskedCardNumber: string;

  @Expose({ name: "ObjectDataString@bo_card_id" })
  cardId: string;

  @Expose({ name: "account_type2" })
  accountType2: string;

  @Expose({ name: "cur_code" })
  currencyCode: string;

  @Expose({ name: "account_id" })
  accountId: string;

  @Expose({ name: "ObjectDataString@bo_owner_number" })
  ownerNumber: string;

  @Expose({ name: "ObjectDataString@bo_sema_number" })
  semaNumber: string;

  @Expose({ name: "acct_name" })
  accountName: string;
}
