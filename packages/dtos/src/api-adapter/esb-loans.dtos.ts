import { Expose, plainToInstance, Transform, Type } from "class-transformer";
import { castArray, keyBy } from "lodash";
import { EsbAmountDto } from "./esb-common.dtos";

export class EsbLoanListItemDto {
  @Expose({ name: "LoanProductType" })
  loanProductType: string;

  @Expose({ name: "ProductIdent" })
  productIdent: string;

  @Expose({ name: "MaturityDt" })
  maturityDt: string;

  @Expose({ name: "LoanAmt" })
  @Type(() => EsbAmountDto)
  amount: EsbAmountDto;

  @Expose({ name: "OutstandingAmt" })
  @Type(() => EsbAmountDto)
  outstandingAmt: EsbAmountDto;

  @Expose({ name: "OverDueAmt" })
  @Type(() => EsbAmountDto)
  overDueAmt: EsbAmountDto;

  @Expose({ name: "InterestRate" })
  interestRate: number;

  @Expose({ name: "Tenor" })
  tenor: string;

  @Expose({ name: "Status" })
  status: string;
}
export class LoanCreditBalanceDto {
  @Expose({ name: "BookBalance" })
  @Transform(({ value }) => {
    return plainToInstance(EsbAmountDto, value.CurAmt, {
      excludeExtraneousValues: true,
    });
  })
  bookBalance: EsbAmountDto;

  @Expose({ name: "AvailCredit" })
  @Transform(({ value }) => {
    return plainToInstance(EsbAmountDto, value.CurAmt, {
      excludeExtraneousValues: true,
    });
  })
  availCredit: EsbAmountDto;

  @Expose({ name: "CreditLimit" })
  @Transform(({ value }) => {
    return plainToInstance(EsbAmountDto, value.CurAmt, {
      excludeExtraneousValues: true,
    });
  })
  creditLimit: EsbAmountDto;

  @Expose({ name: "ClearBalance" })
  @Transform(({ value }) => {
    return plainToInstance(EsbAmountDto, value.CurAmt, {
      excludeExtraneousValues: true,
    });
  })
  clearBalance: EsbAmountDto;
}

export class LoanDataDto {
  @Expose({ name: "IntRate" })
  interestRate: number;

  @Expose({ name: "TotalInt" })
  totalInterest: number;
}

export class EsbLoanDetailsDto {
  @Expose({ name: "NS4:PartyIdent" })
  partyIdent: string;

  @Expose({ name: "LoanProductTypeDesc" })
  loanProductTypeDesc: string;

  @Expose({ name: "Status" })
  status: string;

  @Expose({ name: "DrawDnAcct" })
  drawDownAccount: string;

  @Expose({ name: "MaturityDt" })
  maturityDate: string;

  @Expose({ name: "OriginationDt" })
  originationDate: string;

  @Expose({ name: "PurposeDesc" })
  purposeDesc: string;

  @Expose({ name: "DueDt" })
  dueDate: string;

  @Expose({ name: "OverDueAmt" })
  @Type(() => EsbAmountDto)
  overdueAmount: EsbAmountDto;

  @Expose({ name: "CreditProdBal" })
  @Transform(({ value }) => {
    return plainToInstance(
      LoanCreditBalanceDto,
      keyBy(castArray(value), "BalType"),
      {
        excludeExtraneousValues: true,
      },
    );
  })
  balances: LoanCreditBalanceDto;

  @Expose({ name: "LOCLoanData" })
  @Type(() => LoanDataDto)
  loanData: LoanDataDto;

  @Expose({ name: "InterestLiqAcct" })
  interestLiquidationAccount: string;
}
