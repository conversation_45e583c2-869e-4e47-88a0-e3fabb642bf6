import { MaskCardNumberTransformer } from "@helpers";
import { Expose, plainToInstance, Transform, Type } from "class-transformer";
import { keyBy } from "lodash";
import { EsbAmountDto } from "./esb-common.dtos";

export class EsbDebitCardListItemDto {
  @Expose({ name: "CardIdent" })
  vpan: string;

  @Expose({ name: "CardHoldername" })
  cardHolderName: string;

  @Expose({ name: "CardType" })
  cardType: string;

  @Expose({ name: "ProductCode" })
  productCode: string;

  @Expose({ name: "ExpDt" })
  expDate: string;

  @Expose({ name: "ActivationDt" })
  activationDate: string;

  @Expose({ name: "ActivationUsr" })
  activationUser: string;

  @Expose({ name: "MaskedCardNum" })
  @MaskCardNumberTransformer()
  maskedCardNumber: string;

  @Expose({ name: "CardSatus" })
  status: string;

  @Expose({ name: "CurCode" })
  @Transform(({ value }) => value?.CurCodeValue)
  curCode: string;

  @Expose({ name: "OwnerParty" })
  ownerParty: string;

  @Expose({ name: "PinSetupFlag" })
  pinSetupFlag: boolean;
}

class LastPaymentInfoDto {
  @Expose({ name: "LastPaymentDt" })
  lastPaymentDate: string;

  @Expose({ name: "LastPaymentAmt" })
  @Transform(({ value }) => value?.Amt)
  lastPaymentAmount: number;
}

class CardBalanceDto {
  @Expose({ name: "Current Balance" })
  @Transform(({ value }) => value?.CurAmt?.Amt)
  currentBalance: number;

  @Expose({ name: "Authorized Balance" })
  @Transform(({ value }) => value?.CurAmt?.Amt)
  authorizedBalance: number;

  @Expose({ name: "RemainFullAmt" })
  @Transform(({ value }) => value?.CurAmt?.Amt)
  remainFullAmount: number;
}

class LegalIdentityDto {
  @Expose({ name: "LegalIdentType" })
  legalIdentType: string;

  @Expose({ name: "LegalIdentValue" })
  legalIdentValue: string;
}

class ContactInfoDto {
  @Expose({ name: "ContactValue" })
  contactValue: string;

  @Expose({ name: "ContactType" })
  contactType: string;
}

export class EsbCreditCardDetailsDto {
  @Expose({ name: "CardPartyIdent" })
  cardPartyIdent: number;

  @Expose({ name: "OwnerParty" })
  ownerParty: string;

  @Expose({ name: "CardHolderName" })
  cardHolderName: string;

  @Expose({ name: "CardCur" })
  @Transform(({ value }) => value?.CurCodeValue)
  cardCurrency: string;

  @Expose({ name: "CardStatus" })
  cardStatus: number;

  @Expose({ name: "CardStatusDesc" })
  cardStatusDesc: string;

  @Expose({ name: "StopCode" })
  stopCode: number;

  @Expose({ name: "CardCategory" })
  productCode: string;

  @Expose({ name: "CardBranch" })
  cardBranch: number;

  @Expose({ name: "ExpDt" })
  expiryDate: string;

  @Expose({ name: "ActivationDt" })
  activationDate: string;

  @Expose({ name: "TotalLimit" })
  @Transform(({ value }) => value?.Amt)
  totalLimit: number;

  @Expose({ name: "AvailCred" })
  @Transform(({ value }) => value?.Amt)
  availableCredit: number;

  @Expose({ name: "TotalCashLimit" })
  @Transform(({ value }) => value?.Amt)
  totalCashLimit: number;

  @Expose({ name: "AvailCash" })
  @Transform(({ value }) => value?.Amt)
  availableCash: number;

  @Expose({ name: "CardBal" })
  @Transform(({ value }) => {
    return plainToInstance(CardBalanceDto, keyBy(value, "BalType"), {
      excludeExtraneousValues: true,
    });
  })
  balances: CardBalanceDto;

  @Expose({ name: "nextPaymentDueDt" })
  dueDate: string;

  @Expose({ name: "LastStatementBalance" })
  @Transform(({ value }) => value?.CurAmt?.Amt)
  lastStatementBalance: number;

  @Expose({ name: "LastTrnDt" })
  lastTransactionDate: string;

  @Expose({ name: "DueAmt" })
  @Transform(({ value }) => value?.Amt)
  dueAmount: number;

  @Expose({ name: "LegalIdent" })
  @Type(() => LegalIdentityDto)
  legalIdentity: LegalIdentityDto;

  @Expose({ name: "ContactInfo" })
  @Type(() => ContactInfoDto)
  contactInfo: ContactInfoDto;

  @Expose({ name: "NewCardUse" })
  newCardUse: number;

  @Expose({ name: "RemainingDueAmt" })
  @Transform(({ value }) => value?.Amt)
  minimumAmountDue: number;

  @Expose({ name: "InstallmentPercent" })
  totalAmountDue: number;

  @Expose({ name: "LastPaymentInfo" })
  @Type(() => LastPaymentInfoDto)
  lastPaymentInfo: LastPaymentInfoDto;

  @Expose({ name: "ApplicationOrigin" })
  applicationOrigin: number;

  @Expose({ name: "ArrearsAmt" })
  @Transform(({ value }) => value?.Amt)
  arrearsAmount: number;

  @Expose({ name: "AgreementStatus" })
  agreementStatus: number;

  @Expose({ name: "ClosingDt" })
  closingDate: string;

  @Expose({ name: "GroupAcct" })
  groupAccount: number;

  @Expose({ name: "DirectDebitAccount" })
  directDebitAccount: number;

  @Expose({ name: "DirectDebitPercentage" })
  directDebitPercentage: string;
}

export class EsbInstallmentsListItemDto {
  @Expose({ name: "Period" })
  period: number;

  @Expose({ name: "MerchName" })
  merchName: string;

  @Expose({ name: "OriginalAmt" })
  @Transform(({ value }) => value?.Amt)
  originalAmount: number;

  @Expose({ name: "InstalmentAmt" })
  @Transform(({ value }) => value?.PrincibleAmt?.Amt)
  instalmentAmount: number;

  @Expose({ name: "TransactionDt" })
  transactionDate: string;

  @Expose({ name: "RemainingAmt" })
  @Transform(({ value }) => value?.TotalAmt?.Amt)
  remainingAmount: number;

  @Expose({ name: "InterestRate" })
  interestRate: number;

  @Expose({ name: "CampaignAgreeIdent" })
  campaignAgreeIdent: number;

  @Expose({ name: "OpenDt" })
  openDate: string;

  @Expose({ name: "InstalmentsCount" })
  instalmentsCount: number;

  @Expose({ name: "RemainingInstCount" })
  remainingInstallmentsCount: number;

  @Expose({ name: "Currency" })
  @Transform(({ value }) => value?.CurCodeValue)
  currency: string;

  @Expose({ name: "CampaignStatus" })
  campaignStatus: number;

  @Expose({ name: "Tenor" })
  tenor: number;

  @Expose({ name: "CurrentBalance" })
  @Transform(({ value }) => value?.Amt)
  currentBalance: number;
}

export class EsbInstallmentsListDto {
  @Expose({ name: "CreditCardInstallmentsList" })
  @Type(() => EsbInstallmentsListItemDto)
  creditCardInstallmentsList: EsbInstallmentsListItemDto[];
}

export class EsbCreditCardListItemDto {
  @Expose({ name: "CardIdent" })
  cardIdent: number;

  @Expose({ name: "SemaIdent" })
  semaIdent: number;

  @Expose({ name: "ExpDt" })
  expDt: string;

  @Expose({ name: "CardSatus" }) // note: source has typo
  cardStatus: number;

  @Expose({ name: "CardStatusDesc" })
  cardStatusDesc: string;

  @Expose({ name: "TotalLimit" })
  @Type(() => EsbAmountDto)
  totalLimit: EsbAmountDto;

  @Expose({ name: "CardType" })
  cardType: string;

  @Expose({ name: "CredAcct" })
  credAcct: number;

  @Expose({ name: "AvailCred" })
  @Type(() => EsbAmountDto)
  availCred: EsbAmountDto;

  @Expose({ name: "MaskedCardNum" })
  @MaskCardNumberTransformer()
  maskedCardNumber: string;

  @Expose({ name: "CardKey" })
  cardKey: number;

  @Expose({ name: "CurrentBalance" })
  @Type(() => EsbAmountDto)
  currentBalance: EsbAmountDto;

  @Expose({ name: "DCFlag" })
  dcFlag: number;

  @Expose({ name: "AuthorisedBalance" })
  @Type(() => EsbAmountDto)
  authorisedBalance: EsbAmountDto;

  @Expose({ name: "EmbossingName" })
  embossingName: string;

  @Expose({ name: "DirectDebitAccount" })
  directDebitAccount: number;
}
