import { Expose, Type } from 'class-transformer';
import { IsNumber, IsString, ValidateNested } from 'class-validator';

export class ContraCurCodeDto {
  @Expose({ name: 'CurCodeValue' })
  @IsString()
  curCodeValue: string;
}

export class NewForExRateDto {
  @Expose({ name: 'ExchRate' })
  @IsNumber()
  exchRate: number;

  @Expose({ name: 'ContraCurCode' })
  @ValidateNested()
  @Type(() => ContraCurCodeDto)
  contraCurCode: ContraCurCodeDto;
}

export class ForExRateSheetCurRateDto {
  @Expose({ name: 'NewForExRate' })
  @ValidateNested()
  @Type(() => NewForExRateDto)
  newForExRate: NewForExRateDto;

  @Expose({ name: 'BuySellIndicator' })
  @IsString()
  buySellIndicator: string;
}

export class ForExRateSheetInfoDto {
  @Expose({ name: 'ForExRateSheetCurRate' })
  @ValidateNested({ each: true })
  @Type(() => ForExRateSheetCurRateDto)
  forExRateSheetCurRate: ForExRateSheetCurRateDto[];
}

export class ForexRatesEsbResponseDto {
  @Expose({ name: 'ForExRateSheetInfo' })
  @ValidateNested({ each: true })
  @Type(() => ForExRateSheetInfoDto)
  rates: ForExRateSheetInfoDto[];
}
