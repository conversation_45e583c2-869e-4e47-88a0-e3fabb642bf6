import {
  ProductCodeEnum,
  ProductStatusCodeEnum,
  SubTransactionStatusCodeEnum,
  TransactionStatusCodeEnum,
} from "@enums";
import { FlattenArrayTransformer } from "@helpers";
import { Expose, Transform, Type } from "class-transformer";

class AdditionalFieldsDto {
  @Expose({ name: "name" })
  name: string;

  @Expose({ name: "value" })
  value: string;
}

export class FccAttachmentDto {
  @Expose({ name: "ref_id" })
  refId: string;

  @Expose({ name: "tnx_id" })
  tnxId: string;

  @Expose({ name: "mime_type" })
  mimeType: string;

  @Expose({ name: "file_name" })
  fileName: string;

  @Expose({ name: "attachment_id" })
  attachmentId: string;

  @Expose()
  type: string;

  @Expose()
  title: string;

  @Expose({ name: "upload_date" })
  uploadDate: string;
}

class CounterpartyDto {
  @Expose({ name: "ref_id" })
  refId: string;

  @Expose({ name: "counterparty_id" })
  counterpartyId: string;

  @Expose({ name: "tnx_id" })
  tnxId: string;

  @Expose({ name: "company_id" })
  companyId: string;

  @Expose({ name: "brch_code" })
  brchCode: string;

  @Expose({ name: "counterparty_type" })
  counterpartyType: string;
}

class CounterpartiesDto {
  @Type(() => CounterpartyDto)
  @Expose({ name: "counterparty" })
  counterparty: CounterpartyDto;
}

class CommentDto {
  @Expose({ name: "text" })
  text: string;
}
export class FccGenericTnxResponseDto {
  @Expose({ name: "ref_id" })
  refId: string;

  @Expose({ name: "master_version" })
  masterVersion: string;

  @Expose({ name: "tnx_id" })
  tnxId: string;

  @Type(() => AdditionalFieldsDto)
  @Expose({ name: "additionalFields" })
  additionalFields: AdditionalFieldsDto[];

  @Expose({ name: "tnx_type_code" })
  tnxTypeCode: string;

  @Expose({ name: "inp_dttm" })
  inpDttm: string;

  @Expose({ name: "release_dttm" })
  releaseDttm: string;

  @Expose({ name: "product_code" })
  productCode: ProductCodeEnum;

  @Expose({ name: "userLanguage" })
  userLanguage: string;

  @Expose({ name: "userLangaugeMessage" })
  userLangaugeMessage: string;

  @Expose({ name: "brch_code" })
  brchCode: string;

  @Expose({ name: "sub_product_code" })
  subProductCode: string;

  @Expose({ name: "id" })
  id: string;

  @Type(() => CounterpartiesDto)
  @Expose({ name: "counterparties" })
  counterparties: CounterpartiesDto;

  @Expose({ name: "transactionSubStatus" })
  transactionSubStatus: string;

  @Expose({ name: "messageKey" })
  messageKey: string;

  @Expose({ name: "eventId" })
  eventId: string;

  @Expose({ name: "appl_date" })
  applDate: string;

  @Expose({ name: "company_id" })
  companyId: string;

  @Expose({ name: "transactionStatus" })
  transactionStatus: string;

  @Expose({ name: "productStatus" })
  productStatus: string;

  @Expose({ name: "inp_user_id" })
  inpUserId: string;

  @Expose({ name: "message" })
  message: string;

  @Expose({ name: "transactionType" })
  transactionType: string;

  @Expose({ name: "applicant_abbv_name" })
  applicantAbbvName: string;

  @Expose({ name: "tnx_stat_code" })
  tnxStatCode: TransactionStatusCodeEnum;

  @Expose({ name: "sub_tnx_stat_code" })
  subTnxStatCode: SubTransactionStatusCodeEnum;

  @Expose({ name: "prod_stat_code" })
  prodStatCode: ProductStatusCodeEnum;

  @Expose({ name: "company_name" })
  companyName: string;

  idempotencyKey: string;

  @Expose({ name: "beneficiary_mode" })
  beneficiaryMode: string;

  @Expose({ name: "bene_email_1" })
  beneEmail1: string;

  @Expose({ name: "bene_email_2" })
  beneEmail2: string;

  @Expose({ name: "pre_approved" })
  preApproved: string;

  @Expose({ name: "pre_approved_status" })
  preApprovedStatus: string;

  @Expose({ name: "ft_amt" })
  ftAmt: string;

  @Expose({ name: "ft_cur_code" })
  ftCurCode: string;

  @Expose({ name: "td_amt" })
  tdAmt: string;

  @Expose({ name: "td_cur_code" })
  tdCurCode: string;

  @Type(() => CommentDto)
  @Expose({ name: "bo_comment" })
  boComment: CommentDto;

  @Type(() => CommentDto)
  @Expose({ name: "return_comments" })
  returnComment: CommentDto;

  @Expose({ name: "bo_release_dttm" })
  boReleaseDttm: string;

  @Expose({ name: "value_date" })
  valueDate: string;

  @Expose()
  @FlattenArrayTransformer(FccAttachmentDto, ["attachments", "attachment"])
  attachments: FccAttachmentDto[];
}
