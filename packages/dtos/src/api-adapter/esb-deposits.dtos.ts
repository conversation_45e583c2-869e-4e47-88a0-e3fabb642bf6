import { Expose, plainToInstance, Transform, Type } from "class-transformer";
import { castArray, keyBy } from "lodash";
import { IsArray, ValidateNested } from "class-validator";

export class CurrencyCodeDto {
  @Expose({ name: "CurCodeValue" })
  curCodeValue: string;
}

export class AmountDto {
  @Expose({ name: "Amt" })
  amount: number;

  @Expose({ name: "CurCode" })
  @Transform(({ value }) => value?.CurCodeValue)
  currencyCode: string;
}

export class EsbDepositListItemDto {
  @Expose({ name: "ProductIdent" })
  productIdent: string;

  @Expose({ name: "DepositProductType" })
  depositProductType: string;

  @Expose({ name: "MaturityDt" })
  maturityDt: string;

  @Expose({ name: "OriginationDt" })
  originationDt: string;

  @Expose({ name: "Amt" })
  @Type(() => AmountDto)
  amt: AmountDto;

  @Expose({ name: "InterestRate" })
  interestRate: number;

  @Expose({ name: "AvailableAmt" })
  @Type(() => AmountDto)
  availableAmt: AmountDto;

  @Expose({ name: "TotalHeldAmt" })
  @Type(() => AmountDto)
  totalHeldAmt: AmountDto;

  @Expose({ name: "RenewalOption" })
  renewalOption: boolean;

  @Expose({ name: "Tenor" })
  tenor: string;

  @Expose({ name: "LiabilityCode" })
  liabilityCode: string;

  @Expose({ name: "Status" })
  status: string;
}
export class InterestDto {
  @Expose({ name: "IntFreq" })
  @Transform(({ value }) => value)
  interestFrequency: string;

  @Expose({ name: "IntRate" })
  interestRate: string;

  @Expose({ name: "IntDueDt" })
  nextInterestDate: string;
}

export class HoldInfoDto {
  @Expose({ name: "HoldAmt" })
  @Transform(({ value }) => {
    // console.log(value);
    return value?.Amt;
  })
  amount: number;

  @Expose({ name: "HoldReason" })
  reason: string;
}

export class HoldInfoListDto {
  @Expose({ name: "HoldInfo" })
  @Type(() => HoldInfoDto)
  holdInfo: HoldInfoDto[];
}

export class TDProdInfoDto {
  @Expose({ name: "Capitalise" })
  capitalise: string;

  @Type(() => InterestDto)
  @Expose({ name: "Interest" })
  interest: InterestDto;
}

export class DepositProdInfoDto {
  @Type(() => TDProdInfoDto)
  @Expose({ name: "TDProdInfo" })
  tdProdInfo: TDProdInfoDto;
}

export class DepositBalanceDto {
  @Expose({ name: "BookBalance" })
  @Transform(({ value }) => {
    return plainToInstance(AmountDto, value.CurAmt, {
      excludeExtraneousValues: true,
    });
  })
  bookBalance: AmountDto;

  @Expose({ name: "Ledger" })
  @Transform(({ value }) => {
    return plainToInstance(AmountDto, value.CurAmt, {
      excludeExtraneousValues: true,
    });
  })
  ledgerBalance: AmountDto;

  @Expose({ name: "TotalHeld" })
  @Transform(({ value }) => {
    return plainToInstance(AmountDto, value.CurAmt, {
      excludeExtraneousValues: true,
    });
  })
  totalHeld: AmountDto;

  @Expose({ name: "Avail" })
  @Transform(({ value }) => {
    return plainToInstance(AmountDto, value.CurAmt, {
      excludeExtraneousValues: true,
    });
  })
  availBalance: AmountDto;

  @Expose({ name: "TotalInterest" })
  @Transform(({ value }) => {
    return plainToInstance(AmountDto, value.CurAmt, {
      excludeExtraneousValues: true,
    });
  })
  interestBalance: AmountDto;
}

export class EsbDepositDetailsItemDto {
  @Expose({ name: "PartyIdent" })
  partyIdent: string;

  @Expose({ name: "DepositProductTypeDesc" })
  @Transform(
    ({ value }) => {
      const [code, name, description] = String(value).split("&");
      const length = String(value).split("&").length;
      if (length > 2) {
        return {
          productCode: code?.trim(),
          productSegment: name?.trim(),
          description: description?.trim(),
        };
      } else {
        return {
          productCode: code?.trim(),
          description: name?.trim(),
        };
      }
    },
    { toClassOnly: true },
  )
  productInfo: {
    productCode: string;
    productName: string;
    productDescription: string;
  };

  @Expose({ name: "OriginationDt" })
  originationDate: string;

  @Expose({ name: "MaturityDt" })
  maturityDate: string;

  @Expose({ name: "CurCode" })
  @Transform(({ value }) => value.CurCodeValue)
  currencyCode: string;

  @Expose({ name: "DepositProdTitle" })
  depositProdTitle: string;

  @Expose({ name: "Status" })
  status: string;

  @Expose({ name: "DepositProdBal" })
  @Transform(({ value }) => {
    return plainToInstance(
      DepositBalanceDto,
      keyBy(castArray(value), "BalType"),
      {
        excludeExtraneousValues: true,
      },
    );
  })
  balances: DepositBalanceDto;

  @Type(() => AmountDto)
  @Expose({ name: "TotalInterest" })
  totalInterest: AmountDto;

  @Expose({ name: "Tenor" })
  tenor: string;

  @Type(() => InterestDto)
  @Expose({ name: "Interest" })
  interest: InterestDto;

  @Expose({ name: "DrawDnAcct" })
  drawAccount: string;

  @Expose({ name: "PrincLiqAcct" })
  principleLiquidationAccount: string;

  @Expose({ name: "InterestLiqAcct" })
  interestLiquidationAccount: string;

  @Expose({ name: "HoldInfoList" })
  @Transform(({ value }) => {
    // ensure we always end up with an array of HoldInfo
    if (value?.HoldInfo) {
      const arr = castArray(value?.HoldInfo);
      return plainToInstance(HoldInfoDto, arr, {
        excludeExtraneousValues: true,
      });
    }
  })
  @IsArray()
  @ValidateNested({ each: true })
  collateral: HoldInfoDto[];

  @Expose({ name: "RenewalOption" })
  renewalOption: boolean;

  @Type(() => DepositProdInfoDto)
  @Expose({ name: "DepositProdInfo" })
  depositProdInfo: DepositProdInfoDto;

  @Expose({ name: "ProductIdent" })
  productIdent: string;

  @Expose({ name: "LatestOriginationDt" })
  latestInterestDate: string;
}

export class OverrideReturnDto {
  @Expose({ name: "OvrdExceptionDesc" })
  overrideExceptionDescription: string;
}

export class BreakDepositProductFulfillmentDto {
  @Expose({ name: "DepositProdIdent" })
  depositProductIdentifier: string;

  @Expose({ name: "OverrideReturn" })
  @Transform(({ value }) => {
    return value.OvrdExceptionDesc;
  })
  overrideReturn: OverrideReturnDto;

  @Expose({ name: "OFSReturn" })
  ofsReturn: string;

  // Parsed fields from OFSReturn string (if you want to extract specific values)
  @Expose()
  @Transform(({ obj }) => {
    const ofsReturn = obj.OFSReturn || "";
    console.log(ofsReturn);
    const match = ofsReturn.match(/CUSTOMER\.ID:1:1=([^,]+)/);
    return match ? match[1] : null;
  })
  customerId: string;

  @Expose()
  @Transform(({ obj }) => {
    const ofsReturn = obj.OFSReturn || "";
    const match = ofsReturn.match(/CURRENCY:1:1=([^,]+)/);
    return match ? match[1] : null;
  })
  currency: string;

  @Expose()
  @Transform(({ obj }) => {
    const ofsReturn = obj.OFSReturn || "";
    const match = ofsReturn.match(/PRIN.INCREASE:1:1=([^,]+)/);
    return match ? parseFloat(match[1]) : null;
  })
  redemptionAmount: number;

  @Expose()
  @Transform(({ obj }) => {
    const ofsReturn = obj.OFSReturn || "";
    const match = ofsReturn.match(/CHARGE.AMOUNT:1:1=([^,]+)/);
    return match ? match[1] : null;
  })
  penaltyAmount: string;
}
