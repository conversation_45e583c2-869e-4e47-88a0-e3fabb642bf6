import { Expose, Type } from "class-transformer";
import {
  DecodeHtmlTransformer,
  MoneyTransformer,
  TransformDateTimeFromTimestamp,
} from "@helpers";
import {
  ProductCodeEnum,
  ProductStatusCodeEnum,
  SubTransactionStatusCodeEnum,
  TransactionStatusCodeEnum,
} from "@enums";
import { DateTimeRecord } from "@types";
import { defaultConfig } from "@modules";

export class TransactionListItemDto {
  @Expose({ name: "ref_id" })
  refId: string;

  @Expose({ name: "tnx_id" })
  tnxId: string;

  @Expose({ name: "product_code_val" })
  productCode: ProductCodeEnum;

  @Expose({ name: "sub_product_code_val" })
  subProductCode: string;

  // FT
  @Expose({ name: "tnx_amt" })
  tnxAmt: string;

  @Expose({ name: "tnx_cur_code" })
  tnxCurCode: string;

  @Expose({ name: "applicant_act_no" })
  applicantActNo: string;

  @Expose({ name: "applicant_act_name" })
  applicantActName: string;

  @Expose({ name: "applicant_act_cur_code" })
  applicantActCurCode: string;

  // SE
  @Expose({ name: "ObjectDataString@upload_file_type" })
  @DecodeHtmlTransformer()
  seFileType: string;

  //Status
  @Expose({ name: "tnx_stat_code_val" })
  tnxStatCode: TransactionStatusCodeEnum;

  @Expose({ name: "sub_tnx_stat_code_val" })
  subTnxStatCode: SubTransactionStatusCodeEnum;

  @Expose({ name: "prod_stat_code_val" })
  prodStatCode: ProductStatusCodeEnum;

  @Expose({ name: "tnx_type_code" })
  tnxTypeCode: string;

  @Expose({ name: "inp_dttm" })
  @DecodeHtmlTransformer()
  inpDttm: string;

  @Expose({ name: "Inputter@login_id" })
  inputterLoginId: string;

  @Expose({ name: "Inputter@first_name" })
  inputterFirstName: string;

  @Expose({ name: "Inputter@last_name" })
  inputterLastName: string;

  @Expose({ name: "box_ref" })
  boxRef: string;

  @Expose({ name: "buyer_name" })
  buyerName: string;

  @Expose({ name: "seller_name" })
  sellerName: string;

  @Expose({ name: "amd_no" })
  amdNo: string;

  @Expose({ name: "release_dttm" })
  @DecodeHtmlTransformer()
  releaseDttm: string;

  @Expose({ name: "full_name_releaser" })
  fullNameReleaser: string;

  @Expose({ name: "Releaser@login_id" })
  releaserLoginId: string;

  @Expose({ name: "Releaser@last_name" })
  releaserLastName: string;

  @Expose({ name: "bic_code" })
  bicCode: string;

  @Expose({ name: "fscm_program_code" })
  fscmProgramCode: string;

  @Expose({ name: "child_product_code" })
  childProductCode: string;

  @Expose({ name: "sub_tnx_type_code" })
  subTnxTypeCode: string;

  @Expose({ name: "full_type" })
  fullType: string;

  @Expose({ name: "tnx_date" })
  @TransformDateTimeFromTimestamp(defaultConfig().timezone)
  tnxDate: DateTimeRecord;
}

class TransactionValidationStepDto {
  @Expose()
  date: string;

  @Expose()
  fullName: string;

  @Expose({ name: "description" })
  role: string;
}

class TransactionJourneyItemDto {
  @Expose({ name: "tnx_stat_code" })
  tnxStatCode: TransactionStatusCodeEnum;

  @Expose({ name: "tnx_id" })
  tnxId: string;

  @Expose()
  @Type(() => TransactionValidationStepDto)
  validationSteps: TransactionValidationStepDto[];

  @Expose({ name: "prod_stat_code" })
  prodStatCode: ProductStatusCodeEnum;

  @Expose({ name: "bo_release_dttm" })
  boReleaseDttm: string;
}

export class TransactionJourneyResponseDto {
  @Expose()
  @Type(() => TransactionJourneyItemDto)
  items: TransactionJourneyItemDto[];
}
