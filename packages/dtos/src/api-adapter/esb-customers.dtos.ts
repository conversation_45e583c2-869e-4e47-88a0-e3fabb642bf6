import { StringConcatTransformer } from "@helpers";
import { Expose, Transform, Type } from "class-transformer";

class LegalIdentityDto {
  @Expose({ name: "LegalIdentType" })
  type: string;

  @Expose({ name: "LegalIdentValue" })
  value: string;

  @Expose({ name: "LegalIdentIssueDt" })
  issueDate: string;

  @Expose({ name: "LegalIdentExpDt" })
  expiryDate: string;

  @Expose({ name: "LegalIdentIssueAuthority" })
  authority: string;
}

class PartyPreferencesDto {
  @Expose({ name: "Language" })
  language: string;

  @Expose({ name: "SMSAlert" })
  smsAlert: string;
}

class IncomeDto {
  @Expose({ name: "Source" })
  source: string;

  @Expose({ name: "PaymentMethod" })
  paymentMethod: string;
}

class FinancialDataDto {
  @Expose({ name: "TotalExpenses" })
  totalExpenses: number;

  @Expose({ name: "EstMonthlyDpst" })
  estimatedMonthlyDeposit: number;

  @Expose({ name: "EstMonthlyTrans" })
  estimatedMonthlyTransactions: string;

  @Type(() => IncomeDto)
  @Expose({ name: "Income" })
  income: IncomeDto;

  @Expose({ name: "TotalIncome" })
  totalIncome: number;
}

class PhoneDto {
  @Expose({ name: "ContactValue" })
  value: string;

  @Expose({ name: "ContactType" })
  type: string;
}

class SalaryDto {
  @Expose({ name: "Amt" })
  amount: number;

  // @Transform(({ value }) => value?.CurCodeValue)
  // currencyCode: string;

  @Expose({ name: "CurCode" })
  currencyCode: any;
}

class TimeFrameDto {
  @Expose({ name: "StartDt" })
  startDate: string;
}

class EmployerInfoDto {
  @Expose({ name: "EmploymentStatus" })
  employmentStatus: string;

  @Expose({ name: "EmployerName" })
  employerName: string;

  @Expose({ name: "EmployerAddr" })
  employerAddress: string;

  @Type(() => SalaryDto)
  @Expose({ name: "Salary" })
  salary: SalaryDto;

  @Type(() => TimeFrameDto)
  @Expose({ name: "TimeFrame" })
  timeFrame: TimeFrameDto;
}

class EmploymentDto {
  @Type(() => EmployerInfoDto)
  @Expose({ name: "EmployerInfo" })
  employerInfo: EmployerInfoDto;

  @Expose({ name: "EmployName" })
  arabicEmployerName: string;

  @Expose({ name: "IncomeRange" })
  incomeRange: string;

  @Expose({ name: "Proffession" })
  professionCode: string;

  @Expose({ name: "AddressAr" })
  arabicWorkAddress: string;
}

class PersonDataDto {
  @Expose({ name: "FirstNameEn" })
  firstNameEn: string;

  @Expose({ name: "LastNameEn" })
  lastNameEn: string;

  @Expose({ name: "FullNameEn" })
  @StringConcatTransformer()
  fullNameEn: string;

  @Expose({ name: "ShortName" })
  @StringConcatTransformer()
  shortName: string;

  @Expose({ name: "NameAr" })
  @StringConcatTransformer()
  arabicName: string;

  @Expose({ name: "AddressAr" })
  @StringConcatTransformer()
  arabicAddress: string;

  @Expose({ name: "BirthDt" })
  birthDate: string;

  @Expose({ name: "Gender" })
  gender: string;

  @Expose({ name: "MaritalStat" })
  maritalStatus: string;

  @Expose({ name: "Occupation" })
  occupation: string;

  @Expose({ name: "Nationality" })
  nationality: string;

  @Expose({ name: "OtherNationality" })
  secondaryNationality: string;

  @Expose({ name: "HomeAddr" })
  @StringConcatTransformer()
  homeAddress: string;

  @Expose({ name: "HomeDistrict" })
  district: string;

  @Expose({ name: "EmailAddrList" })
  email: string;

  @Expose({ name: "ResidenceCountry" })
  residenceCountry: string;

  @Type(() => PhoneDto)
  @Expose({ name: "PhoneList" })
  phones: PhoneDto[];

  @Expose({ name: "EducationLevel" })
  educationLevel: string;

  @Type(() => EmploymentDto)
  @Expose({ name: "Employment" })
  employment: EmploymentDto;

  @Expose({ name: "HomeCountry" })
  homeCountry: string;

  @Expose({ name: "PublicFigure" })
  isPublicFigure: boolean;

  @Expose({ name: "FifthActivityCode" })
  fifthActivityCode: boolean;
}

class RelationshipManagerDto {
  @Expose({ name: "RelationshipMgrIdent" })
  id: string;

  @Expose({ name: "Desc" })
  name: string;
}

class KycNextReviewInfoDto {
  @Expose({ name: "NextReviewType" })
  type: string;

  @Expose({ name: "NextReviewDate" })
  date: string;
}

class AmlInfoDto {
  @Expose({ name: "AMLCheck" })
  checked: string;

  @Expose({ name: "AmlResult" })
  result: string;
}

class KYCDocInfoDto {
  @Expose({ name: "ContactDt" })
  contactDate: string;

  @Expose({ name: "KycCommpleteFlag" })
  isComplete: string;

  @Expose({ name: "KycReviewDt" })
  reviewDate: string;

  @Type(() => KycNextReviewInfoDto)
  @Expose({ name: "KycNextReviewInfo" })
  nextReview: KycNextReviewInfoDto;

  @Type(() => AmlInfoDto)
  @Expose({ name: "AMLInfo" })
  aml: AmlInfoDto;

  @Expose({ name: "CompanyLetterInd" })
  hasCompanyLetter: boolean;

  @Expose({ name: "SpecialNatureAcctInd" })
  specialNatureAccount: boolean;

  @Expose({ name: "ComplianceApprInd" })
  complianceApproved: boolean;

  @Expose({ name: "NagativeChkInd" })
  hasNegativeCheck: boolean;
}

class RestrictionDto {
  @Expose({ name: "TrnSubType" })
  code: number;

  @Expose({ name: "LimitMemolist" })
  @Transform(({ value }) => value?.Memo)
  memos: string[];
}

class MailAddrDto {
  @Expose({ name: "Address" })
  @StringConcatTransformer(", ")
  address: string;

  @Expose({ name: "District" })
  district: string;

  @Expose({ name: "Country" })
  country: string;

  @Expose({ name: "City" })
  city: string;

  @Expose({ name: "Memo" })
  @StringConcatTransformer(", ")
  memos: string[];
}

export class CountryOperatorsDto {
  @Expose({ name: "CountryName" })
  countryName: string;
}

class ResidenceInfoDto {
  @Expose({ name: "Status" })
  status: string;

  @Expose({ name: "StartDate" })
  startDate: string;
}

export class AmountCurDto {
  @Expose({ name: "Amt" })
  amt: number;

  @Expose({ name: "CurCode" })
  @Transform(({ value }) => value?.CurCodeValue)
  currencyCode: string;
}

class CorporateFinancialsDto {
  @Expose({ name: "PaidCapital" })
  @Type(() => AmountCurDto)
  paidCapital: AmountCurDto;

  @Expose({ name: "AnnualSales" })
  @Type(() => AmountCurDto)
  annualSales: AmountCurDto;
}

class CorporatePartyInfoDto {
  @Expose({ name: "LegalStatus" })
  legalStatus: number;

  @Expose({ name: "CommercialRegExpiryDate" })
  commercialRegExpiryDate: string;

  @Expose({ name: "ComRegLastExt" })
  comRegLastExt: string;

  @Expose({ name: "commRegOffice" })
  commRegOffice: number;

  @Expose({ name: "CompanyType" })
  companyType: string;

  @Expose({ name: "GoverningLaw" })
  governingLaw: string;

  @Expose({ name: "InstitutNation" })
  institutNation: number;

  @Expose({ name: "GovernateCode" })
  governateCode: number;

  @Expose({ name: "WomenContribution" })
  womenContribution: string;

  @Expose({ name: "CountryOperators" })
  @Type(() => CountryOperatorsDto)
  countryOperators: CountryOperatorsDto;

  @Expose({ name: "EmployeesNum" })
  employeesNum: number;

  @Expose({ name: "FourthActivity" })
  fourthActivity: number;

  @Expose({ name: "CorporateFinancials" })
  @Type(() => CorporateFinancialsDto)
  corporateFinancials: CorporateFinancialsDto;
}

class SignatoryDto {
  @Expose({ name: "SignatoryPosition" })
  signatoryPosition: number;

  @Expose({ name: "FullName" })
  fullName: string;

  @Expose({ name: "Nationality" })
  nationality: string;

  @Expose({ name: "LegalIdentity" })
  @Type(() => LegalIdentityDto)
  legalIdentity: LegalIdentityDto;
}

class OrgPartyDataDto {
  @Expose({ name: "OrgRegNo" })
  orgRegNo: number;

  @Expose({ name: "OrgRegIssueDt" })
  orgRegIssueDt: string;

  @Expose({ name: "OrgRegExpDt" })
  orgRegExpDt: string;
}

class PartyRoleRemarksDto {
  @Expose({ name: "Memo" })
  memo: string;
}

class RelationExtraInfoDto {
  @Expose({ name: "PartyRoleRemarks" })
  @Type(() => PartyRoleRemarksDto)
  partyRoleRemarks: PartyRoleRemarksDto;
}

class RelatedPartyDto {
  @Expose({ name: "PartyIdent" })
  partyIdent: number;

  @Expose({ name: "RelatedPartyCode" })
  relatedPartyCode: number;

  @Expose({ name: "RelationExtraInfo" })
  @Type(() => RelationExtraInfoDto)
  relationExtraInfo: RelationExtraInfoDto;
}

class SubscriptionInfoDto {
  @Expose({ name: "Code" })
  code: number;

  @Expose({ name: "Description" })
  description: number;
}

export class EsbCustomerDetailsDto {
  @Expose({ name: "PartyIdent" })
  userId: string;

  @Expose({ name: "Mnemonic" })
  mnemonic: string;

  @Expose({ name: "OpeningDt" })
  openingDate: string;

  @Expose({ name: "PartyStatus" })
  status: string;

  @Type(() => LegalIdentityDto)
  @Expose({ name: "LegalIdentity" })
  legalIdentity: LegalIdentityDto;

  @Type(() => PartyPreferencesDto)
  @Expose({ name: "PartyPref" })
  preferences: PartyPreferencesDto;

  @Type(() => FinancialDataDto)
  @Expose({ name: "FinancialData" })
  financial: FinancialDataDto;

  @Type(() => PersonDataDto)
  @Expose({ name: "PersonData" })
  person: PersonDataDto;

  @Expose({ name: "PreferredChannel" })
  preferredChannel: string;

  @Expose({ name: "BranchId" })
  branchId: string;

  @Expose({ name: "BranchCode" })
  branchCode: string;

  @Expose({ name: "Target" })
  target: string;

  @Expose({ name: "PartySector" })
  sector: string;

  @Expose({ name: "PartyIndustry" })
  industry: string;

  @Type(() => RelationshipManagerDto)
  @Expose({ name: "RelationshipMgr" })
  relationshipManager: RelationshipManagerDto;

  @Expose({ name: "PartyType" })
  typeCode: string;

  @Expose({ name: "ServiceAgrrementInd" })
  hasServiceAgreement: boolean;

  @Expose({ name: "OTPAggrementInd" })
  hasOtpAgreement: boolean;

  @Expose({ name: "LoanInd" })
  hasLoan: boolean;

  @Expose({ name: "PartyMIDASIdent" })
  midasId: string;

  @Expose({ name: "RetailPartyGroup" })
  retailGroup: string;

  @Type(() => KYCDocInfoDto)
  @Expose({ name: "KYCDocInfo" })
  kyc: KYCDocInfoDto;

  @Expose({ name: "IssueCheqInd" })
  canIssueCheque: boolean;

  @Expose({ name: "ChildGender" })
  childGender: string;

  @Expose({ name: "PartyOpenDt" })
  openDate: string;

  @Type(() => RestrictionDto)
  @Expose({ name: "PartyTrnLimit" })
  restriction: RestrictionDto;

  @Type(() => MailAddrDto)
  @Expose({ name: "MailAddr" })
  mailingAddress: MailAddrDto;

  @Type(() => ResidenceInfoDto)
  @Expose({ name: "ResidenceInfo" })
  residence: ResidenceInfoDto;

  @Expose({ name: "CIBRiskLevel" })
  riskLevel: string;

  @Expose({ name: "PoliticalExposed" })
  isPoliticallyExposed: string;

  @Expose({ name: "ComplexOwnership" })
  complexOwnership: string;

  @Expose({ name: "CorporatePartyInfo" })
  @Type(() => CorporatePartyInfoDto)
  corporatePartyInfo: CorporatePartyInfoDto;

  @Expose({ name: "Signatory" })
  @Type(() => SignatoryDto)
  signatory: SignatoryDto;

  @Expose({ name: "RelatedParty" })
  @Type(() => RelatedPartyDto)
  relatedParty: RelatedPartyDto[];

  @Expose({ name: "TaxIdent" })
  taxIdent: number;

  @Expose({ name: "OrgPartyData" })
  @Type(() => OrgPartyDataDto)
  orgPartyData: OrgPartyDataDto;

  @Expose({ name: "SubscriptionInfo" })
  @Type(() => SubscriptionInfoDto)
  subscriptionInfo: SubscriptionInfoDto[];

  @Expose({ name: "ReverseRelationCode" })
  reverseRelationCode: number[];
}
