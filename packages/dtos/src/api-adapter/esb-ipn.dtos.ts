import { Expose } from "class-transformer";

export class EsbBankListListItemDto {
  @Expose({ name: "NameAr" })
  nameAr: string;
  @Expose({ name: "NameEn" })
  nameEn: string;
  @Expose({ name: "BankID" })
  bankId: string;
  @Expose({ name: "IsOperator" })
  isOperator: string;
  @Expose({ name: "Version" })
  version: string;
  @Expose({ name: "isActive" })
  isActive: string;
  @Expose({ name: "SpocInfo" })
  spocInfo: string;
}
