import { Expose, plainToInstance, Transform, Type } from "class-transformer";
import {
  IsArray,
  IsDateString,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from "class-validator";

import { ArrayTransformer } from "@helpers";
import { castArray, keyBy } from "lodash";

export class CurrencyCode {
  @Expose({ name: "CurCodeValue" })
  @IsString()
  curCodeValue: string;
}

export class Amount {
  @Expose({ name: "Amt" })
  @IsNumber()
  amt: number;
}

export class AccountBalance {
  @Expose({ name: "BalType" })
  @IsString()
  balType: string;

  @Expose({ name: "CurAmt" })
  @ValidateNested()
  @Type(() => Amount)
  curAmt: Amount;
}

export class AccountStatus {
  @Expose({ name: "AcctStatusCode" })
  @IsString()
  accountStatusCode: string;
}

export class AcctTrnLimitItemDto {
  @Expose({ name: "CurAmt" })
  @Transform(({ value }) => value?.Amt)
  currentAmount: number;

  @Expose({ name: "ExpDt" })
  expiryDate: string;

  @Expose({ name: "LimitMemo" })
  limitMemo: string;

  @Expose({ name: "TrnSubType" })
  trnSubType: string;

  @Expose({ name: "TrnType" })
  trnType: string;
}

export class AccountTrnLimitDto {
  @Expose({ name: "OverDraft" })
  @Type(() => AcctTrnLimitItemDto)
  overdraft: AcctTrnLimitItemDto;

  @Expose({ name: "Posting" })
  @Type(() => AcctTrnLimitItemDto)
  postingRestriction: AcctTrnLimitItemDto;

  @Expose({ name: "AccountLimit" })
  @Type(() => AcctTrnLimitItemDto)
  accountLimit: AcctTrnLimitItemDto;
}

export class InterestRate {
  @Expose({ name: "IntRate" })
  @IsOptional()
  @IsNumber()
  intRate?: number;

  @Expose({ name: "IntFreq" })
  @IsOptional()
  @IsString()
  intFreq?: string;

  @Expose({ name: "IntRateType" })
  @IsString()
  intRateType: string;
}

export class Duration {
  @Expose({ name: "Unit" })
  @IsString()
  unit: string;

  @Expose({ name: "Desc" })
  @IsString()
  desc: string;
}

export class StatementTimeFrame {
  @Expose({ name: "Duration" })
  @ValidateNested()
  @Type(() => Duration)
  duration: Duration;
}

export class RelationshipManager {
  @Expose({ name: "RelationshipMgrIdent" })
  @IsString()
  relationshipMgrIdent: string;
}

export class AccountBalanceDto {
  @Expose({ name: "BookBalance" })
  @Transform(({ value }) => value?.CurAmt?.Amt)
  bookBalance: number;

  @Expose({ name: "ClearBalance" })
  @Transform(({ value }) => value?.CurAmt?.Amt)
  clearBalance: number;

  @Expose({ name: "Avail" })
  @Transform(({ value }) => value?.CurAmt?.Amt)
  available: number;

  @Expose({ name: "OpeningActual" })
  @Transform(({ value }) => value?.CurAmt?.Amt)
  openingActual: number;

  @Expose({ name: "AvailLimit" })
  @Transform(({ value }) => value?.CurAmt?.Amt)
  availableLimit: number;
}

export class EsbAccountDetailsDto {
  @Expose({ name: "PartyIdent" })
  @IsNumber()
  partyIdent: number;

  @Expose({ name: "AcctNum" })
  @IsNumber()
  accountNumber: number;

  @Expose({ name: "AcctIBAN" })
  @IsString()
  accountIban: string;

  @Expose({ name: "OpenDt" })
  @IsDateString()
  openDate: string;

  @Expose({ name: "CurCode" })
  @Transform(({ value }) => value?.CurCodeValue)
  curCode: string;

  @Expose({ name: "AcctTitle" })
  @IsString()
  accountTitle: string;

  @Expose({ name: "AcctCat" })
  @IsNumber()
  categoryCode: number;

  @Expose({ name: "BranchId" })
  @IsString()
  branchId: string;

  @Expose({ name: "ProductIdent" })
  @IsNumber()
  productIdent: number;

  @Expose({ name: "AcctType" })
  @IsString()
  accountType: string;

  @Expose({ name: "AcctTypeDesc" })
  @IsString()
  @Transform(({ value }) => {
    if (typeof value === "string") {
      const [, desc] = value.split("&");
      return desc?.trim();
    }
    return value;
  })
  accountTypeDesc: string;

  @Expose({ name: "AcctProdType" })
  @Transform(
    ({ value }) => {
      const [code, name] = String(value).split("&");
      return {
        productCode: Number(code?.trim()),
        productName: name?.trim(),
      };
    },
    { toClassOnly: true }
  )
  productInfo: { productCode: number; productName: string };

  @Expose({ name: "AcctBal" })
  @Transform(({ value }) => {
    return plainToInstance(
      AccountBalanceDto,
      keyBy(castArray(value), "BalType"),
      {
        excludeExtraneousValues: true,
      }
    );
  })
  balances: AccountBalanceDto;

  @Expose({ name: "AcctStatus" })
  @ValidateNested()
  @Type(() => AccountStatus)
  accountStatus: AccountStatus;

  @Expose({ name: "Interest" })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => InterestRate)
  interest: InterestRate[];

  @Expose({ name: "StmtTimeFrame" })
  @ValidateNested()
  @Type(() => StatementTimeFrame)
  stmtTimeFrame: StatementTimeFrame;

  @Expose({ name: "RelationshipMgr" })
  @ValidateNested()
  @Type(() => RelationshipManager)
  relationshipMgr: RelationshipManager;

  @Expose({ name: "LimitDesc" })
  @IsString()
  limitDesc: string;

  @Expose()
  @Type(() => Date)
  expiryDate: any;

  @Expose({ name: "AcctTrnLimit" })
  @Transform(({ value }) => {
    return plainToInstance(AccountTrnLimitDto, keyBy(value, "TrnType"), {
      excludeExtraneousValues: true,
    });
  })
  accountTrnLimit: AccountTrnLimitDto;

  // to be override from the formatter in order to use the final formatted object
  @Expose()
  isOverdraft: boolean;
}

export class EsbAccountListItemDto extends EsbAccountDetailsDto {}
