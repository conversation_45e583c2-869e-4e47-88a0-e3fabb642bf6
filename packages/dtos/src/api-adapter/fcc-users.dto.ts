import { DecodeHtmlTransformer } from "@helpers";
import { Expose, Type } from "class-transformer";
import {
  IsArray,
  IsBoolean,
  IsEmail,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateNested,
} from "class-validator";

class CustomerReferenceDto {
  @Expose()
  name: string;

  @Expose()
  ID: string;
}

class LegalInformationDto {
  @Expose()
  identifier: string;

  @Expose()
  number: string;

  @Expose()
  country: string;
}

class AddressDto {
  @Expose()
  line1: string;
}

class UserPreferencesDto {
  @Expose()
  @DecodeHtmlTransformer()
  timezone?: string;

  @Expose()
  correspondenceLanguage?: string;

  @Expose()
  preferredLanguage?: string;

  @Expose()
  baseCurrency?: string;

  @Expose()
  defaultAccount?: string;
}

class ContactInformationDto {
  @Expose()
  phone?: string;

  @Expose()
  @DecodeHtmlTransformer()
  email?: string;
}

class EntityDto {
  @Expose()
  id: string;

  @Expose()
  name: string;

  @Expose()
  abbreviation: string;

  @Expose()
  references: string[];
}

export class FccUserDetailsDto {
  @Expose()
  @Type(() => CustomerReferenceDto)
  customerReferences: CustomerReferenceDto[];

  @Expose()
  companyId: string;

  @Expose()
  id: string;

  @Expose()
  firstName: string;

  @Expose()
  lastName: string;

  @Expose()
  status: string;

  @Expose()
  @Type(() => LegalInformationDto)
  legalinformation: LegalInformationDto;

  @Expose()
  @Type(() => AddressDto)
  address: AddressDto;

  @Expose()
  @IsString()
  authenticationMode: string;

  @Expose()
  @Type(() => UserPreferencesDto)
  userPreferences: UserPreferencesDto;

  @Expose()
  @Type(() => ContactInformationDto)
  contactInformation: ContactInformationDto;

  @Expose()
  receivePendingNotification: boolean;

  @Expose()
  userLastLoginDateTime: string;

  @Expose()
  userLastFailedLoginDateTime: string;

  @Expose()
  parentBankShortName: string;

  @Expose()
  userId: string;

  @Expose()
  @Type(() => EntityDto)
  entities: EntityDto[];
}

export type UserDetailsDto = FccUserDetailsDto; // to be removed
