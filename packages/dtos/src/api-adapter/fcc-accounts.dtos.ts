import { Expose, Type } from "class-transformer";
import { IsString } from "class-validator";
import { MoneyTransformer } from "@helpers";

export class FccAccountListDto {
  @Expose({ name: "_meta" })
  meta: any;

  @Expose({ name: "items" })
  @Type(() => FccAccountListItemDto)
  items: FccAccountListItemDto[];
}

export class FccAccountListItemDto {
  @Expose({ name: "id" })
  @IsString()
  accountId: string;

  @Expose({ name: "nickname" })
  @IsString()
  nickname: string;

  @Expose({ name: "accountNumber" })
  @IsString()
  accountNumber: string;

  @Expose({ name: "accountIban" })
  @IsString()
  accountIban: string;

  @Expose({ name: "type" })
  @IsString()
  accountType: string;

  @Expose({ name: "availableBalance" })
  @IsString()
  @MoneyTransformer()
  availableBalance: number;

  @Expose({ name: "availableBalanceEquivalent" })
  @IsString()
  @MoneyTransformer()
  availableBalanceEquivalent: number;

  @Expose({ name: "equivalentCurrency" })
  @IsString()
  equivalentCurrency: string;

  @Expose({ name: "ledgerBalance" })
  @IsString()
  @MoneyTransformer()
  ledgerBalance: number;

  @Expose({ name: "ledgerBalanceEquivalent" })
  @IsString()
  @MoneyTransformer()
  ledgerBalanceEquivalent: number;

  @Expose({ name: "currency" })
  @IsString()
  currency: string;

  @Expose({ name: "overdraftLimit" })
  @IsString()
  @MoneyTransformer()
  overdraftLimit: number;

  @Expose({ name: "overdraftLimitEquivalent" })
  @IsString()
  @MoneyTransformer()
  overdraftLimitEquivalent: number;

  @Expose({ name: "interestRateDebit" })
  interestRateDebit: string;

  @Expose({ name: "interestRateCredit" })
  interestRateCredit: string;

  @Expose({ name: "onlineActualBalance" })
  @IsString()
  @MoneyTransformer()
  onlineActualBalance: number;

  @Expose({ name: "onlineActualBalanceEquivalent" })
  @IsString()
  @MoneyTransformer()
  onlineActualBalanceEquivalent: number;

  @Expose({ name: "heldAmount" })
  @IsString()
  @MoneyTransformer()
  heldAmount: number;

  @Expose({ name: "heldAmountEquivalent" })
  @IsString()
  @MoneyTransformer()
  heldAmountEquivalent: number;

  @Expose({ name: "tdStartDate" })
  tdStartDate: any;
  @Expose({ name: "tdEndDate" })
  tdEndDate: any;
  @Expose({ name: "principalAmount" })
  @MoneyTransformer()
  principalAmount: any;
  @Expose({ name: "maturityAmount" })
  @MoneyTransformer()
  maturityAmount: any;
  @Expose({ name: "principalAmountEquivalent" })
  @MoneyTransformer()
  principalAmountEquivalent: any;
  @Expose({ name: "maturityAmountEquivalent" })
  @MoneyTransformer()
  maturityAmountEquivalent: any;
  @Expose({ name: "principalAmountEquivalentEGP" })
  @MoneyTransformer()
  principalAmountEquivalentEGP: any;
}

export class FccAccountDetailBalanceDto {
  @Expose({ name: "heldAmountEquivalent" })
  @MoneyTransformer()
  heldAmountEquivalent: any;

  @Expose({ name: "overdraftLimit" })
  @MoneyTransformer()
  overdraftLimit: any;

  @Expose({ name: "availableBalanceEquivalent" })
  @MoneyTransformer()
  availableBalanceEquivalent: any;

  @Expose({ name: "equivalentCurrency" })
  equivalentCurrency: any;

  @Expose({ name: "heldAmount" })
  @MoneyTransformer()
  heldAmount: any;

  @Expose({ name: "availableBalance" })
  @MoneyTransformer()
  availableBalance: any;

  @Expose({ name: "balanceAsOn" })
  balanceAsOn: any;

  @Expose({ name: "ledgerBalance" })
  @MoneyTransformer()
  ledgerBalance: any;

  @Expose({ name: "ledgerBalanceEquivalent" })
  @MoneyTransformer()
  ledgerBalanceEquivalent: any;

  @Expose({ name: "overdraftLimitEquivalent" })
  @MoneyTransformer()
  overdraftLimitEquivalent: any;

  @Expose({ name: "onlineActualBalance" })
  @MoneyTransformer()
  onlineActualBalance: any;

  @Expose({ name: "onlineActualBalanceEquivalent" })
  @MoneyTransformer()
  onlineActualBalanceEquivalent: any;

  @Expose({ name: "nickname" })
  nickname: any;

  @Expose({ name: "expiryDate" })
  expiryDate: Date;

  @Expose({ name: "principalAmount" })
  @MoneyTransformer()
  principalAmount: any;
  @Expose({ name: "maturityAmount" })
  @MoneyTransformer()
  maturityAmount: any;
  @Expose({ name: "principalAmountEquivalent" })
  @MoneyTransformer()
  principalAmountEquivalent: any;
  @Expose({ name: "maturityAmountEquivalent" })
  @MoneyTransformer()
  maturityAmountEquivalent: any;
}

export class FCCAccountDetailDto {
  @Expose({ name: "interestRate" })
  interestRate: any;

  @Expose({ name: "country" })
  country: any;

  @Expose({ name: "cutomerReference" })
  customerReference: any;

  @Expose({ name: "debitInterestRate" })
  debitInterestRate: any;

  @Expose({ name: "accountName" })
  accountName: any;

  @Expose({ name: "format" })
  format: any;

  @Expose({ name: "accountEndDate" })
  accountEndDate: any;

  @Expose({ name: "type" })
  type: any;

  @Expose({ name: "principalAmount" })
  principalAmount: any;

  @Expose({ name: "number" })
  number: any;

  @Expose({ name: "maturityAmount" })
  maturityAmount: any;

  @Expose({ name: "balances" })
  balances: FccAccountDetailBalanceDto;

  @Expose({ name: "accountStartDate" })
  accountStartDate: any;

  @Expose({ name: "bankShortName" })
  bankShortName: any;

  @Expose({ name: "overDraftLimit" })
  overDraftLimit: any;

  @Expose({ name: "name" })
  name: any;

  @Expose({ name: "currency" })
  currency: any;

  @Expose({ name: "id" })
  id: any;

  @Expose({ name: "creditInterestRate" })
  creditInterestRate: any;

  @Expose({ name: "status" })
  status: any;

  @Expose({ name: "tdStartDate" })
  tdStartDate: any;
  @Expose({ name: "tdEndDate" })
  tdEndDate: any;
}
