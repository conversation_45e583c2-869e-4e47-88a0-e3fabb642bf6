import { FCCAccountDetailDto } from "./fcc-accounts.dtos";
import { IsArray, IsNumber, IsOptional, IsString, Min } from "class-validator";
import { QueryArrayTransformer } from "@helpers";
import { Type } from "class-transformer";
import { ListDefQueryDto } from "../common";

export class FccLoanDetailsDto extends FCCAccountDetailDto {}

export class LoanListQueryDto extends ListDefQueryDto {
  // Financial Filters
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @QueryArrayTransformer()
  curCode?: string[];

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  fromAmount?: number;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  toAmount?: number;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  outstandingFromAmount?: number;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  outstandingToAmount?: number;

  // Date filters
  @IsOptional()
  @IsString()
  valueDate?: string;

  @IsOptional()
  @IsString()
  maturityDate?: string;
}
