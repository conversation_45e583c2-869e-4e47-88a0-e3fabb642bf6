import { BadRequestException, Injectable, Logger } from "@nestjs/common";
import { LookupEntity } from "../lookups.enums";
import { LookupsRemoteDataService } from "../remote-data.service";
import { UserLanguage } from "@enums";
import { I18nLookupService } from "./i18n.service";
import { RawAccountProduct } from "../lookups.types";

@Injectable()
export class AccountsLookupService {
  constructor(
    protected dataService: LookupsRemoteDataService,
    protected i18n: I18nLookupService
  ) {}

  getProductList(language: UserLanguage = UserLanguage["en-US"]) {
    return Array.from(this.getProductMap(language)?.values());
  }

  getEligibleProductList(
    segmentId: string,
    language: UserLanguage = UserLanguage["en-US"]
  ) {
    return Array.from(
      this.getEligibleProductsMap(segmentId, language)?.values()
    );
  }

  getGroupedEligibleProductList(
    segmentId: string,
    language: UserLanguage = UserLanguage["en-US"]
  ) {
    const products = this.getEligibleProductsMap(segmentId, language).values();
    const grouped = new Map<string, any>();

    for (const product of products) {
      const existing = grouped.get(product.name);

      const enrichedCurrencies = product.currencies.map((currency) => ({
        ...currency,
        productCode: product.productCode,
      }));

      if (existing) {
        existing.currencies.push(...enrichedCurrencies);
      } else {
        grouped.set(product.name, {
          name: product.name,
          categoryCode: product.categoryCode,
          currencies: enrichedCurrencies,
        });
      }
    }

    return Array.from(grouped.values());
  }

  getEligibleProductsMap(
    segmentId: string,
    language: UserLanguage = UserLanguage["en-US"]
  ) {
    const map = this.getProductMap(language);

    for (const [key, item] of map) {
      const { segments, ...product } = item;

      const isEligible = segments?.find(
        (segment) => segment?.segmentId === segmentId
      );

      if (isEligible) {
        map.set(key, {
          ...product,
          allowMultiplePerCurrency: !!isEligible.allowMultiplePerCurrency,
        });
      } else {
        map.delete(key);
      }
    }
    return map;
  }

  getProductMap(language: UserLanguage = UserLanguage["en-US"]) {
    const map = this.dataService.getTranslatedEntity<Map<string, any>>(
      LookupEntity.ACCOUNT_PRODUCT,
      language
    );
    const newMap = new Map(map);
    for (const [key, item] of newMap) {
      newMap.set(key, {
        ...item,
        currencies: item.currencies.map((currency) => ({
          symbol: currency,
          name: this.i18n.translate(
            `global.CurrencyNames.${currency}`,
            language
          ),
        })),
      });
    }
    return newMap;
  }

  getProduct(
    productCode: string,
    language: UserLanguage = UserLanguage["en-US"]
  ) {
    return this.dataService
      .getTranslatedEntity<
        Map<string, RawAccountProduct>
      >(LookupEntity.ACCOUNT_PRODUCT, language)
      ?.get(productCode);
  }
}
