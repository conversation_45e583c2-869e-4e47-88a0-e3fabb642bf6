import { Injectable } from "@nestjs/common";
import { LookupEntity } from "../lookups.enums";
import { LookupsRemoteDataService } from "../remote-data.service";
import { UserLanguage } from "@enums";

@Injectable()
export class I18nLookupService {
  private entity: LookupEntity = LookupEntity.I18N_SERVER;

  constructor(private readonly dataService: LookupsRemoteDataService) {}

  translate(
    key: string,
    language: UserLanguage = UserLanguage["en-US"],
    values?: Record<string, string | number>
  ) {
    const template =
      this.dataService.getTranslatedEntity(this.entity, language)?.[key] || key;
    return template.replace(/{{(.*?)}}/g, (_, key) => {
      const trimmedKey = key.trim();
      const value = values?.[trimmedKey];
      return value !== undefined ? String(value) : `{{${trimmedKey}}}`;
    });
  }

  has(key: string, language: UserLanguage = UserLanguage["en-US"]) {
    return !!this.dataService.getTranslatedEntity(this.entity, language)?.[key];
  }

  translateOrDefault(
    key: string,
    language: UserLanguage = UserLanguage["en-US"],
    values?: Record<string, string | number>,
    defaultValue?: string
  ) {
    return this.has(key) ? this.translate(key, language, values) : defaultValue;
  }
}
