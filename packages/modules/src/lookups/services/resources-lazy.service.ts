import { Injectable, Logger } from "@nestjs/common";
import { LookupsRemoteDataService } from "../remote-data.service";
import { LookupsEntityConfig } from "../lookups.types";
import { UserLanguage } from "@enums";
import { UserLanguageMap } from "@constants";

@Injectable()
export class LazyResourcesLookupService {
  private options: Partial<LookupsEntityConfig> = {
    identifier: "identifier",
    keyByIdentifier: true,
  };

  constructor(
    private dataService: LookupsRemoteDataService,
    private readonly logger: Logger
  ) {}

  async load(
    resourceGroupId: string,
    language: UserLanguage = UserLanguage["en-US"]
  ): Promise<{ get: (identifier: string) => string; getAll: () => any }> {
    const groupData = await this.dataService?.forceLoad(
      resourceGroupId,
      this.options
    );

    const group = groupData?.get(UserLanguageMap?.[language]) || {};

    return {
      get: (identifier: string) => {
        return group?.get(identifier)?.value || "";
      },
      getAll: () => {
        return group instanceof Map ? Object.fromEntries(group.entries()) : {};
      },
    };
  }
}
