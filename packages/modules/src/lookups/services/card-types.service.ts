import { Injectable } from "@nestjs/common";
import { LookupEntity } from "../lookups.enums";
import { LookupsRemoteDataService } from "../remote-data.service";
import { CardTypeLookup } from "../lookups.types";

@Injectable()
export class CardTypesService {
  constructor(protected dataService: LookupsRemoteDataService) {}

  getOrDefault(identifier: string) {
    const defaults = this.dataService
      .getEntity<Map<string, CardTypeLookup>>(LookupEntity.CARD_TYPE)
      ?.get(identifier);

    return {
      image: defaults?.defaultImage || "",
      topImage: defaults?.defaultTopImage || "",
      backgroundColor: defaults?.defaultBackgroundColor || "#000000",
      textColor: defaults?.defaultTextColor || "#ffffff",
    };
  }
}
