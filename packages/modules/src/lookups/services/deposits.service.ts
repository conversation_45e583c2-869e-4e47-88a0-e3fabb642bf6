import { BadRequestException, Injectable, Logger } from "@nestjs/common";
import { LookupEntity } from "../lookups.enums";
import { LookupsRemoteDataService } from "../remote-data.service";
import { CurrencyEnum, UserLanguage } from "@enums";
import { I18nLookupService } from "./i18n.service";
import { RawDepositProduct, DepositProduct } from "../lookups.types";
import { normalizeTenorUnit, pluralize } from "@helpers";

@Injectable()
export class DepositsLookupService {
  private readonly logger = new Logger(DepositsLookupService.name);

  constructor(
    protected dataService: LookupsRemoteDataService,
    protected i18n: I18nLookupService
  ) {}

  getGroupedEligibleProductList(
    segmentId: string,
    allowedCurrencies: CurrencyEnum[],
    language: UserLanguage = UserLanguage["en-US"]
  ) {
    this.logger.debug(
      `Trying to get the eligible products for ${segmentId} segment`
    );
    const products = this.getEligibleProductsMap(segmentId, language);

    const grouped = {
      local: [],
      foreign: [],
    };

    for (const [id, product] of products) {
      const code = product.currency.code as CurrencyEnum;
      if (allowedCurrencies && allowedCurrencies.includes(code))
        if (code === CurrencyEnum.EGP) {
          grouped.local.push(product);
        } else {
          grouped.foreign.push(product);
        }
    }

    return grouped;
  }

  getEligibleProductList(
    segmentId: string,
    language: UserLanguage = UserLanguage["en-US"]
  ) {
    return Array.from(
      this.getEligibleProductsMap(segmentId, language)?.values()
    );
  }

  getEligibleProductsMap(
    segmentId: string,
    language: UserLanguage = UserLanguage["en-US"]
  ) {
    const map = this.getProductMap(language);

    for (const [key, item] of map) {
      const { segments, ...product } = item;

      const isEligible = segments?.find(
        (segment) => segment?.segmentId === segmentId
      );

      if (isEligible) {
        map.set(key, {
          ...product,
        });
      } else {
        map.delete(key);
      }
    }
    return map;
  }

  getProductMap(language: UserLanguage = UserLanguage["en-US"]) {
    const map = this.dataService.getTranslatedEntity<
      Map<string, RawDepositProduct>
    >(LookupEntity.DEPOSIT_PRODUCT, language);
    const newMap = new Map<string, DepositProduct>();
    for (const [key, item] of map) {
      newMap.set(key, {
        id: item.id,
        name: item.name,
        currency: {
          code: item.currency,
          localized: this.i18n.translate(
            `global.CurrencyNames.${item.currency}`,
            language
          ),
        },
        interestType: this.i18n.translate(
          `deposits.interestTypes.${item.interestType}`,
          language
        ),
        interestRate: item.interestRate,
        interestPaymentFrequency: this.i18n.translate(
          `deposits.interestPaymentFrequencies.${item.interestPaymentFrequency}`,
          language
        ),
        lockupPeriod: item.lockupPeriod,
        minimumBookingAmount: item.minimumBookingAmount,
        minimumRedemptionAmount: item.minimumRedemptionAmount,
        tenor: `${item.tenorValue} ${this.i18n.translate(
          pluralize(
            `global.units.${normalizeTenorUnit(item.tenorUnit)}`,
            item.tenorValue
          ),
          language
        )}`,
        segments: item.segments,
      });
    }
    return newMap;
  }

  getProductDetails(
    productCode: string,
    language: UserLanguage = UserLanguage["en-US"]
  ): any {
    const map = this.getProductMap(language);
    if (map.has(productCode)) {
      const { segments, ...product } = map.get(productCode);
      return { ...product };
    } else {
      this.logger.debug(
        `Deposit product ${productCode} does not exist on dashboard`
      );
    }
    return {};
  }

  getProduct(
    productCode: string,
    language: UserLanguage = UserLanguage["en-US"]
  ) {
    return this.dataService
      .getTranslatedEntity<
        Map<string, any>
      >(LookupEntity.DEPOSIT_PRODUCT, language)
      ?.get(productCode);
  }
}
