import { BadRequestException, Injectable, Logger } from "@nestjs/common";
import { LookupEntity } from "../lookups.enums";
import { LookupsRemoteDataService } from "../remote-data.service";
import { UserLanguage } from "@enums";

@Injectable()
export class DynamicListsLookupService {
  entity: LookupEntity = LookupEntity.CONTENT_LIST;

  constructor(protected dataService: LookupsRemoteDataService) {}

  getValues(
    identifier: string,
    language: UserLanguage = UserLanguage["en-US"]
  ) {
    return (
      this.dataService
        .getTranslatedEntity<Map<string, any>>(this.entity, language)
        ?.get(identifier)?.items || []
    );
  }

  // isValidItem(identifier: string, key: string): boolean {
  //   if (this.getTranslated().has(identifier)) {
  //     const items = this.getTranslated().get(identifier)?.items;
  //     return Array.isArray(items) && items.some((item) => item.key === key);
  //   }
  //   return false;
  // }

  // getItemOrThrow(identifier: string, key: string) {
  //   if (this.getTranslated().has(identifier)) {
  //     const items = this.getTranslated().get(identifier)?.items;
  //     const item =
  //       Array.isArray(items) && items.find((item) => item.key === key);
  //     if (item) {
  //       return item;
  //     }
  //   }
  //   throw new BadRequestException(`${key} is not valid`);
  // }

  getItemValue(
    identifier: string,
    keys: string | string[],
    language: UserLanguage = UserLanguage["en-US"]
  ): string | string[] | undefined {
    const items = this.getValues(identifier, language);
    if (!items) return Array.isArray(keys) ? [] : undefined;

    const findName = (key: string) =>
      items.find((item) => item.key === key)?.name;

    if (Array.isArray(keys)) {
      return keys.map((key) => findName(key));
    }

    return findName(keys);
  }
}
