import { BadRequestException, Injectable, Logger } from "@nestjs/common";
import { LookupEntity } from "../lookups.enums";
import { LookupsRemoteDataService } from "../remote-data.service";
import { UserLanguage } from "@enums";
import { LookupsEntityConfig } from "../lookups.types";
import { UserLanguageMap } from "@constants";

@Injectable()
export class LazyBranchesLookupService {
  entity: LookupEntity = LookupEntity.BANK_BRANCH;

  constructor(
    protected dataService: LookupsRemoteDataService,
    private readonly logger: Logger
  ) {}

  private options: Partial<LookupsEntityConfig> = {};

  async load(
    entity: LookupEntity,
    language: UserLanguage = UserLanguage["en-US"]
  ) {
    const groupData = await this.dataService?.forceLoad(entity, this.options);
    return groupData?.get(UserLanguageMap?.[language]) || [];
  }

  async getOrThrow(id: string, language: UserLanguage = UserLanguage["en-US"]) {
    const branches = await this.load(this.entity, language);
    const obj = branches.find((item) => item.id == id);
    if (obj) {
      return obj;
    }
    throw new BadRequestException(`${id} is not valid ${this.entity}`);
  }
}
