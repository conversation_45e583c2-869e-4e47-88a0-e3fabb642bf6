import { Injectable } from "@nestjs/common";
import {
  ConfigIndustryCode,
  ConfigSegment,
  IndustryInfo,
} from "../dtos/industry.dto";
import { LookupEntity } from "../lookups.enums";
import { LookupsRemoteDataService } from "../remote-data.service";

@Injectable()
export class IndustryLookupService {
  constructor(private readonly dataService: LookupsRemoteDataService) {}

  getIndustry(code: string): IndustryInfo {
    console.debug(`get industry code ${code} info`);
    const { id, name, segment, ...industry }: ConfigIndustryCode =
      this.dataService
        ?.getEntity<Map<string, any>>(LookupEntity.INDUSTRY_CODE)
        ?.get(code) || {};
    return {
      id,
      name,
      segment,
      segmentId: segment?.id,
      isMinor: industry.ageGroup === "minor",
      isSenior: industry.ageGroup === "senior",
      isYouth: industry.ageGroup === "youth",
      isOverseas:
        industry.overseas === "overseas" ||
        industry.overseas === "potentialOverseas",
      isCare: !!industry.isCare,
    };
  }

  getSegment(industryCode: string): ConfigSegment {
    const industry = this.getIndustry(industryCode);
    if (industry) {
      return industry.segment;
    }
  }
}
