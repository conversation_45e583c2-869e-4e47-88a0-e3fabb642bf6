import { Injectable } from "@nestjs/common";
import { LookupsRemoteDataService } from "../remote-data.service";
import { CardTypesService } from "./card-types.service";
import { CardTypeEnum } from "@enums";
import {
  AppleTokenizationConfigLookup,
  CardProductLookup,
  ICardProductOptions,
} from "../lookups.types";
import { LookupEntity } from "../lookups.enums";

@Injectable()
export class CardProductsLookupService {
  constructor(
    protected dataService: LookupsRemoteDataService,
    protected cardTypesService: CardTypesService
  ) {}

  getAppleTokenizationConfig(
    identifier: string
  ): AppleTokenizationConfigLookup {
    const { appleTokenizationConfig }: CardProductLookup = this.dataService
      .getEntity<Map<string, CardProductLookup>>(LookupEntity.CARD_PRODUCT)
      ?.get(identifier);
    return appleTokenizationConfig;
  }

  getProductDetails(
    identifier: string,
    type: CardTypeEnum = CardTypeEnum.CREDIT_CARD,
    options: ICardProductOptions = {}
  ): any {
    const results = {
      name: "Unknown",
      network: "Unknown",
      segmentLogo: "",
      typeLogo: "",
      visible: true,
      ...this.cardTypesService.getOrDefault(type),
    };
    const cardInfo = this.dataService
      .getEntity<Map<string, CardProductLookup>>(LookupEntity.CARD_PRODUCT)
      ?.get(identifier);

    if (cardInfo) {
      const {
        id,
        cardName: name,
        cardNetwork: network,
        cardType,
        cardImage: image,
        cardTopImage: topImage,
        cardTypeImage: typeImage,
        cardSegmentImage: segmentImage,
        cardBackgroundColor: backgroundColor,
        cardTextColor: textColor,
        appleTokenizationConfig: {
          appleTokenizationEnabled,
          appleTokenizationCardStatuses = [],
        },
        issuanceFees,
        renewalFees,
        ...reset
      } = cardInfo;

      let updatedFields = {
        productId: id,
        name,
        image,
        topImage,
        typeImage,
        type: cardType?.identifier,
        segmentImage,
        backgroundColor,
        textColor,
        network: cardInfo?.cardNetwork?.identifier,
        networkLogo: cardInfo?.cardNetwork?.logoImage,
        appleTokenizationEnabled,
        appleTokenizationCardStatuses,
        ...reset,
      };

      if (options && options.includeFees === true) {
        updatedFields = { ...updatedFields, issuanceFees, renewalFees } as any;
      }

      Object.assign(
        results,
        Object.entries(updatedFields).reduce(
          (acc, [key, value]) => {
            if (value !== undefined && value !== null && value !== "") {
              acc[key] = value;
            }
            return acc;
          },
          {} as Record<string, any>
        )
      );
    }

    return results;
  }
}
