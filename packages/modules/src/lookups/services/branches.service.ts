import { BadRequestException, Injectable, Logger } from "@nestjs/common";
import { LookupEntity } from "../lookups.enums";
import { LookupsRemoteDataService } from "../remote-data.service";
import { UserLanguage } from "@enums";

@Injectable()
export class LocalBranchesLookupService {
  constructor(protected dataService: LookupsRemoteDataService) {}

  private getBranchesMap(language: UserLanguage = UserLanguage["en-US"]) {
    return this.dataService.getTranslatedEntity<Map<string, any>>(
      LookupEntity.BANK_BRANCH,
      language
    );
  }

  getBranchesList(language: UserLanguage = UserLanguage["en-US"]) {
    return Array.from(this.getBranchesMap(language)?.values() ?? []);
  }

  getBranchById(
    branchId: string,
    language: UserLanguage = UserLanguage["en-US"]
  ) {
    return this.getBranchesMap(language)?.get(branchId);
  }

  getGovernatesList(language: UserLanguage = UserLanguage["en-US"]) {
    return this.dataService.getTranslatedEntity<Map<string, any>>(
      LookupEntity.BANK_BRANCH_GOVERNATE,
      language
    );
  }
}
