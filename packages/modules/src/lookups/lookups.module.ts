import { DynamicModule, Global, Logger, Module } from "@nestjs/common";
import { ScheduleModule } from "@nestjs/schedule";

import { HttpModule } from "@nestjs/axios";
import { LookupsEntityOptions } from "./lookups.types";
import { LookupsRemoteDataService } from "./remote-data.service";
import { I18nLookupService } from "./services/i18n.service";
import {
  AccountsLookupService,
  DepositsLookupService,
  DynamicListsLookupService,
  IndustryLookupService,
  LazyBranchesLookupService,
  LazyResourcesLookupService,
  LocalBranchesLookupService,
} from "./services";
import { CardTypesService } from "./services/card-types.service";
import { CardProductsLookupService } from "./services/cards.service";

@Global()
@Module({})
export class LookupsModule {
  static forRoot(entities: LookupsEntityOptions[] = []): DynamicModule {
    return {
      module: LookupsModule,
      imports: [ScheduleModule.forRoot(), HttpModule],
      providers: [
        {
          provide: "entities",
          useValue: entities,
        },
        LookupsRemoteDataService,
        I18nLookupService,
        IndustryLookupService,
        LazyResourcesLookupService,
        DynamicListsLookupService,
        LazyBranchesLookupService,
        LocalBranchesLookupService,
        AccountsLookupService,
        CardProductsLookupService,
        CardTypesService,
        DepositsLookupService,
        Logger,
      ],
      exports: [
        LookupsRemoteDataService,
        I18nLookupService,
        IndustryLookupService,
        LazyResourcesLookupService,
        DynamicListsLookupService,
        LazyBranchesLookupService,
        LocalBranchesLookupService,
        AccountsLookupService,
        CardProductsLookupService,
        CardTypesService,
        DepositsLookupService,
      ],
    };
  }
}
