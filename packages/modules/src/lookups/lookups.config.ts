import { LookupEntity } from "./lookups.enums";
import { LookupsEntityConfig } from "./lookups.types";

export const LookupsEntityMap: Partial<
  Record<LookupEntity, LookupsEntityConfig>
> = {
  [LookupEntity.CONTENT_LIST]: {
    name: LookupEntity.CONTENT_LIST,
    identifier: "identifier",
    keyByIdentifier: true,
  },
  [LookupEntity.I18N_SERVER]: {
    name: LookupEntity.I18N_SERVER,
  },
  [LookupEntity.SERVER_CONFIG]: {
    name: LookupEntity.I18N_SERVER,
  },
  [LookupEntity.INDUSTRY_CODE]: {
    name: LookupEntity.INDUSTRY_CODE,
    identifier: "id",
    keyByIdentifier: true,
  },
  [LookupEntity.BANK_BRANCH]: {
    name: LookupEntity.BANK_BRANCH,
    identifier: "id",
    keyByIdentifier: true,
  },
  [LookupEntity.BANK_BRANCH_GOVERNATE]: {
    name: LookupEntity.BANK_BRANCH_GOVERNATE,
  },
  [LookupEntity.ACCOUNT_PRODUCT]: {
    name: LookupEntity.ACCOUNT_PRODUCT,
    identifier: "productCode",
    keyByIdentifier: true,
    dependencies: [LookupEntity.INDUSTRY_CODE, LookupEntity.I18N_SERVER],
  },
  [LookupEntity.CARD_PRODUCT]: {
    name: LookupEntity.CARD_PRODUCT,
    identifier: "id",
    keyByIdentifier: true,
    dependencies: [LookupEntity.CARD_TYPE],
  },
  [LookupEntity.DEPOSIT_PRODUCT]: {
    name: LookupEntity.DEPOSIT_PRODUCT,
    identifier: "id",
    keyByIdentifier: true,
    dependencies: [LookupEntity.INDUSTRY_CODE, LookupEntity.I18N_SERVER],
  },
};
