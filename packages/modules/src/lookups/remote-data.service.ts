import { Injectable, Logger, Inject } from "@nestjs/common";
import {
  LookupData,
  LookupsEntityConfig,
  LookupsEntityOptions,
  TypeMap,
} from "./lookups.types";
import { Cron, Interval } from "@nestjs/schedule";
import { HttpService } from "@nestjs/axios";
import { ConfigService } from "@nestjs/config";
import { firstValueFrom } from "rxjs";
import * as http from "node:http";
import * as https from "node:https";
import { LookupEntity } from "./lookups.enums";
import { UserLanguage } from "@enums";
import { UserLanguageMap } from "@constants";
import { LookupsEntityMap } from "./lookups.config";

@Injectable()
export class LookupsRemoteDataService {
  private readonly logger = new Logger(LookupsRemoteDataService.name);

  private readonly data: LookupData = {};

  constructor(
    @Inject("entities") private entities: LookupsEntityOptions[],
    private readonly httpService: HttpService,
    private readonly configService: ConfigService
  ) {}

  onModuleInit() {
    this.loadData();
  }

  @Interval(2 * 60 * 1000)
  refreshData() {
    this.loadData();
  }

  async loadRawData(
    entities: string | string[] | LookupEntity | LookupEntity[]
  ) {
    const response = await firstValueFrom(
      this.httpService.get("/api/v1/store/public", {
        baseURL: this.configService.get("lookupsAPIServiceURL"),
        params: {
          entity: entities,
        },
        httpAgent: new http.Agent({ keepAlive: true }),
        httpsAgent: new https.Agent({
          keepAlive: true,
          rejectUnauthorized: false,
        }),
      })
    );
    return response;
  }

  toMap(records: any[], identifier: string, filter?: string[]) {
    const map = new Map();
    if (Array.isArray(records)) {
      if (records.length > 0) {
        records.forEach((item) => {
          if (
            filter &&
            filter?.length > 0 &&
            filter.includes(item[identifier]) === false
          ) {
            return;
          }
          map.set(item[identifier], item);
        });
      }
    }
    return Object.freeze(map); //freezing the individual maps
  }

  async loadData() {
    if (!this.entities || this.entities?.length === 0) {
      return;
    }
    const entities = this.entities
      .map((entity) => {
        return [
          entity.name,
          ...(LookupsEntityMap?.[entity.name]?.dependencies ?? []),
        ];
      })
      .flat();

    try {
      this.logger.log(`loading ${entities} entities...`);
      const { data } = await this.loadRawData(entities);

      const newData: LookupData = {};

      this.entities.forEach((entity) => {
        const config = LookupsEntityMap?.[entity.name];
        if (data && data[entity.name]) {
          if (!newData[entity.name]) {
            newData[entity.name] = new Map();
          }

          if (Array.isArray(data[entity.name])) {
            if (data[entity.name].length > 0) {
              newData[entity.name] = this.toMap(
                data[entity.name],
                config?.identifier
              );
            }
          } else {
            Object.keys(data[entity.name]).forEach((key: string) => {
              let record = data[entity.name][key];
              if (config?.keyByIdentifier === true) {
                record = this.toMap(
                  data[entity.name][key],
                  config?.identifier,
                  entity.filter
                );
              }
              if (record !== undefined && record !== "") {
                newData?.[entity.name].set(key, record);
              }
            });
          }
        } else {
          this.logger.error(`${entity.name} entity is not found`);
        }
      });

      // Deep-freeze the newData object
      Object.keys(newData).forEach((key) => {
        if (newData[key] instanceof Map) {
          newData[key] = Object.freeze(newData[key]);
        }
      });

      Object.assign(this.data, Object.freeze(newData));
    } catch (error) {
      console.log(error);
      this.logger.error(`error loading ${entities}`);
    }
  }

  async forceLoad(entity: string, options: Partial<LookupsEntityConfig> = {}) {
    this.logger.log(`loading ${entity} entity...`);
    const { data } = await this.loadRawData(entity);

    const newData = new Map();
    if (data[entity]) {
      Object.keys(data[entity]).forEach((key: string) => {
        let record = data[entity][key];

        if (options?.keyByIdentifier === true) {
          record = this.toMap(data[entity][key], options.identifier);
        }
        if (record) {
          newData.set(key, record);
        }
      });
    }

    return newData;
  }

  getEntity<T>(entity: LookupEntity): T | null {
    const entityData = this.data?.[entity];
    if (!entityData) {
      return null;
    }
    return new Map(entityData) as unknown as T; // Return a shallow copy
  }

  getEntityField<T>(entity: LookupEntity, key: string): T | null {
    return this.getEntity<Map<string, any>>(entity)?.get(key);
  }

  getTranslatedEntity<T>(
    entity: LookupEntity,
    language: UserLanguage = UserLanguage["en-US"]
  ) {
    const locale = UserLanguageMap?.[language] || UserLanguageMap["en-US"];
    return this.getEntityField<T>(entity, locale);
  }
}
