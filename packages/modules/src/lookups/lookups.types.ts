import { LocalizedCurrency } from "@types";
import { LookupEntity } from "./lookups.enums";

export interface LookupsEntityOptions {
  name: LookupEntity;
  filter?: string[];
}

export interface LookupsEntityConfig {
  name: LookupEntity;
  identifier?: string;
  keyByIdentifier?: boolean;
  dependencies?: LookupEntity[];
}

export type LookupData = {
  [K in keyof TypeMap]: Map<string, TypeMap[K]>;
};

export type TypeMap = {};

//Raw Lookups types are xLookup
export interface CardTypeLookup {
  identifier: string;
  name: string;
  defaultBackgroundColor: string;
  defaultTextColor: string;
  defaultImage: string;
  defaultTopImage: string;
  appleTokenizationEnabled: boolean;
}

export interface CardNetworkLookup {
  identifier: string;
  logoImage: string;
}

export interface CardProductLookup {
  id: string;
  cardName: string;

  visible: boolean;

  appleTokenizationEnabled: boolean;
  appleTokenizationConfig: any;

  issuanceFees?: number;
  renewalFees?: number;

  cardType: any;
  cardNetwork: CardNetworkLookup;

  cardTypeImage: string;
  cardImage: string;
  cardTopImage: string;
  typeLogo: string;
  cardSegmentImage: string;
  cardBackgroundColor: string;
  cardTextColor: string;
}

export interface AppleTokenizationConfigLookup {
  appleTokenizationEnabled: boolean;
  appleTokenizationCardStatuses: string[];
  networkName: string;
  productId: string;
}

export interface ICardProductOptions {
  includeFees?: boolean;
}

//Deposits

export interface RawDepositProduct {
  id: string;
  name: string;
  currency: string;
  interestType: string;
  interestRate: number;
  interestPaymentFrequency: string;
  lockupPeriod: string;
  minimumBookingAmount: number;
  minimumRedemptionAmount: number;
  tenorUnit: string;
  tenorValue: number;
  segments: {
    segmentId: string;
  }[];
}

export interface DepositProduct {
  id: string;
  name: string;
  currency: LocalizedCurrency;
  interestType: string;
  interestRate: number;
  interestPaymentFrequency: string;
  lockupPeriod: string;
  minimumBookingAmount: number;
  minimumRedemptionAmount: number;
  tenor: string;
  segments?: {
    segmentId: string;
  }[];
}

//Accounts

export interface RawAccountProduct {
  id: string;
  name: string;
  currency: string[];
  productCode: string;
  categoryCode: string;
  segments: {
    segmentId: string;
    allowMultiplePerCurrency: number;
  }[];
}
