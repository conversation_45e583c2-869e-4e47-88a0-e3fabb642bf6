export interface ConfigIndustryCode {
  id: string;
  name: string;
  ageGroup: string; //Minor | Senior | Youth
  overseas: string; // Potential Overseas | Overseas
  isCare: boolean;
  segment: ConfigSegment;
}

export interface IndustryInfo {
  id: string;
  name: string;
  isMinor: boolean;
  isSenior: boolean;
  isYouth: boolean;
  isOverseas: boolean;
  isCare: boolean;
  segment: ConfigSegment;
  segmentId: string;
}

export interface ConfigSegment {
  id: string;
  name: string;
  primaryColor: string;
}
