import { UserLanguage } from "@enums";
import { createParamDecorator, ExecutionContext } from "@nestjs/common";

export const getUserLanguage = createParamDecorator(
  (data: string | undefined, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    const userLang = request.headers["accept-language"];
    return UserLanguage[userLang] ? userLang : UserLanguage["en-US"];
  }
);
