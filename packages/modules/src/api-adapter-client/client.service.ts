import { HttpException, Injectable, Logger } from "@nestjs/common";
import * as http from "node:http";
import * as https from "node:https";

import { HttpService } from "@nestjs/axios";
import { firstValueFrom } from "rxjs";
import { RequestModuleEnum } from "@enums";
import { ApiAdapterRequestDto } from "@dtos";
import { ConfigService } from "@nestjs/config";
import { ApiValidationException } from "@exceptions";

/**
 * Service for making API requests to the Adapter Service.
 * Centralizes response extraction, error handling, and logging.
 */
@Injectable()
export class ApiAdapterClientService {
  readonly logger = new Logger(ApiAdapterClientService.name);

  constructor(
    protected readonly httpService: HttpService,
    protected readonly configService: ConfigService
  ) {}

  /**
   * Makes a request to the FCC module.
   * @param request The request DTO (without module).
   * @returns The response data from the FCC module.
   */
  async fccRequest<T>(
    request: Omit<Partial<ApiAdapterRequestDto>, "module">
  ): Promise<T> {
    return this.call({ ...request, module: RequestModuleEnum.FCC });
  }

  /**
   * Makes a request to the ESB module.
   * @param request The request DTO (without module).
   * @returns The response data from the FCC module.
   */
  async esbRequest<T>(
    request: Omit<Partial<ApiAdapterRequestDto>, "module">
  ): Promise<T> {
    return this.call<T>({ ...request, module: RequestModuleEnum.ESB });
  }

  /**
   * Makes a POST request to the API Adapter Service.
   * @param request The request DTO.
   * @returns The response data.
   */
  async call<T>(request: Partial<ApiAdapterRequestDto>): Promise<T> {
    try {
      const response = await firstValueFrom(
        this.httpService.request({
          method: "POST",
          url: `${this.configService.get<string>("apiAdapter.url")}/v1`,
          data: request,
          httpAgent: new http.Agent({ keepAlive: true }),
          httpsAgent: new https.Agent({ keepAlive: true }),
        })
      );
      return response.data;
    } catch (error) {
      if (error.response?.data) {
        this.logger.error({ status: error.status, error: error.response.data });
        const { statusCode, errorCode } = error.response?.data;
        if (errorCode) {
          throw new ApiValidationException(request, errorCode, statusCode);
        }
        throw new HttpException(error.response?.data, 500);
      }
      this.logger.error(error);
      throw new HttpException("An unknown error occurred", 500);
    }
  }
}
