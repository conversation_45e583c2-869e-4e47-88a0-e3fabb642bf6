import { Injectable } from "@nestjs/common";

import { RequestModuleEnum } from "@enums";
import { ApiAdapterRequestDto } from "@dtos";
import { ApiAdapterClientService } from "./client.service";
import { HttpService } from "@nestjs/axios";
import { ConfigService } from "@nestjs/config";
import { RedisAttachmentsService } from "../redis";

@Injectable()
export class FccClientService extends ApiAdapterClientService {
  constructor(
    protected readonly httpService: HttpService,
    protected readonly configService: ConfigService,
    protected readonly attachmentsService: RedisAttachmentsService
  ) {
    super(httpService, configService);
  }

  async call<T>(
    request: Omit<Partial<ApiAdapterRequestDto>, "module">
  ): Promise<T> {
    return super.call({ ...request, module: RequestModuleEnum.FCC });
  }

  async submitAttachments(tnxId: string) {
    const attachments = await this.attachmentsService.getAttachments(tnxId);
    if (attachments.length > 0) {
      for (let index = 0; index < attachments.length; index++) {
        const attachment = attachments[index];
        this.logger.debug(
          `Try to upload ${attachment.fileName} to ${attachment.refId}`
        );
      }
    }
  }
}
