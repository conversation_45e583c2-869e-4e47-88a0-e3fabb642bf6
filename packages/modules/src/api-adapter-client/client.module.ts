import { Global, Module } from "@nestjs/common";
import { ConfigModule, ConfigService } from "@nestjs/config";
import { ApiAdapterClientService } from "./client.service";
import { HttpModule } from "@nestjs/axios";
import { FccClientService } from "./fcc-client.service";

/*
 - timeout is to the api adapter only not the full lifecycle.
 - the external calls timeout is managed from the api adapter server
*/
@Global()
@Module({
  imports: [
    ConfigModule,
    HttpModule.registerAsync({
      useFactory: async (configService: ConfigService) => ({
        timeout: Number(configService.get<number>("apiAdapter.timeout")) * 1000,
      }),
      inject: [ConfigService],
    }),
  ],
  controllers: [],
  providers: [ApiAdapterClientService, FccClientService],
  exports: [ApiAdapterClientService, FccClientService],
})
export class ApiAdapterClient {}
