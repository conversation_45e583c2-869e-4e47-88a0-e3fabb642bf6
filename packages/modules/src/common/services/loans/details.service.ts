import { Injectable } from "@nestjs/common";
import { CurrentUser, DepositDetailsOptions } from "@types";
import { EsbProductTypeEnum, EsbRequestsEnum, FccRequestsEnum } from "@enums";
import {
  EsbLoanDetailsDto,
  EsbLoanListItemDto,
  FccLoanDetailsDto,
} from "@dtos";
import { ApiAdapterClientService } from "@modules";
import { LoanDetailsFormatterService } from "./formatters/details.formatter";

@Injectable()
export class LoanDetailsService {
  constructor(
    private readonly apiClientService: ApiAdapterClientService,
    private readonly formatterService: LoanDetailsFormatterService
  ) {}

  async getDetails(
    loanId: string,
    currentUser: CurrentUser,
    options: DepositDetailsOptions = {}
  ) {
    const fccDetails =
      await this.apiClientService.fccRequest<FccLoanDetailsDto>({
        requestId: FccRequestsEnum.LOAN_DETAILS,
        payload: { accountId: loanId },
        user: currentUser,
      });
    const [esbDetails, esbList] = await Promise.all([
      this.apiClientService.esbRequest<EsbLoanDetailsDto>({
        requestId: EsbRequestsEnum.LOANS_DETAILS,
        payload: {
          loanNumber: fccDetails.number,
        },
      }),
      this.apiClientService.esbRequest<EsbLoanListItemDto[]>({
        requestId: EsbRequestsEnum.LOANS_LIST,
        user: currentUser,
        payload: {
          userId: currentUser.cif,
          productType: EsbProductTypeEnum.LOAN,
        },
      }),
    ]);
    const esbItem: EsbLoanListItemDto = esbList.find(
      (item) => item.productIdent === fccDetails.number
    );

    return this.formatterService.format(
      fccDetails,
      esbDetails,
      esbItem,
      options
    );
  }
}
