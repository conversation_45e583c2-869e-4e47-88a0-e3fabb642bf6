import { Injectable, Logger } from "@nestjs/common";
import { BaseLoanFormatterService } from "./base-formatter.service";
import { EsbLoanDetailsDto, EsbLoanListItemDto } from "@dtos";
import { FccLoanDetailsDto } from "../../../../../../dtos/src/api-adapter/fcc-loans.dtos";
import { LoanListOptions } from "@types";

@Injectable()
export class LoanDetailsFormatterService extends BaseLoanFormatterService {
  private readonly logger = new Logger(LoanDetailsFormatterService.name);

  format(
    fccItem: FccLoanDetailsDto,
    esbItem: EsbLoanDetailsDto,
    esbLoanListItem: EsbLoanListItemDto,
    options: LoanListOptions = {},
  ) {
    this.logger.debug(fccItem, esbItem, esbLoanListItem);
    return {
      productDetails: {
        name: fccItem.accountName,
        description: esbItem.loanProductTypeDesc.includes("&")
          ? esbItem.loanProductTypeDesc.split("&")[1]
          : esbItem.loanProductTypeDesc,
        number: fccItem.number,
        amount: fccItem.principalAmount,
        type: fccItem.type,
        currency: {
          code: fccItem.currency,
          localized: this.i18n.translate(
            `global.CurrencyNames.${fccItem.currency}`,
            options.userLanguage,
          ),
        },
        valueDate: this.formatDate(fccItem.accountStartDate),
        maturityDate: this.formatDate(fccItem.accountEndDate),
      },
      termsDetails: {
        loanAmount: fccItem.principalAmount,
        outstandingAmount: {
          amount: esbLoanListItem.outstandingAmt.amt,
          curCode: esbLoanListItem.outstandingAmt.curCode,
        },
      },
      interestRate: esbItem.loanData.interestRate,
      tenor: this.formatTenor(esbLoanListItem.tenor, options.userLanguage),
      openedDate: fccItem.tdStartDate,
      maturityDate: fccItem.tdEndDate,
      installmentDetails: {
        accountNumber: esbItem.interestLiquidationAccount,
        amount: esbItem.loanData.totalInterest,
        nextDueDate: this.formatDate(esbItem.dueDate),
        totalPastDues: esbItem.overdueAmount,
      },
    };
  }
}
