import { Injectable, Logger } from "@nestjs/common";
import { CurrentUser, DepositListOptions } from "@types";
import { BaseLoanFormatterService } from "./base-formatter.service";
import { EsbLoanListItemDto } from "@dtos";

@Injectable()
export class LoanListFormatterService extends BaseLoanFormatterService {
  private readonly logger = new Logger(LoanListFormatterService.name);
  format(
    fccItem: any,
    esbItem: EsbLoanListItemDto,
    user: CurrentUser,
    options: DepositListOptions = {},
  ) {
    this.logger.debug(fccItem, esbItem);
    return {
      id: this.generateId(fccItem.accountId, fccItem.accountNumber, user),
      name: fccItem.accountIban,
      type: fccItem.accountType,
      number: fccItem.accountNumber,
      tenor: this.formatTenor(esbItem.tenor, options.userLanguage),
      startDate: fccItem.tdStartDate,
      maturityDate: fccItem.tdEndDate,
      currency: {
        code: fccItem.currency,
        localized: this.i18n.translate(
          `global.CurrencyNames.${fccItem.currency}`,
          options.userLanguage,
        ),
      },
      amount: fccItem.principalAmount,
      equivalentCurrency: fccItem.equivalentCurrency,
      equivalentAmount: fccItem.principalAmountEquivalent,
      principalAmountEquivalentEGP: fccItem.principalAmountEquivalentEGP,
      outstandingAmount: esbItem.outstandingAmt.amt,
      outstandingAmountCurrency: {
        code: esbItem.outstandingAmt.curCode,
        currency: {
          code: fccItem.currency,
          localized: this.i18n.translate(
            `global.CurrencyNames.${esbItem.outstandingAmt.curCode}`,
            options.userLanguage,
          ),
        },
      },
      nickname: fccItem.nickname,
      interestRate: esbItem.interestRate,
    };
  }
}
