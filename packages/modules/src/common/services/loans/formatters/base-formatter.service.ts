import { UserLanguage } from "@enums";
import {
  encryptUserJson,
  loanTenorFromString,
  pluralize,
  tenorFromString,
} from "@helpers";
import { DepositsLookupService, I18nLookupService } from "@modules";
import { Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { CurrentUser } from "@types";

@Injectable()
export abstract class BaseLoanFormatterService {
  constructor(
    protected readonly i18n: I18nLookupService,
    protected readonly config: ConfigService,
    protected readonly depositsLookupService: DepositsLookupService
  ) {}

  abstract format(...args: any[]): any;

  protected generateId(
    accountId: string,
    loanId: string,
    user: CurrentUser
  ): string {
    return encodeURIComponent(
      encryptUserJson(
        { accountId, loanId },
        this.config.get<string>("aesSecret"),
        user.userId
      )
    );
  }

  protected formatDate(dateStr: string, format: string = "en-GB"): string {
    return new Intl.DateTimeFormat(format, {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    }).format(new Date(dateStr));
  }

  public formatTenor(
    rawTenor: string,
    language: UserLanguage = UserLanguage["en-US"]
  ) {
    const { value, unit, isVariable } = loanTenorFromString(rawTenor);

    return {
      tenor: rawTenor,
      localized: `${value} ${this.i18n.translate(
        pluralize(`global.units.${unit}`, value),
        language
      )}`,
      isVariable,
    };
  }
}
