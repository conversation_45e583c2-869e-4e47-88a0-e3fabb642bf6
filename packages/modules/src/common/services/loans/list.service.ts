import { Injectable } from "@nestjs/common";
import { CurrentUser, LoanListOptions } from "@types";
import {
  DetailedAccountTypeWithBalanceQueryEnum,
  EsbProductTypeEnum,
  EsbRequestsEnum,
  FccRequestsEnum,
} from "@enums";
import { EsbLoanListItemDto, FccAccountListDto } from "@dtos";
import { ApiAdapterClientService } from "@modules";
import { LoanListFormatterService } from "./formatters/list.formatter.service";
import { LoanListQueryDto } from "../../../../../dtos/src/api-adapter/fcc-loans.dtos";

@Injectable()
export class LoansListService {
  constructor(
    private readonly apiClientService: ApiAdapterClientService,
    private readonly formatterService: LoanListFormatterService,
  ) {}

  async getOwnList(
    currentUser: CurrentUser,
    options: LoanListOptions = {},
    listQuery: LoanListQueryDto,
  ) {
    const [fccResult, esbResult] = await Promise.all([
      this.apiClientService.fccRequest<FccAccountListDto>({
        requestId: FccRequestsEnum.LOANS_LIST,
        user: currentUser,
        payload: {
          accountTypeForBalance: DetailedAccountTypeWithBalanceQueryEnum.LOAN,
          ...listQuery,
        },
      }),
      this.apiClientService
        .esbRequest<EsbLoanListItemDto[]>({
          requestId: EsbRequestsEnum.LOANS_LIST,
          user: currentUser,
          payload: {
            userId: currentUser.cif,
            productType: EsbProductTypeEnum.LOAN,
          },
        })
        .catch(() => []),
    ]);

    const esbMap = new Map(
      esbResult
        .filter((d) => d.status === "Active")
        .map((d) => [d.productIdent, d]),
    );

    const matchingFccLoans = fccResult.items.filter((fcc) =>
      esbMap.has(fcc.accountNumber),
    );

    let loans = matchingFccLoans.map((fccDeposit) =>
      this.formatterService.format(
        fccDeposit,
        esbMap.get(fccDeposit.accountNumber),
        currentUser,
        options,
      ),
    );

    loans = this.filterLoansWithOutstandingAmount(loans, listQuery);

    const totals = loans.reduce(
      (acc, d) => {
        acc.totalBalance += d.amount;
        acc.totalEquivalentBalance += d.equivalentAmount;
        acc.equivalentCurrency = d.equivalentCurrency;
        acc.totalOutstandingBalance = d.outstandingAmount;
        acc.totalOutstandingCurrency = d.outstandingAmountCurrency.code;
        return acc;
      },
      {
        totalBalance: 0,
        totalEquivalentBalance: 0,
        equivalentCurrency: options?.defaultCurrency,
        totalOutstandingBalance: 0,
        totalOutstandingCurrency: options?.defaultCurrency,
      },
    );

    return {
      ...totals,
      loans,
    };
  }

  private filterLoansWithOutstandingAmount(
    loans: {
      interestRate: number;
      amount: any;
      outstandingAmount: number;
      outstandingAmountCurrency: {
        code: any;
        currency: { code: any; localized: string };
      };
      principalAmountEquivalentEGP: any;
      type: any;
      equivalentCurrency: any;
      number: any;
      tenor: { tenor: string; localized: string; isVariable: boolean };
      maturityDate: any;
      name: any;
      equivalentAmount: any;
      nickname: any;
      currency: { code: any; localized: string };
      id: string;
      startDate: any;
    }[],
    listQuery: LoanListQueryDto,
  ) {
    if (listQuery?.outstandingFromAmount) {
      loans = loans.filter(
        (loan) => loan.outstandingAmount > listQuery.outstandingFromAmount,
      );
    }

    if (listQuery?.outstandingFromAmount) {
      loans = loans.filter(
        (loan) => loan.outstandingAmount < listQuery.outstandingToAmount,
      );
    }
    return loans;
  }
}
