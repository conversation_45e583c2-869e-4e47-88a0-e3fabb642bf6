import { ApiAdapterClientService } from "@modules";
import { Injectable, Logger } from "@nestjs/common";
import { EsbRequestsEnum, IbanTypeEnum, UserLanguage } from "@enums";
import { EsbAccountDetailsDto, ValidateIbanDto } from "@dtos";
import { isValidIBAN, maskAccountTitle } from "@helpers";
import { CurrentUser } from "@types";
import { UsersService } from "../users/users.service";

@Injectable()
export class AccountsValidationService {
  private readonly logger = new Logger(AccountsValidationService.name);

  constructor(
    private readonly apiClientService: ApiAdapterClientService,
    private readonly userService: UsersService
  ) {}

  async validateAccount(
    accountNumber: string,
    currentUser: CurrentUser,
    userLanguage: UserLanguage
  ) {
    try {
      const accountDetails =
        await this.apiClientService.esbRequest<EsbAccountDetailsDto>({
          requestId: EsbRequestsEnum.ACCOUNT_DETAILS,
          payload: {
            accountNumber,
          },
          options: { language: userLanguage },
        });

      let ownAccount = false;

      if (accountDetails?.partyIdent) {
        const references =
          await this.userService.getCompanyReferences(currentUser);
        if (references?.includes(accountDetails.partyIdent)) {
          ownAccount = true;
        }
      }

      return {
        isValid: true,
        ownAccount,
        title: maskAccountTitle(accountDetails.accountTitle),
        accountNumber: accountDetails.accountNumber,
        accountIban: accountDetails.accountIban,
        curCode: accountDetails.curCode,
      };
    } catch (error) {
      this.logger.error(error);
      return {
        isValid: false,
      };
    }
  }

  async validateIban({ iban, type = IbanTypeEnum.Domestic }: ValidateIbanDto) {
    if (
      (type === IbanTypeEnum.International && iban.startsWith("EG")) ||
      (type === IbanTypeEnum.Domestic && !iban.startsWith("EG"))
    ) {
      return {
        valid: false,
      };
    }
    const isValid = isValidIBAN(iban);
    return {
      isValid,
    };
  }
}
