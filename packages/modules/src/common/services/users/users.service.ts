import { Injectable, Logger } from "@nestjs/common";
import { CurrencyEnum, EsbRequestsEnum, FccRequestsEnum } from "@enums";
import { CurrentUser, CustomerDetails } from "@types";
import { EsbCustomerDetailsDto, FccUserDetailsDto } from "@dtos";
import { ApiAdapterClientService } from "@modules";

@Injectable()
export class UsersService {
  readonly logger = new Logger(UsersService.name);

  constructor(protected readonly clientService: ApiAdapterClientService) {}

  async getUserProfile(user: CurrentUser) {
    const fccProfile = await this.clientService.fccRequest<FccUserDetailsDto>({
      requestId: FccRequestsEnum.USER_DETAILS,
      user,
    });

    return fccProfile;
  }

  async getCustomerDetails(user: CurrentUser): Promise<CustomerDetails> {
    const fccProfile = await this.getUserProfile(user);
    const esbProfile =
      await this.clientService.esbRequest<EsbCustomerDetailsDto>({
        requestId: EsbRequestsEnum.CUSTOMER_DETAILS,
        payload: {
          userId: fccProfile.customerReferences.at(0).ID,
        },
      });

    return {
      fccProfile,
      segment: user.segment,
      ...esbProfile,
    };
  }

  async getCompanyReferences(user: CurrentUser) {
    const { customerReferences = [] } = await this.getUserProfile(user);
    return customerReferences.map((ref) => Number(ref.ID));
  }

  async getDefaultCurrency(user: CurrentUser) {
    const { userPreferences } = await this.getUserProfile(user);
    return userPreferences?.baseCurrency ?? CurrencyEnum.EGP;
  }

  async getUserData(user: CurrentUser) {
    const profile = await this.getUserProfile(user);

    return {
      companyReferences: (profile.customerReferences ?? []).map((ref) =>
        Number(ref.ID)
      ),
      defaultCurrency:
        profile.userPreferences?.baseCurrency ?? CurrencyEnum.EGP,
      defaultAccount: profile.userPreferences?.defaultAccount,
    };
  }
}
