import { Injectable } from "@nestjs/common";
import { CurrentUser, DepositListOptions } from "@types";
import {
  DetailedAccountTypeWithBalanceQueryEnum,
  EsbProductTypeEnum,
  EsbRequestsEnum,
  FccRequestsEnum,
} from "@enums";
import {
  DepositListQueryDto,
  EsbDepositListItemDto,
  FccAccountListDto,
} from "@dtos";
import { ApiAdapterClientService } from "@modules";
import { TermDepositListFormatterService } from "./formatters/td-list-formatter.service";

@Injectable()
export class TermDepositListService {
  constructor(
    private readonly apiClientService: ApiAdapterClientService,
    private readonly formatterService: TermDepositListFormatterService
  ) {}

  async getOwnList(
    currentUser: CurrentUser,
    options: DepositListOptions = {},
    listQuery: DepositListQueryDto
  ) {
    const [fccResult, esbResult] = await Promise.all([
      this.apiClientService.fccRequest<FccAccountListDto>({
        requestId: FccRequestsEnum.TERM_DEPOSIT_LIST,
        user: currentUser,
        payload: {
          accountTypeForBalance:
            DetailedAccountTypeWithBalanceQueryEnum.TERM_DEPOSIT,
          ...listQuery,
        },
      }),
      this.apiClientService
        .esbRequest<EsbDepositListItemDto[]>({
          requestId: EsbRequestsEnum.DEPOSIT_LIST,
          user: currentUser,
          payload: {
            userId: currentUser.cif,
            productType: EsbProductTypeEnum.Deposit,
          },
        })
        .catch(() => []),
    ]);

    const esbMap = new Map(
      esbResult
        .filter((d) => d.status === "Active")
        .map((d) => [d.productIdent, d])
    );

    const matchingFccDeposits = fccResult.items.filter((fcc) =>
      esbMap.has(fcc.accountNumber)
    );

    const deposits = matchingFccDeposits.map((fccDeposit) =>
      this.formatterService.format(
        fccDeposit,
        esbMap.get(fccDeposit.accountNumber),
        currentUser,
        options
      )
    );

    const totals = deposits.reduce(
      (acc, d) => {
        acc.totalBalance += d.principalAmountEquivalentEGP;
        acc.totalEquivalentBalance += d.equivalentAmount;
        acc.equivalentCurrency = d.equivalentCurrency;
        return acc;
      },
      {
        totalBalance: 0,
        totalEquivalentBalance: 0,
        equivalentCurrency: options?.defaultCurrency,
      }
    );

    return {
      ...totals,
      deposits,
    };
  }
}
