import { Injectable } from "@nestjs/common";
import { CurrentUser, DepositDetailsOptions } from "@types";
import { EsbRequestsEnum, FccRequestsEnum } from "@enums";
import { EsbDepositDetailsItemDto, FccTermDepositDetailsDto } from "@dtos";
import { ApiAdapterClientService } from "@modules";
import { TermDepositDetailsFormatterService } from "./formatters/td-details-formatter.service";

@Injectable()
export class TermDepositDetailsService {
  constructor(
    private readonly apiClientService: ApiAdapterClientService,
    private readonly formatterService: TermDepositDetailsFormatterService
  ) {}

  async getDetails(
    depositId: string,
    currentUser: CurrentUser,
    options: DepositDetailsOptions = {}
  ) {
    const fccDetails =
      await this.apiClientService.fccRequest<FccTermDepositDetailsDto>({
        requestId: FccRequestsEnum.TERM_DEPOSIT_DETAILS,
        payload: { accountId: depositId },
        user: currentUser,
      });

    const esbDetails =
      await this.apiClientService.esbRequest<EsbDepositDetailsItemDto>({
        requestId: EsbRequestsEnum.DEPOSIT_DETAILS,
        payload: {
          depositNumber: fccDetails.number,
        },
      });

    return this.formatterService.format(fccDetails, esbDetails, options);
  }
}
