import { Injectable } from "@nestjs/common";
import { CurrentUser, DepositListOptions } from "@types";
import { BaseDepositFormatterService } from "./base-formatter.service";

@Injectable()
export class TermDepositListFormatterService extends BaseDepositFormatterService {
  format(
    fccItem: any,
    esbItem: any,
    user: CurrentUser,
    options: DepositListOptions = {}
  ) {
    return {
      id: this.generateId(fccItem.accountId, fccItem.accountNumber, user),
      name: fccItem.accountIban,
      type: fccItem.accountType,
      number: fccItem.accountNumber,
      code: esbItem.liabilityCode,
      tenor: this.formatTenor(esbItem.tenor, options.userLanguage),
      startDate: fccItem.tdStartDate,
      maturityDate: this.formatDate(esbItem.maturityDt),
      currency: {
        code: fccItem.currency,
        localized: this.i18n.translate(
          `global.CurrencyNames.${fccItem.currency}`,
          options.userLanguage
        ),
      },
      amount: fccItem.principalAmount,
      equivalentCurrency: fccItem.equivalentCurrency,
      equivalentAmount: fccItem.principalAmountEquivalent,
      principalAmountEquivalentEGP: fccItem.principalAmountEquivalentEGP,
      nickname: fccItem.nickname,
      interestRate: esbItem.interestRate,
    };
  }
}
