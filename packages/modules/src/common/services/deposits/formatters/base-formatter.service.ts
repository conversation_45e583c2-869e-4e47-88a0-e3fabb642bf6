import { EsbDepositDetailsItemDto } from "@dtos";
import { UserLanguage } from "@enums";
import {
  depositTenorFromString,
  encryptUserJson,
  pluralize,
  esbRenewalInstruction,
} from "@helpers";

import { DepositsLookupService, I18nLookupService } from "@modules";
import { Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { CurrentUser } from "@types";

@Injectable()
export abstract class BaseDepositFormatterService {
  constructor(
    protected readonly i18n: I18nLookupService,
    protected readonly config: ConfigService,
    protected readonly depositsLookupService: DepositsLookupService
  ) {}

  abstract format(...args: any[]): any;

  protected generateId(
    accountId: string,
    depositId: string,
    user: CurrentUser
  ): string {
    return encodeURIComponent(
      encryptUserJson(
        { accountId, depositId },
        this.config.get<string>("aesSecret"),
        user.userId
      )
    );
  }

  protected formatDate(dateStr: string): string {
    return new Intl.DateTimeFormat("en-GB", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    }).format(new Date(dateStr));
  }

  public formatTenor(
    rawTenor: string,
    language: UserLanguage = UserLanguage["en-US"]
  ) {
    const { value, unit, isVariable } = depositTenorFromString(rawTenor);
    return {
      tenor: rawTenor,
      localized: `${value} ${this.i18n.translate(
        pluralize(`global.units.${unit}`, value),
        language
      )}`,
      isVariable,
    };
  }

  protected formatRenewalInstruction(
    esbItem: EsbDepositDetailsItemDto,
    userLanguage: UserLanguage
  ) {
    const autoCapitalize = esbItem.depositProdInfo?.tdProdInfo?.capitalise;
    const renewalOption = esbItem.renewalOption;
    const instruction = esbRenewalInstruction(renewalOption, autoCapitalize);
    return this.i18n.translate(
      `deposits.renewalInstructions.${instruction}`,
      userLanguage
    );
  }
}
