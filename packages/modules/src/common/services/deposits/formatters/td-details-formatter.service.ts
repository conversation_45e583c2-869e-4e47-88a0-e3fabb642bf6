import { Injectable } from "@nestjs/common";
import { DepositListOptions } from "@types";
import { BaseDepositFormatterService } from "./base-formatter.service";
import { EsbDepositDetailsItemDto, FccTermDepositDetailsDto } from "@dtos";

@Injectable()
export class TermDepositDetailsFormatterService extends BaseDepositFormatterService {
  format(
    fccItem: FccTermDepositDetailsDto,
    esbItem: EsbDepositDetailsItemDto,
    options: DepositListOptions = {},
  ) {
    const { productCode } = esbItem.productInfo;

    return {
      code: productCode,
      productDetails: {
        name: esbItem.productInfo.productDescription,
        number: fccItem.number,
        amount: fccItem.principalAmount,
        currency: {
          code: fccItem.currency,
          localized: this.i18n.translate(
            `global.CurrencyNames.${fccItem.currency}`,
            options.userLanguage,
          ),
        },
        tenor: this.formatTenor(esbItem.tenor, options.userLanguage),
        valueDate: esbItem.originationDate,
        maturityDate: esbItem.maturityDate,
      },
      product: this.depositsLookupService.getProductDetails(productCode),
      interestDetails: {
        interestRate: esbItem.interest?.interestRate,
        interestPaymentFrequency:
          esbItem?.interest?.interestFrequency &&
          this.formatTenor(
            esbItem.interest.interestFrequency,
            options.userLanguage,
          ),
        nextCreditInterestDate: esbItem.interest.nextInterestDate,
        nextCreditInterest: esbItem.balances.interestBalance.amount,
        nextCreditInterestCurrencyCode:
          esbItem.balances.interestBalance.currencyCode,
        creditInterestAccountNumber: esbItem.interestLiquidationAccount,
        principleLiquidationAccount: esbItem.principleLiquidationAccount,
      },
      renewal: {
        enabled: esbItem.renewalOption,
        instruction: this.formatRenewalInstruction(
          esbItem,
          options.userLanguage,
        ),
      },
      collateral: {
        has: esbItem?.collateral?.length > 0,
        details: esbItem.collateral,
      },
      allowedRedemptionType: this.getAllowedRedemptionType(esbItem),
      maximumRedemptionAmount: esbItem.balances.availBalance,
      bookBalance: esbItem.balances.bookBalance,
      totalHeld: esbItem.balances.totalHeld,
      accounts: {
        principle: esbItem.principleLiquidationAccount,
        drawn: esbItem.drawAccount,
      },
    };
  }

  private getAllowedRedemptionType(esbItem: EsbDepositDetailsItemDto) {
    const { collateral, balances } = esbItem;
    const fullRedemption = !collateral || collateral.length === 0;
    const partialRedemption =
      balances.bookBalance.amount !== balances.totalHeld.amount;

    return { fullRedemption, partialRedemption };
  }
}
