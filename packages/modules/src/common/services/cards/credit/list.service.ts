import { CreditCardListItemDto } from "@dtos";
import {
  CardOperationEnum,
  CardStatusEnum,
  CurrencyEnum,
  FccRequestsEnum,
} from "@enums";
import {
  ApiAdapterClientService,
  FxService,
  I18nLookupService,
} from "@modules";
import { Injectable } from "@nestjs/common";
import { CardListOptions, CurrentUser } from "@types";

import { CreditCardDetailsService } from "./details.service";
import { encryptUserJson } from "@helpers";
import { ConfigService } from "@nestjs/config";

@Injectable()
export class CreditCardListService {
  constructor(
    private readonly creditCardDetailsService: CreditCardDetailsService,
    private readonly apiClientService: ApiAdapterClientService,
    private readonly fxService: FxService,
    private readonly i18n: I18nLookupService,
    private readonly config: ConfigService
  ) {}

  async geCreditCardsList(
    operation: CardOperationEnum,
    currentUser: CurrentUser,
    options: CardListOptions = {}
  ) {
    const list = await this.apiClientService.fccRequest<
      CreditCardListItemDto[]
    >({
      requestId: FccRequestsEnum.CREDIT_CARDS_LIST,
      payload: {},
      user: currentUser,
    });

    const detailedList = await Promise.all(
      list.map((card) =>
        this.creditCardDetailsService
          .getDetails(card.semaNumber, card.cardId, currentUser, {
            defaultCurrency: options.defaultCurrency,
            userLanguage: options?.userLanguage,
            calculateEquivalents: false,
          })
          .then((formatted) => {
            const id = encryptUserJson(
              {
                sema: card.semaNumber,
                id: card.cardId,
                maskedCardNumber: card.maskedCardNumber,
                accountId: card.accountId,
              },
              this.config.get("aesSecret"),
              currentUser.userId
            );
            return {
              id: encodeURIComponent(id),
              ...formatted,
              maskedCardNumber: card.maskedCardNumber,
            };
          })
      )
    );
    const displayableList = detailedList.filter((card) =>
      this.shouldDisplay(operation, card)
    );

    const { totalLimits, totalOutstandingBalance, totalAvailableBalance } =
      displayableList.reduce<{
        totalLimits: number;
        totalOutstandingBalance: number;
        totalAvailableBalance: number;
      }>(
        (totals, card) => {
          totals.totalLimits += card.totalLimit || 0;
          totals.totalOutstandingBalance += card.outstandingBalance || 0;
          totals.totalAvailableBalance += card.availableBalance || 0;
          return totals;
        },
        { totalLimits: 0, totalOutstandingBalance: 0, totalAvailableBalance: 0 }
      );

    const equivalents = {} as any;

    if (options?.calculateEquivalents) {
      const fxConvert = await this.fxService.convert(
        CurrencyEnum.EGP,
        options.defaultCurrency,
        currentUser
      );
      equivalents.equivalentCardsLimits = fxConvert(totalLimits);
      equivalents.equivalentOutstandingBalance = fxConvert(
        totalOutstandingBalance
      );
    }

    return {
      balanceLastUpdated: new Date().toISOString(),
      cards: displayableList ?? [],
      currency: {
        code: CurrencyEnum.EGP,
        localized: this.i18n.translate(
          `global.CurrencyNames.${CurrencyEnum.EGP}`,
          options?.userLanguage
        ),
      },
      defaultCurrency: {
        defaultCurrency: options.defaultCurrency,
        localized: this.i18n.translate(
          `global.CurrencyNames.${options.defaultCurrency}`,
          options?.userLanguage
        ),
      },
      totalCardsLimits: totalLimits,
      totalOutstandingBalance: totalOutstandingBalance,
      totalAvailableBalance: totalAvailableBalance,
      ...equivalents,
    };
  }

  shouldDisplay(operation: CardOperationEnum, card: any): boolean {
    switch (operation) {
      case CardOperationEnum.List:
        if (
          card.status.appStatus === CardStatusEnum.Closed &&
          card.outstandingBalance === 0
        ) {
          return false;
        }
        return true;

      default:
        return true;
    }
  }
}
