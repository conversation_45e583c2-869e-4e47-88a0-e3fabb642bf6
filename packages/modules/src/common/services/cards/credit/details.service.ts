import { EsbCreditCardDetailsDto } from "@dtos";
import { EsbRequestsEnum } from "@enums";
import { ApiAdapterClientService } from "@modules";
import { Injectable } from "@nestjs/common";
import { CreditCardDetailsFormatterService } from "./formatters/card-details-formatter.service";
import { CardDetailsOptions, CurrentUser } from "@types";

@Injectable()
export class CreditCardDetailsService {
  constructor(
    private readonly apiClientService: ApiAdapterClientService,
    private readonly detailsFormatterService: CreditCardDetailsFormatterService
  ) {}

  async getDetails(
    semaNumber: string,
    cardId: string,
    currentUser: CurrentUser,
    options: CardDetailsOptions = {}
  ) {
    const details =
      await this.apiClientService.esbRequest<EsbCreditCardDetailsDto>({
        requestId: EsbRequestsEnum.CREDIT_CARD_DETAILS,
        payload: {
          partyIdent: semaNumber,
          cardId,
        },
      });

    return this.detailsFormatterService.format(details, currentUser, options);
  }
}
