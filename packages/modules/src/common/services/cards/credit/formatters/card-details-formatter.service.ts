import { EsbCreditCardDetailsDto } from "@dtos";
import { CurrencyEnum } from "@enums";
import { getCreditCardAppStatus } from "@helpers";
import {
  CardProductsLookupService,
  FxService,
  I18nLookupService,
} from "@modules";
import { Injectable } from "@nestjs/common";
import { CardDetailsOptions, CurrentUser } from "@types";

@Injectable()
export class CreditCardDetailsFormatterService {
  constructor(
    protected readonly productsService: CardProductsLookupService,
    protected readonly i18n: I18nLookupService,
    protected readonly fxService: FxService
  ) {}

  async format(
    details: EsbCreditCardDetailsDto,
    currentUser: CurrentUser,
    options: CardDetailsOptions = {}
  ) {
    const {
      productCode,
      ownerParty,
      cardHolderName,
      cardCurrency,
      expiryDate,
      activationDate,
      totalLimit,
      availableCredit,
      totalCashLimit,
      availableCash,
      balances,
      lastStatementBalance,
      lastTransactionDate,
      minimumAmountDue,
      totalAmountDue,
      dueDate,
      cardStatus,
      cardStatusDesc,
      directDebitAccount,
      directDebitPercentage,
    } = details;

    const productDetails = this.productsService.getProductDetails(productCode);

    const equivalents = {} as any;
    if (
      options?.calculateEquivalents &&
      options.defaultCurrency &&
      cardCurrency !== options.defaultCurrency
    ) {
      try {
        const fxConvert = await this.fxService.convert(
          cardCurrency as CurrencyEnum, // Use the card's actual currency
          options.defaultCurrency,
          currentUser
        );
        equivalents.equivalentCardLimit = fxConvert(totalLimit);
        equivalents.equivalentOutstandingBalance = fxConvert(
          balances.currentBalance
        );
      } catch (error) {
        // If FX conversion fails, don't include equivalents
        console.warn("FX conversion failed for credit card details:", error);
      }
    }

    const appStatus = getCreditCardAppStatus(cardStatus);

    return {
      ...productDetails,
      ownerParty,
      holderName: cardHolderName,
      currency: {
        code: cardCurrency,
        localized: this.i18n.translate(
          `global.CurrencyNames.${cardCurrency}`,
          options?.userLanguage
        ),
      },
      status: {
        code: cardStatus,
        appStatus,
        localized: `${this.i18n.translate(`cards.appStatus.${appStatus}`, options.userLanguage)}`,
      },
      defaultCurrency: {
        code: options.defaultCurrency,
        localized: this.i18n.translate(
          `global.CurrencyNames.${options?.defaultCurrency}`,
          options?.userLanguage
        ),
      },
      expiryDate,
      activationDate,
      minimumAmountDue,
      totalAmountDue,
      dueDate,
      directDebitAccount,
      directDebitPercentage,
      lastStatementBalance,
      lastTransactionDate,
      heldAmount: balances.authorizedBalance,
      outstandingBalance: balances.currentBalance,
      totalLimit,
      availableBalance: availableCredit,
      ...equivalents,
      balanceLastUpdated: new Date().toISOString(),
    };
  }
}
