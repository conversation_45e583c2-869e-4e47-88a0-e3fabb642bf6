import { EsbInstallmentsListItemDto } from "@dtos";
import {
  CardProductsLookupService,
  FxService,
  I18nLookupService,
} from "@modules";
import { Injectable } from "@nestjs/common";
import { CardInstallmentsOptions } from "@types";
import { DateTime } from "luxon";

@Injectable()
export class InstallmentsFormatterService {
  constructor(
    protected readonly productsService: CardProductsLookupService,
    protected readonly i18n: I18nLookupService,
    protected readonly fxService: FxService
  ) {}

  format(
    installments: EsbInstallmentsListItemDto[],
    options: CardInstallmentsOptions = {}
  ) {
    return installments.map((item: EsbInstallmentsListItemDto) => {
      const startDate = DateTime.fromISO(item.openDate);
      const endDate = startDate.plus({ months: item.tenor });
      const paidInstallments =
        item.instalmentsCount - item.remainingInstallmentsCount;

      let nextInstallmentDate = null;
      if (item.remainingInstallmentsCount > 0) {
        nextInstallmentDate = startDate.plus({ months: paidInstallments });
      }

      return {
        merchantName: item.merchName,
        originalAmount: item.originalAmount,
        interestRate: +item.interestRate,
        monthlyInterestRate: +(item.interestRate / 12).toFixed(2),
        interestRateAmount: (item.originalAmount * +item.interestRate) / 100,
        monthlyPayment: item.instalmentAmount,
        remainingAmount: item.remainingAmount,
        installmentDetails: {
          installmentsCount: item.instalmentsCount,
          paidInstallments,
          remainingInstallments: item.remainingInstallmentsCount,
        },
        startDate: startDate.toISODate(),
        nextInstallmentDate: nextInstallmentDate
          ? nextInstallmentDate.toISODate()
          : null,
        maturityDate: endDate.toISODate(),
        endDate: endDate.toISODate(),
        campaignStatus: item.campaignStatus,

        currency: {
          code: item.currency,
          localized: this.i18n.translate(
            `global.CurrencyNames.${item.currency}`,
            options?.userLanguage
          ),
        },
      };
    });
  }
}
