import { Injectable } from "@nestjs/common";
import {
  ProductCodeEnum,
  ProductStatusCodeEnum,
  SubTransactionStatusCodeEnum,
  TransactionStatusCodeEnum,
  TransactionStatusEnum,
  UserLanguage,
} from "@enums";
import { I18nLookupService } from "../../../lookups";
import { TranslatedStatusRecord } from "@types";

@Injectable()
export class TransactionStatusService {
  constructor(private readonly i18n: I18nLookupService) {}

  getTranslatedStatusByProduct(
    productCode: ProductCodeEnum,
    userLanguage: UserLanguage,
    statusCode: TransactionStatusCodeEnum,
    subStatusCode: SubTransactionStatusCodeEnum,
    productStatus?: ProductStatusCodeEnum,
    subProductCode?: string
  ): TranslatedStatusRecord {
    switch (productCode) {
      case ProductCodeEnum.SecureEmail:
        return this.getSecureMailTranslatedStatus(
          userLanguage,
          statusCode,
          subStatusCode,
          productStatus
        );

      default:
        return this.getTranslatedStatus(
          productCode,
          userLanguage,
          statusCode,
          subStatusCode,
          productStatus,
          subProductCode
        );
    }
  }

  getSecureMailTranslatedStatus(
    userLanguage: UserLanguage,
    statusCode: TransactionStatusCodeEnum,
    subStatusCode: SubTransactionStatusCodeEnum,
    productStatus: ProductStatusCodeEnum
  ): TranslatedStatusRecord {
    return this.getTranslatedStatus(
      ProductCodeEnum.SecureEmail,
      userLanguage,
      statusCode,
      subStatusCode,
      productStatus
    );
  }

  getTranslatedStatus(
    productCode: ProductCodeEnum,
    userLanguage: UserLanguage,
    statusCode: TransactionStatusCodeEnum,
    subStatusCode: SubTransactionStatusCodeEnum,
    productStatus?: ProductStatusCodeEnum,
    subProductCode?: string
  ): TranslatedStatusRecord {
    if (productStatus) {
      if (
        statusCode === TransactionStatusCodeEnum.Acknowledged ||
        statusCode === TransactionStatusCodeEnum.Controlled
      ) {
        let appStatus: TransactionStatusEnum;

        if (
          productStatus === ProductStatusCodeEnum.Pending ||
          productStatus === ProductStatusCodeEnum.InProgress
        ) {
          appStatus = TransactionStatusEnum.InProgress;
        }

        if (productStatus === ProductStatusCodeEnum.New) {
          appStatus = TransactionStatusEnum.Approved;
        }

        if (productStatus === ProductStatusCodeEnum.Rejected) {
          appStatus = TransactionStatusEnum.RejectedBank;
        }

        if (appStatus) {
          return {
            translated: `${this.i18n.translate(`transactions.appStatus.${appStatus}`, userLanguage)}`,
            status: appStatus,
          };
        }

        return {
          translated: `${this.i18n.translate(`transactions.status.${statusCode}`, userLanguage)} (${this.i18n.translate(`transactions.productStatus.${productStatus}`, userLanguage)})`,
        };
      }
    }

    if (statusCode === TransactionStatusCodeEnum.Uncontrolled) {
      let appStatus: TransactionStatusEnum;

      if (subStatusCode === SubTransactionStatusCodeEnum.PendingAuthorise) {
        appStatus = TransactionStatusEnum.PendingAuthorization;
      }

      if (subStatusCode === SubTransactionStatusCodeEnum.PendingVerify) {
        appStatus = TransactionStatusEnum.PendingVerification;
      }

      if (appStatus) {
        return {
          translated: `${this.i18n.translate(`transactions.appStatus.${appStatus}`, userLanguage)}`,
          status: appStatus,
        };
      }
    }

    if (statusCode === TransactionStatusCodeEnum.Incomplete) {
      let appStatus: TransactionStatusEnum;

      if (subStatusCode === SubTransactionStatusCodeEnum.Returned) {
        appStatus = TransactionStatusEnum.RejectedCompany;
      }

      if (appStatus) {
        return {
          translated: `${this.i18n.translate(`transactions.appStatus.${appStatus}`, userLanguage)}`,
          status: appStatus,
        };
      }
    }

    if (statusCode === TransactionStatusCodeEnum.UncontrolledBank) {
      let appStatus: TransactionStatusEnum;

      if (subStatusCode === SubTransactionStatusCodeEnum.Sent) {
        appStatus = TransactionStatusEnum.InProgress;
      }

      if (appStatus) {
        return {
          translated: `${this.i18n.translate(`transactions.appStatus.${appStatus}`, userLanguage)}`,
          status: appStatus,
        };
      }
    }

    return {
      translated: `${this.i18n.translate(`transactions.status.${statusCode}`, userLanguage)} (${this.i18n.translate(`transactions.subStatus.${subStatusCode}`, userLanguage)})`,
    };
  }
}
