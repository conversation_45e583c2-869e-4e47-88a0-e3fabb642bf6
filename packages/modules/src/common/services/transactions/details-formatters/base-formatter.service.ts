import { FccGenericTnxResponseDto } from "@dtos";
import { TransactionFieldGroupEnum, UserLanguage } from "@enums";
import { I18nLookupService, TransactionStatusService } from "@modules";
import { Injectable } from "@nestjs/common";
import { TransactionDetailsField } from "@types";
import { castArray, Dictionary, get, keyBy } from "lodash";

type TransformInput = {
  input?: FccGenericTnxResponseDto;
  additionalFields?: Dictionary<any>;
};

type TransformOptions = {
  userLanguage?: UserLanguage;
};

export type TransactionDetailsFormatterMap = Record<
  string,
  {
    location?: "root" | "additional" | string;
    transform?: (
      value: any,
      input?: TransformInput,
      options?: TransformOptions
    ) => any;
    i18nKey?: string;
    group?: string[];
  }
>;

@Injectable()
export abstract class TransactionDetailsFormatterBaseService {
  protected abstract readonly customMappings: TransactionDetailsFormatterMap;
  protected readonly defaultMappings: TransactionDetailsFormatterMap = {
    tnxId: {
      location: "root",
      group: [TransactionFieldGroupEnum.System],
    },
    boComment: {
      location: "boComment.text",
      group: [
        TransactionFieldGroupEnum.System,
        TransactionFieldGroupEnum.BackOffice,
      ],
    },
    boReleaseDttm: {
      location: "root",
      group: [
        TransactionFieldGroupEnum.System,
        TransactionFieldGroupEnum.BackOffice,
      ],
    },
    returnComment: {
      location: "returnComment.text",
      group: [TransactionFieldGroupEnum.Company],
    },
  };

  constructor(
    protected i18n: I18nLookupService,
    protected statusService: TransactionStatusService
  ) {}

  mapToHumanReadable(
    input: FccGenericTnxResponseDto,
    userLanguage: UserLanguage
  ): Array<TransactionDetailsField> {
    const result: Array<TransactionDetailsField> = [];

    const additionalFields: Dictionary<any> = keyBy(
      castArray(input.additionalFields),
      "name"
    );

    for (const [key, config] of Object.entries({
      ...this.customMappings,
      ...this.defaultMappings,
    })) {
      const { location, transform, i18nKey, group } = config;
      const displayName = this.i18n.translate(
        i18nKey ?? `transactions.fieldMappings.${key}`,
        userLanguage
      );

      let rawValue: any;

      if (location === "root") {
        if (input[key] !== undefined) {
          rawValue = input[key];
        }
      } else if (location === "additional" && additionalFields) {
        const found = additionalFields[key];
        if (found) {
          rawValue = found.value;
        }
      } else {
        rawValue = get(input, location);
      }

      const value = transform
        ? transform(rawValue, { input, additionalFields }, { userLanguage })
        : rawValue;
      if (value) {
        result.push({
          key,
          label: displayName,
          value,
          group: group ?? [],
        });
      }
    }

    return result;
  }

  title(input: FccGenericTnxResponseDto, language: UserLanguage): string {
    return this.i18n.translate(
      `transactions.title.${input.productCode}${input.subProductCode}`,
      language
    );
  }

  format(
    input: FccGenericTnxResponseDto,
    userLanguage: UserLanguage
  ): Record<string, any> {
    return {
      title: this.title(input, userLanguage),
      fields: this.mapToHumanReadable(input, userLanguage),
    };
  }
}
