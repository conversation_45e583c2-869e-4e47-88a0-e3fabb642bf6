import { Injectable } from "@nestjs/common";
import {
  TransactionDetailsFormatterBaseService,
  TransactionDetailsFormatterMap,
} from "./base-formatter.service";
import { FccGenericTnxResponseDto } from "@dtos";
import {
  AccountsLookupService,
  DynamicListsLookupService,
  I18nLookupService,
  LocalBranchesLookupService,
} from "@modules";
import { TransactionStatusService } from "../status.service";
import {
  SecureMailFieldNamesEnum,
  SecureMailFieldTypeEnum,
  TransactionFieldGroupEnum,
  UserLanguage,
} from "@enums";
import { SecureMailFieldRecord } from "@types";

@Injectable()
export class SEDetailsFormatterService extends TransactionDetailsFormatterBaseService {
  protected customMappings: TransactionDetailsFormatterMap = {
    file_type_name: {
      location: "additional",
      group: [TransactionFieldGroupEnum.Product],
    },
    fullNameAr: {
      location: "additional",
      group: [TransactionFieldGroupEnum.Product],
      i18nKey: "secureMail.jsonFields.fullNameAr",
    },
    fullNameEn: {
      location: "additional",
      group: [TransactionFieldGroupEnum.Product],
      i18nKey: "secureMail.jsonFields.fullNameEn",
    },
    mobileNumber: {
      location: "additional",
      group: [TransactionFieldGroupEnum.Product],
      i18nKey: "secureMail.jsonFields.mobileNumber",
    },
    privilege: {
      location: "additional",
      group: [TransactionFieldGroupEnum.Product],
      i18nKey: "secureMail.jsonFields.privilege",
    },
    portalType: {
      location: "additional",
      group: [TransactionFieldGroupEnum.Product],
      i18nKey: "secureMail.jsonFields.portalType",
    },
    tokenType: {
      location: "additional",
      group: [TransactionFieldGroupEnum.Product],
      i18nKey: "secureMail.jsonFields.tokenType",
    },
    hasDelegation: {
      location: "additional",
      group: [TransactionFieldGroupEnum.Product],
      i18nKey: "secureMail.jsonFields.hasDelegation",
    },
    accessPermission: {
      location: "additional",
      group: [TransactionFieldGroupEnum.Product],
      i18nKey: "secureMail.jsonFields.accessPermission",
    },
    user: {
      location: "additional",
      group: [TransactionFieldGroupEnum.Product],
      i18nKey: "secureMail.jsonFields.user",
    },
    newMobileNumber: {
      location: "additional",
      group: [TransactionFieldGroupEnum.Product],
      i18nKey: "secureMail.jsonFields.newMobileNumber",
    },
    newEmail: {
      location: "additional",
      group: [TransactionFieldGroupEnum.Product],
      i18nKey: "secureMail.jsonFields.newEmail",
    },
    productType: {
      location: "additional",
      group: [TransactionFieldGroupEnum.Product],
      i18nKey: "secureMail.jsonFields.productType",
    },
    accountNumber: {
      location: "additional",
      group: [TransactionFieldGroupEnum.Product],
      i18nKey: "secureMail.jsonFields.accountNumber",
    },
    fundTransferNumber: {
      location: "additional",
      group: [TransactionFieldGroupEnum.Product],
      i18nKey: "secureMail.jsonFields.fundTransferNumber",
    },
    [SecureMailFieldNamesEnum.DeliveryBranch]: {
      location: "additional",
      group: [
        TransactionFieldGroupEnum.Product,
        TransactionFieldGroupEnum.Branch,
      ],
      i18nKey: `secureMail.jsonFields.${SecureMailFieldNamesEnum.DeliveryBranch}`,
      transform: (value) => {
        const branch = this.branchesLookup.getBranchById(value);
        return branch ? `${branch?.name} (${value})` : value;
      },
    },
    secureMailType: {
      location: "additional",
      group: [TransactionFieldGroupEnum.Product],
      i18nKey: "secureMail.jsonFields.secureMailType",
    },
    referenceNumber: {
      location: "additional",
      group: [TransactionFieldGroupEnum.Product],
      i18nKey: "secureMail.jsonFields.referenceNumber",
    },
    socialInsuranceNumber: {
      location: "additional",
      group: [TransactionFieldGroupEnum.Product],
      i18nKey: "secureMail.jsonFields.socialInsuranceNumber",
    },
    TaxRegistrationNumber: {
      location: "additional",
      group: [TransactionFieldGroupEnum.Product],
      i18nKey: "secureMail.jsonFields.TaxRegistrationNumber",
    },
    amount: {
      location: "additional",
      group: [TransactionFieldGroupEnum.Product],
      i18nKey: "secureMail.jsonFields.amount",
    },
    refundToAccountNumber: {
      location: "additional",
      group: [TransactionFieldGroupEnum.Product],
      i18nKey: "secureMail.jsonFields.refundToAccountNumber",
    },
    parentBranch: {
      location: "additional",
      group: [TransactionFieldGroupEnum.Product],
      i18nKey: "secureMail.jsonFields.parentBranch",
    },
    prevRequest: {
      location: "additional",
      group: [TransactionFieldGroupEnum.Product],
      i18nKey: "secureMail.jsonFields.prevRequest",
    },
    requestNumber: {
      location: "additional",
      group: [TransactionFieldGroupEnum.Product],
      i18nKey: "secureMail.jsonFields.requestNumber",
    },

    //CTCHP
    topic_description: {
      location: "additional",
      group: [TransactionFieldGroupEnum.Product],
      i18nKey: "secureMail.jsonFields.topicDescription",
    },
    free_format_text: {
      location: "root",
      group: [TransactionFieldGroupEnum.Product],
      i18nKey: "secureMail.jsonFields.freeText",
    },

    //common things
    currency: {
      location: "additional",
      group: [TransactionFieldGroupEnum.Product],
      i18nKey: "secureMail.jsonFields.currency",
      transform: (value, input, options) => {
        return this.i18n.translateOrDefault(
          `global.CurrencyNames.${value}`,
          options?.userLanguage,
          {},
          value
        );
      },
    },

    //Open new account
    [SecureMailFieldNamesEnum.AccountProductCode]: {
      location: "additional",
      group: [TransactionFieldGroupEnum.Product],
      i18nKey: `secureMail.jsonFields.${SecureMailFieldNamesEnum.AccountProductCode}`,
      transform: (value) => {
        const account = this.accountsLookup.getProduct(value);
        return account ? `${account?.name}` : value;
      },
    },
    [SecureMailFieldNamesEnum.SignatureAccountNumber]: {
      location: "additional",
      group: [TransactionFieldGroupEnum.Product],
      i18nKey: `secureMail.jsonFields.${SecureMailFieldNamesEnum.SignatureAccountNumber}`,
    },
    employeesNumber: {
      location: "additional",
      group: [TransactionFieldGroupEnum.Product],
      i18nKey: "secureMail.jsonFields.employeesNumber",
    },
    businessSize: {
      location: "additional",
      group: [TransactionFieldGroupEnum.Product],
      i18nKey: "secureMail.jsonFields.businessSize",
    },

    //Book Term Deposit
    [SecureMailFieldNamesEnum.RedemptionAccountNumber]: {
      location: "additional",
      group: [TransactionFieldGroupEnum.Product],
      i18nKey: "secureMail.jsonFields.redemptionAccountNumber",
    },
    [SecureMailFieldNamesEnum.DepositId]: {
      location: "additional",
      group: [TransactionFieldGroupEnum.Product],
      i18nKey: "secureMail.jsonFields.depositId",
    },
    [SecureMailFieldNamesEnum.RedemptionAmount]: {
      location: "additional",
      group: [TransactionFieldGroupEnum.Product],
      i18nKey: "secureMail.jsonFields.redemptionAmount",
    },
    [SecureMailFieldNamesEnum.RedemptionType]: {
      location: "additional",
      group: [TransactionFieldGroupEnum.Product],
      i18nKey: "secureMail.jsonFields.redemptionType",
      transform: (value, input, options) => {
        return this.i18n.translateOrDefault(
          `deposits.redemptionTypes.${value}`,
          options?.userLanguage,
          {},
          value
        );
      },
    },
  };

  constructor(
    protected readonly i18n: I18nLookupService,
    protected readonly statusService: TransactionStatusService,
    protected readonly listsLookup: DynamicListsLookupService,
    protected readonly branchesLookup: LocalBranchesLookupService,
    private readonly accountsLookup: AccountsLookupService
  ) {
    super(i18n, statusService);
  }

  format(
    input: FccGenericTnxResponseDto,
    userLanguage: UserLanguage
  ): Record<string, any> {
    if (input?.additionalFields && Array.isArray(input?.additionalFields)) {
      const freeFormatJson = input?.additionalFields?.find(
        (field) => field.name === "free_format_json"
      )?.value;

      if (freeFormatJson) {
        try {
          const jsonFieldsMap = JSON.parse(freeFormatJson);
          const jsonFields = Object.entries(jsonFieldsMap).map(
            ([key, value]) => ({
              name: key,
              value: this.getTextFieldValue(
                key,
                value as SecureMailFieldRecord,
                userLanguage
              ),
            })
          );

          input.additionalFields = [
            ...input.additionalFields,
            ...(jsonFields ?? []),
          ];
        } catch (error) {}
      }
    }

    return {
      title: this.title(input, userLanguage),
      fields: this.mapToHumanReadable(input, userLanguage),
    };
  }

  title(input: FccGenericTnxResponseDto, language: UserLanguage): string {
    const title = input.additionalFields.find(
      (f) => f.name === "upload_file_type"
    )?.value;

    return title ? title : super.title(input, language);
  }

  getTextFieldValue(
    key: string,
    field: SecureMailFieldRecord,
    userLanguage: UserLanguage
  ) {
    switch (field.type) {
      case SecureMailFieldTypeEnum.String:
        if (field.dynamicList) {
          const value = this.listsLookup.getItemValue(
            field.dynamicList,
            field.value,
            userLanguage
          );
          return `${value}`;
        }
        break;

      case SecureMailFieldTypeEnum.Boolean:
        return field.value ? "Yes" : "No";
    }
    return field.value ?? "";
  }
}
