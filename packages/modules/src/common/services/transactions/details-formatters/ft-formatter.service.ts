import { Injectable } from "@nestjs/common";
import {
  TransactionDetailsFormatterBaseService,
  TransactionDetailsFormatterMap,
} from "./base-formatter.service";
import { I18nLookupService } from "@modules";
import { TransactionStatusService } from "../status.service";
import { TransactionFieldGroupEnum, UserLanguage } from "@enums";
import { FccGenericTnxResponseDto } from "@dtos";

@Injectable()
export class FTDetailsFormatterService extends TransactionDetailsFormatterBaseService {
  protected customMappings: TransactionDetailsFormatterMap = {
    iss_date: {
      location: "root",
    },
    applicant_act_no: {
      location: "root",
      group: [TransactionFieldGroupEnum.Applicant],
    },
    applicant_act_cur_code: {
      location: "root",
      group: [TransactionFieldGroupEnum.Applicant],
    },
    releaser_email: {
      location: "additional",
    },
    payment_details_ft: {
      location: "additional",
    },
    payment_details_to_beneficiary: {
      location: "additional",
    },
    fx_exchange_rate: {
      location: "additional",
      group: [TransactionFieldGroupEnum.Fx],
    },
    fx_exchange: {
      transform: (value, { input, additionalFields }) => {
        const amount = additionalFields?.fx_exchange_rate_amt?.value;
        const currency = additionalFields?.fx_exchange_rate_cur_code?.value;
        if (amount && currency) {
          return `${amount} ${currency}`;
        }
      },
      group: [TransactionFieldGroupEnum.Fx],
    },

    amount: {
      transform: (value, { input }) => {
        return `${input.ftAmt} ${input.ftCurCode}`;
      },
      group: [TransactionFieldGroupEnum.Amount],
    },

    beneficiary_name: {
      location: "counterparties.counterparty.counterparty_name",
      group: [TransactionFieldGroupEnum.Beneficiary],
    },

    beneficiary_act_no: {
      location: "counterparties.counterparty.counterparty_act_no",
      group: [TransactionFieldGroupEnum.Beneficiary],
    },

    beneficiary_cur_code: {
      location: "counterparties.counterparty.counterparty_cur_code",
      group: [TransactionFieldGroupEnum.Beneficiary],
    },

    //IPN
    ipn_transfer_id: {
      location: "additional",
    },
  };

  constructor(
    protected readonly i18n: I18nLookupService,
    protected readonly statusService: TransactionStatusService
  ) {
    super(i18n, statusService);
  }
}
