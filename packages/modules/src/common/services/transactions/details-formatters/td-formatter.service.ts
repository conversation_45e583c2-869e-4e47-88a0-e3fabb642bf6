import { Injectable } from "@nestjs/common";
import {
  TransactionDetailsFormatterBaseService,
  TransactionDetailsFormatterMap,
} from "./base-formatter.service";
import { I18nLookupService } from "@modules";
import { TransactionStatusService } from "../status.service";
import { TransactionFieldGroupEnum, UserLanguage } from "@enums";
import { FccGenericTnxResponseDto } from "@dtos";
import {
  AppTdRenewalInstructionsMap,
  FccTdRenewalInstructionsMap,
} from "@constants";

@Injectable()
export class TDDetailsFormatterService extends TransactionDetailsFormatterBaseService {
  protected customMappings: TransactionDetailsFormatterMap = {
    iss_date: {
      location: "root",
    },
    applicant_act_no: {
      location: "root",
      group: [TransactionFieldGroupEnum.Applicant],
    },
    applicant_act_cur_code: {
      location: "root",
      group: [TransactionFieldGroupEnum.Applicant],
    },

    credit_act_no: {
      location: "additional",
      group: [TransactionFieldGroupEnum.Applicant],
    },
    credit_act_cur_code: {
      location: "additional",
      group: [TransactionFieldGroupEnum.Applicant],
    },

    amount: {
      transform: (value, { input }) => {
        return `${input.tdAmt} ${input.tdCurCode}`;
      },
      group: [TransactionFieldGroupEnum.Amount],
    },

    maturity_instruction_name: {
      transform: (value, { input }, options) => {
        const instruction = AppTdRenewalInstructionsMap?.[value] ?? "unkown";
        return this.i18n.translate(
          `deposits.renewalInstructions.${instruction}`,
          options.userLanguage
        );
      },
      location: "additional",
      group: [TransactionFieldGroupEnum.Product],
    },

    valueDate: {
      location: "root",
      i18nKey: "transactions.fieldMappings.start_date",
      group: [TransactionFieldGroupEnum.Product],
    },
  };

  constructor(
    protected readonly i18n: I18nLookupService,
    protected readonly statusService: TransactionStatusService
  ) {
    super(i18n, statusService);
  }
}
