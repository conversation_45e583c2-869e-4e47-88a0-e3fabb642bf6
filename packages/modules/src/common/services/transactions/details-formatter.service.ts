import { Injectable } from "@nestjs/common";
import { I18nLookupService } from "../../../lookups";
import { FccGenericTnxResponseDto } from "@dtos";
import { ProductCodeEnum, UserLanguage } from "@enums";
import { TransactionDetailsFormatterBaseService } from "./details-formatters/base-formatter.service";
import { FTDetailsFormatterService } from "./details-formatters/ft-formatter.service";
import { SEDetailsFormatterService } from "./details-formatters/se-formatter.service";
import { TransactionStatusService } from "./status.service";
import { TDDetailsFormatterService } from "./details-formatters/td-formatter.service";

@Injectable()
export class TransactionDetailsFormatterService {
  private readonly formatters: Partial<
    Record<ProductCodeEnum, TransactionDetailsFormatterBaseService>
  >;

  constructor(
    private readonly ftService: FTDetailsFormatterService,
    private readonly seService: SEDetailsFormatterService,
    private readonly tdService: TDDetailsFormatterService,
    private readonly statusService: TransactionStatusService
  ) {
    this.formatters = {
      [ProductCodeEnum.FundTransfer]: this.ftService,
      [ProductCodeEnum.SecureEmail]: this.seService,
      [ProductCodeEnum.TermDeposit]: this.tdService,
    };
  }

  format(
    input: FccGenericTnxResponseDto,
    productCode: ProductCodeEnum,
    userLanguage: UserLanguage
  ) {
    let productDetails = {};

    const formatter = this.formatters[productCode];
    if (formatter) {
      productDetails = formatter.format(input, userLanguage);
    }

    return {
      refId: input.refId,
      tnxId: input.tnxId,
      productCode: input.productCode,
      subProductCode: input.subProductCode,
      status: this.statusService.getTranslatedStatusByProduct(
        input.productCode,
        userLanguage,
        input.tnxStatCode,
        input.subTnxStatCode,
        input.prodStatCode
      ),
      inputDate: input.inpDttm,
      releaseDate: input?.releaseDttm ?? "",
      attachments: input?.attachments ?? [],
      ...productDetails,
    };
  }
}
