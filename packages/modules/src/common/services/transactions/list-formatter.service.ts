import { Injectable } from "@nestjs/common";
import { I18nLookupService } from "../../../lookups";
import { TransactionListItemDto } from "@dtos";
import {
  ProductCodeEnum,
  TransactionStatusCodeEnum,
  UserLanguage,
} from "@enums";
import { TransactionStatusService } from "./status.service";
import { FormattedTransactionListItem } from "@types";

@Injectable()
export class TransactionListFormatterService {
  private readonly formatters: Partial<
    Record<
      ProductCodeEnum,
      (
        input: TransactionListItemDto,
        lang: UserLanguage
      ) => Partial<FormattedTransactionListItem>
    >
  >;

  constructor(
    private readonly i18n: I18nLookupService,
    private readonly statusService: TransactionStatusService
  ) {
    this.formatters = {
      [ProductCodeEnum.FundTransfer]: this.formatFundTransfer,
      [ProductCodeEnum.SecureEmail]: this.formatSecureEmail,
      [ProductCodeEnum.MutualFund]: this.formatMutualFund,
      // Add more product codes here!
    };
  }

  format(
    input: TransactionListItemDto,
    language: UserLanguage
  ): FormattedTransactionListItem {
    const formatter =
      this.formatters[input.productCode] ?? this.defaultFormatter;
    const result = formatter(input, language);

    return {
      refId: input.refId,
      tnxId: input.tnxId,
      title: result.title ?? "",
      subTitle: result.subTitle ?? "",
      productCode: result.productCode ?? input.productCode,
      subProductCode: result.subProductCode ?? input.subProductCode,
      status: this.statusService.getTranslatedStatusByProduct(
        input.productCode,
        language,
        input.tnxStatCode ?? TransactionStatusCodeEnum.Acknowledged, //to be checked
        input.subTnxStatCode,
        input.prodStatCode
      ),
      date: input?.tnxDate?.date,
      time: input?.tnxDate?.time,
      inputter: {
        firstName: input.inputterFirstName,
        lastName: input.inputterLastName,
        loginId: input.inputterLoginId,
      },
    };
  }

  formatMany(
    inputs: TransactionListItemDto[],
    language: UserLanguage
  ): FormattedTransactionListItem[] {
    return inputs.map((input) => this.format(input, language));
  }

  private formatFundTransfer = (
    input: TransactionListItemDto,
    language: UserLanguage
  ) => ({
    title: this.i18n.translate(
      `transactions.title.${input.productCode}${input.subProductCode}`,
      language
    ),
    subTitle: `${input.tnxAmt} ${input.tnxCurCode}`,
  });

  private formatSecureEmail = (
    input: TransactionListItemDto,
    language: UserLanguage
  ) => ({
    title: input.seFileType ?? "",
    subTitle: "",
  });

  private formatMutualFund = (
    input: TransactionListItemDto,
    language: UserLanguage
  ) => ({
    productCode: ProductCodeEnum.FundTransfer,
    title: "Mutual Fund",
    subTitle: "Investment transaction",
  });

  private defaultFormatter = (
    input: TransactionListItemDto,
    language: UserLanguage
  ): Partial<FormattedTransactionListItem> => ({
    title: this.i18n.translate(
      `transactions.title.${input.productCode}${input.subProductCode}`,
      language
    ),
  });
}
