import { Global, Module } from "@nestjs/common";
import { TransactionStatusService } from "./services/transactions/status.service";
import { LookupsModule } from "../lookups";
import { TransactionDetailsFormatterService } from "./services/transactions/details-formatter.service";
import { FTDetailsFormatterService } from "./services/transactions/details-formatters/ft-formatter.service";
import { SEDetailsFormatterService } from "./services/transactions/details-formatters/se-formatter.service";
import {
  AccountsValidationService,
  CreditCardListService,
  TransactionListFormatterService,
  UsersService,
} from "./services";
import { CreditCardDetailsService } from "./services/cards/credit/details.service";
import { CreditCardDetailsFormatterService } from "./services/cards/credit/formatters/card-details-formatter.service";
import { FxModule } from "../fx";
import { TermDepositListFormatterService } from "./services/deposits/formatters/td-list-formatter.service";
import { TermDepositListService } from "./services/deposits/td-list.service";
import { TermDepositDetailsService } from "./services/deposits/td-details.service";
import { TermDepositDetailsFormatterService } from "./services/deposits/formatters/td-details-formatter.service";
import { InstallmentsFormatterService } from "./services/cards/credit/formatters/installments-formatter.service";
import { TDDetailsFormatterService } from "./services/transactions/details-formatters/td-formatter.service";
import { LoanListFormatterService } from "./services/loans/formatters/list.formatter.service";
import { LoanDetailsFormatterService } from "./services/loans/formatters/details.formatter";
import { LoansListService } from "./services/loans/list.service";
import { LoanDetailsService } from "./services/loans/details.service";

@Global()
@Module({
  imports: [LookupsModule, FxModule],
  controllers: [],
  providers: [
    //Transactions
    TransactionStatusService,
    FTDetailsFormatterService,
    SEDetailsFormatterService,
    TDDetailsFormatterService,
    TransactionDetailsFormatterService,
    TransactionListFormatterService,

    //Accounts
    AccountsValidationService,
    //Users
    UsersService,

    //Cards
    CreditCardDetailsFormatterService,
    CreditCardDetailsService,
    CreditCardListService,
    InstallmentsFormatterService,

    //Deposits
    TermDepositListService,
    TermDepositListFormatterService,
    TermDepositDetailsService,
    TermDepositDetailsFormatterService,

    //Loans
    LoanListFormatterService,
    LoanDetailsFormatterService,
    LoansListService,
    LoanDetailsService,
  ],
  exports: [
    TransactionStatusService,
    TransactionDetailsFormatterService,
    TransactionListFormatterService,
    AccountsValidationService,
    UsersService,
    CreditCardDetailsService,
    CreditCardDetailsFormatterService,
    CreditCardListService,
    InstallmentsFormatterService,
    TermDepositListService,
    TermDepositDetailsService,
    LoansListService,
    LoanDetailsService,
    LoanListFormatterService,
    LoanDetailsFormatterService,
  ],
})
export class CommonModule {}
