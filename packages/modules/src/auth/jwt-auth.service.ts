import { Injectable, Logger } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { RedisCacheService } from '../redis/services/redis-cache.service';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class JwtAuthService {
  private readonly logger = new Logger(JwtAuthService.name);

  constructor(
    private jwtService: JwtService,
    private redisCacheService: RedisCacheService,
    private configService: ConfigService,
  ) {}

  async verifyToken(token: string): Promise<any> {
    try {
      const secret = this.configService.get('jwt.secret');
      const decoded = this.jwtService.verify(token, { secret });

      this.logger.debug(`Token verified successfully: ${JSON.stringify(decoded)}`);

      // Validate token in Redis using jti
      if (decoded.jti) {
        const storedToken = await this.redisCacheService.get(decoded.jti);
        
        if (!storedToken) {
          throw new Error('Token has been revoked or expired in Redis');
        }
      }

      return decoded;
    } catch (error) {
      this.logger.error(`Token verification failed: ${error.message}`, error.stack);
      throw error;
    }
  }
}