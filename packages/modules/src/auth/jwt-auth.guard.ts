import { CanActivate, ExecutionContext, Injectable, UnauthorizedException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { JwtAuthService } from './jwt-auth.service';

@Injectable()
export class JwtAuthGuard implements CanActivate {
  constructor(
    private jwtAuthService: JwtAuthService,
    private reflector: Reflector,
  ) {
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const response = context.switchToHttp().getResponse();
    const token = this.extractTokenFromHeader(request);

    if (!token) {
      throw new UnauthorizedException('JWT token is missing');
    }

    try {
      const decoded = await this.jwtAuthService.verifyToken(token);
      
      // Set user object with userId and CIF from decoded token payload
      request.user = {
        ...decoded,
        userId: decoded.userId,
        cif: decoded.cif,
      };

      // Check if token is near expiry and add a header to inform the client
      const currentTime = Math.floor(Date.now() / 1000);
      const expiryTime = decoded.exp;
      const timeRemaining = expiryTime - currentTime;

      if (timeRemaining < 60) { // Less than 1 minute remaining
        response.setHeader('X-Token-Expiring', 'true');
      }

      return true;
    } catch (error) {
      throw new UnauthorizedException('Invalid JWT token');
    }
  }

  private extractTokenFromHeader(request: any): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}
