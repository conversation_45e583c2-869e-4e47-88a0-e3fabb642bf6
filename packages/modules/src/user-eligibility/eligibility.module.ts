import { Global, Module } from "@nestjs/common";
import { UserEligibilityService } from "./eligibility.service";
import { CommercialRegistryCheckService } from "./checks/commercial-registry.check";
import { RestrictionCodeCheckService } from "./checks/restriction-code.check";
import { HighRiskCheckService } from "./checks/high-risk.check";
import { CustomerTypeCheckService } from "./checks/customer-type.check";
import { KycGracePeriodCheck } from "./checks/kyc-grace-period.check";
import { KycCheckService } from "./checks/kyc.check";
import { SectorCodeCheckService } from "./checks/sector-code.check";
import { SegmentsRestrictionCodeCheckService } from "./checks/segments-restriction-code.check";

@Global()
@Module({
  providers: [
    UserEligibilityService,
    CommercialRegistryCheckService,
    RestrictionCodeCheckService,
    HighRiskCheckService,
    CustomerTypeCheckService,
    KycGracePeriodCheck,
    KycCheckService,
    SectorCodeCheckService,
    SegmentsRestrictionCodeCheckService,
    {
      provide: "EligibilityChecks",
      useFactory: (
        cr: CommercialRegistryCheckService,
        rc: RestrictionCodeCheckService,
        hr: HighRiskCheckService,
        ct: CustomerTypeCheckService,
        gp: KycGracePeriodCheck,
        kyc: KycCheckService,
        sc: SectorCodeCheckService,
        src: SegmentsRestrictionCodeCheckService
      ) => [cr, rc, hr, ct, gp, kyc, sc, src],
      inject: [
        CommercialRegistryCheckService,
        RestrictionCodeCheckService,
        HighRiskCheckService,
        CustomerTypeCheckService,
        KycGracePeriodCheck,
        KycCheckService,
        SectorCodeCheckService,
        SegmentsRestrictionCodeCheckService,
      ],
    },
  ],
  exports: [UserEligibilityService],
})
export class UserEligibilityModule {}
