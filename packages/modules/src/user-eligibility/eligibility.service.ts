import { Inject, Injectable, Logger } from "@nestjs/common";
import { eligibilityPolicyMap } from "./eligibility-policy.map";
import { UserEligibilityCheck } from "./eligibility-check.interface";
import { CustomerDetails } from "@types";
import { I18nLookupService } from "../lookups";
import { UserLanguage } from "@enums";

@Injectable()
export class UserEligibilityService {
  private readonly logger = new Logger(UserEligibilityService.name);

  private checkMap: Map<string, UserEligibilityCheck>;

  constructor(
    @Inject("EligibilityChecks") readonly checks: UserEligibilityCheck[],
    protected readonly i18n: I18nLookupService
  ) {
    this.checkMap = new Map(checks.map((check) => [check.name, check]));
  }

  evaluate(
    operation: string,
    customer: CustomerDetails,
    userLanguage: UserLanguage
  ): {
    eligible: boolean;
    reasons: Record<string, string>[];
    hints: Record<string, string>[];
  } {
    const policy = eligibilityPolicyMap[operation] ?? [];
    const reasons: Record<string, string>[] = [];
    const hints: Record<string, string>[] = [];

    for (const { checkName, config } of policy) {
      const check = this.checkMap.get(checkName);
      if (!check) {
        throw new Error(`Check not found: ${checkName}`);
      }

      const result = check.check(customer, config);
      this.logger.debug(`${checkName} check is ${result.passed}`);
      if (!result.passed && result.reasonCode) {
        reasons.push({
          message: this.i18n.translate(
            `messages.userEligibilityChecks.${result.reasonCode}`,
            userLanguage
          ),
          code: result.reasonCode,
        });
      }

      if (result.hintCode) {
        hints.push({
          message: this.i18n.translate(
            `messages.userEligibilityChecks.${result.hintCode}`,
            userLanguage
          ),
          code: result.hintCode,
        });
      }
    }

    return {
      eligible: reasons.length === 0,
      reasons,
      hints,
    };
  }
}
