import { Injectable, Logger } from "@nestjs/common";
import {
  UserEligibilityCheck,
  UserEligibilityCheckResult,
} from "../eligibility-check.interface";
import { CustomerDetails } from "@types";

type RestrictionConfig =
  | { type: "Some"; blockedCodes: number[] }
  | { type: "All" };

@Injectable()
export class RestrictionCodeCheckService implements UserEligibilityCheck {
  private readonly logger = new Logger(RestrictionCodeCheckService.name);

  name = "RestrictionCodeCheck";

  private readonly failResult: UserEligibilityCheckResult = {
    passed: false,
    reasonCode: "restrictedCustomer",
  };

  check(
    customer: CustomerDetails,
    config?: RestrictionConfig
  ): UserEligibilityCheckResult {
    if (!config) return { passed: true };

    const code = Number(customer?.restriction?.code);
    if (this.isRestricted(code, config)) {
      this.logger.debug(`${customer.userId} has restriction code ${code}`);
      return this.failResult;
    }

    return { passed: true };
  }

  private isRestricted(code: number, config: RestrictionConfig): boolean {
    if (config.type === "Some") {
      return code > 0 && config.blockedCodes.includes(code);
    }
    if (config.type === "All") {
      return code > 0;
    }
    return false;
  }
}
