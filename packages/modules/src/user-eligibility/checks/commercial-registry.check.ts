import { Injectable } from "@nestjs/common";
import { DateTime } from "luxon";
import {
  UserEligibilityCheck,
  UserEligibilityCheckResult,
} from "../eligibility-check.interface";
import { CustomerDetails } from "@types";

@Injectable()
export class CommercialRegistryCheckService implements UserEligibilityCheck {
  name = "CommercialRegistryCheck";

  check(customer: CustomerDetails): UserEligibilityCheckResult {
    if (!customer?.corporatePartyInfo?.commercialRegExpiryDate) {
      return {
        passed: false,
        reasonCode: "comRegMissing",
      };
    }

    const expiry = DateTime.fromISO(
      customer?.corporatePartyInfo?.commercialRegExpiryDate
    );
    if (expiry < DateTime.now()) {
      return {
        passed: false,
        reasonCode: "comRegExpired",
      };
    }

    return { passed: true };
  }
}
