import { Injectable } from "@nestjs/common";
import {
  UserEligibilityCheck,
  UserEligibilityCheckResult,
} from "../eligibility-check.interface";
import { CustomerDetails } from "@types";
import { PostingRestrictionCodeEnum } from "@enums";

@Injectable()
export class KycCheckService implements UserEligibilityCheck {
  name = "KycCheck";

  check(customer: CustomerDetails): UserEligibilityCheckResult {
    const code = Number(customer?.restriction?.code);
    const isRestricted = code === PostingRestrictionCodeEnum.EXPIRED_KYC;

    if (isRestricted) {
      return { passed: false, reasonCode: "kycRestriction" };
    }

    return { passed: true };
  }
}
