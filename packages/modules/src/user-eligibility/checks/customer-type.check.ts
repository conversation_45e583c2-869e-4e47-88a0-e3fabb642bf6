import { Injectable, Logger } from "@nestjs/common";
import {
  UserEligibilityCheck,
  UserEligibilityCheckResult,
} from "../eligibility-check.interface";
import { CustomerDetails } from "@types";

@Injectable()
export class CustomerTypeCheckService implements UserEligibilityCheck {
  private readonly logger = new Logger(CustomerTypeCheckService.name);

  name = "CustomerTypeCheck";

  check(
    customer: CustomerDetails,
    config?: { blockedCodes: string[] }
  ): UserEligibilityCheckResult {
    const blockedCodes = config?.blockedCodes ?? [];
    if (blockedCodes.includes(customer?.typeCode)) {
      this.logger.debug(
        `${customer.userId} has Customer type ${customer.typeCode}`
      );
      return {
        passed: false,
        reasonCode: "restrictedCustomer",
      };
    }

    return { passed: true };
  }
}
