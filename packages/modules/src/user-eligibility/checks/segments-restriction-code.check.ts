import { Injectable, Logger } from "@nestjs/common";
import {
  UserEligibilityCheck,
  UserEligibilityCheckResult,
} from "../eligibility-check.interface";
import { CustomerDetails } from "@types";
import { UserSegmentEnum } from "@enums";

@Injectable()
export class SegmentsRestrictionCodeCheckService
  implements UserEligibilityCheck
{
  private readonly logger = new Logger(
    SegmentsRestrictionCodeCheckService.name
  );

  name = "SegmentsRestrictionCodeCheck";

  check(
    customer: CustomerDetails,
    config?: { segments: UserSegmentEnum[]; blockedCodes: number[] }
  ): UserEligibilityCheckResult {
    const segments = config?.segments ?? [];
    if (segments.includes(customer.segment as UserSegmentEnum)) {
      const blockedCodes = config?.blockedCodes ?? [];
      const code = Number(customer?.restriction?.code);
      if (code && blockedCodes.includes(code)) {
        this.logger.debug(`${customer.userId} has restriction code ${code}`);
        return {
          passed: false,
          reasonCode: "restrictedCustomer",
        };
      }
    }

    return { passed: true };
  }
}
