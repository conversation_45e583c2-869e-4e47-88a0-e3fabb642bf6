import { Injectable, Logger } from "@nestjs/common";
import {
  UserEligibilityCheck,
  UserEligibilityCheckResult,
} from "../eligibility-check.interface";
import { CustomerDetails } from "@types";

@Injectable()
export class SectorCodeCheckService implements UserEligibilityCheck {
  private readonly logger = new Logger(SectorCodeCheckService.name);

  name = "SectorCodeCheck";

  check(
    customer: CustomerDetails,
    config?: { blockedSectors: string[] }
  ): UserEligibilityCheckResult {
    const blockedSectors = config?.blockedSectors ?? [];
    if (blockedSectors.includes(String(customer?.sector))) {
      this.logger.debug(
        `${customer.userId} has blocked sector ${customer.sector}`
      );
      return {
        passed: false,
        reasonCode: "restrictedCustomer",
      };
    }

    return { passed: true };
  }
}
