import { Injectable } from "@nestjs/common";
import {
  UserEligibilityCheck,
  UserEligibilityCheckResult,
} from "../eligibility-check.interface";
import { CustomerDetails } from "@types";
import { UserRiskLevelEnum } from "@enums";

@Injectable()
export class HighRiskCheckService implements UserEligibilityCheck {
  name = "HighRiskCheck";

  check(customer: CustomerDetails): UserEligibilityCheckResult {
    if (
      customer.riskLevel === UserRiskLevelEnum.HIGH &&
      !customer?.kyc?.complianceApproved
    ) {
      return {
        passed: false,
        reasonCode: "highRisk",
      };
    }
    return { passed: true };
  }
}
