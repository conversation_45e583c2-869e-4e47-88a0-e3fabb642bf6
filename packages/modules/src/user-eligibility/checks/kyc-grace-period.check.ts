import { Injectable } from "@nestjs/common";
import { DateTime } from "luxon";

import {
  UserEligibilityCheck,
  UserEligibilityCheckResult,
} from "../eligibility-check.interface";
import { CustomerDetails } from "@types";
import { PostingRestrictionCodeEnum } from "@enums";

@Injectable()
export class KycGracePeriodCheck implements UserEligibilityCheck {
  name = "KycGracePeriodCheck";

  check(
    customer: CustomerDetails,
    config: { requireValidKyc: boolean }
  ): UserEligibilityCheckResult {
    const nextReviewDateStr = customer?.kyc?.nextReview?.date;
    const restrictionCode = Number(customer?.restriction?.code);

    if (!nextReviewDateStr) {
      return { passed: true };
    }

    const nextReviewDate = DateTime.fromISO(nextReviewDateStr);
    const now = DateTime.now();

    const isExpired = nextReviewDate < now;
    const isNotRestricted =
      restrictionCode !== PostingRestrictionCodeEnum.EXPIRED_KYC;

    if (isExpired && isNotRestricted) {
      return config.requireValidKyc
        ? { passed: false, reasonCode: "kycGracePeriod" }
        : { passed: true, hintCode: "kycGracePeriod" };
    }

    return { passed: true };
  }
}
