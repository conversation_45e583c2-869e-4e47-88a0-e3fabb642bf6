import {
  BlockedSectorCodeEnum,
  PostingRestrictionCodeEnum,
  UserSegmentEnum,
  UserTypeCodeEnum,
} from "@enums";

export const eligibilityPolicyMap = {
  openAccount: [
    {
      checkName: "KycCheck",
    },
    {
      checkName: "KycGracePeriodCheck",
      config: {
        requireValidKyc: false,
      },
    },
    { checkName: "CommercialRegistryCheck" },
    {
      checkName: "CustomerTypeCheck",
      config: {
        blockedCodes: [
          UserTypeCodeEnum.BadClient,
          UserTypeCodeEnum.FatcaRecalcitrant,
          UserTypeCodeEnum.UncooperativeCustomer,
          UserTypeCodeEnum.UncooperativePAUB,
        ],
      },
    },
    {
      checkName: "RestrictionCodeCheck",
      config: {
        type: "Some",
        blockedCodes: [
          PostingRestrictionCodeEnum.AML_RESTRICTION,
          PostingRestrictionCodeEnum.SANCTION_BLOCK_DEBIT,
        ],
      },
    },
    { checkName: "HighRiskCheck" },
  ],
  bookTermDeposit: [
    {
      checkName: "KycCheck",
    },
    {
      checkName: "KycGracePeriodCheck",
      config: {
        requireValidKyc: false,
      },
    },
    { checkName: "CommercialRegistryCheck" },
    { checkName: "HighRiskCheck" },

    {
      checkName: "CustomerTypeCheck",
      config: {
        blockedCodes: [
          UserTypeCodeEnum.BadClient,
          UserTypeCodeEnum.FatcaRecalcitrant,
          UserTypeCodeEnum.UncooperativeCustomer,
          UserTypeCodeEnum.UncooperativePAUB,
          UserTypeCodeEnum.OffshoreEntity,
        ],
      },
    },
    {
      checkName: "RestrictionCodeCheck",
      config: {
        type: "Some",
        blockedCodes: [
          PostingRestrictionCodeEnum.AML_RESTRICTION,
          PostingRestrictionCodeEnum.SANCTION_BLOCK_DEBIT,
        ],
      },
    },
    {
      checkName: "SectorCodeCheck",
      config: {
        blockedSectors: [BlockedSectorCodeEnum.NonProfitOrganization],
      },
    },
    {
      checkName: "SegmentsRestrictionCodeCheck",
      config: {
        segments: [UserSegmentEnum.FinancialInclusion],
        blockedCodes: [PostingRestrictionCodeEnum.EASY_LITE_LIMIT_BREACH],
      },
    },
  ],
  redeemTermDeposit: [
    {
      checkName: "KycCheck",
    },
    {
      checkName: "KycGracePeriodCheck",
      config: {
        requireValidKyc: false,
      },
    },
    { checkName: "CommercialRegistryCheck" },
    { checkName: "HighRiskCheck" },

    {
      checkName: "CustomerTypeCheck",
      config: {
        blockedCodes: [
          UserTypeCodeEnum.BadClient,
          UserTypeCodeEnum.FatcaRecalcitrant,
          UserTypeCodeEnum.UncooperativeCustomer,
          UserTypeCodeEnum.UncooperativePAUB,
          UserTypeCodeEnum.OffshoreEntity,
        ],
      },
    },
    {
      checkName: "RestrictionCodeCheck",
      config: {
        type: "All",
      },
    },
    {
      checkName: "SectorCodeCheck",
      config: {
        blockedSectors: [BlockedSectorCodeEnum.NonProfitOrganization],
      },
    },
    {
      checkName: "SegmentsRestrictionCodeCheck",
      config: {
        segments: [UserSegmentEnum.FinancialInclusion],
        blockedCodes: [PostingRestrictionCodeEnum.EASY_LITE_LIMIT_BREACH],
      },
    },
  ],
};
