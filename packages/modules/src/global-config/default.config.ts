import { EnvironmentEnum } from "@enums";

export const defaultConfig = () => ({
  port: parseInt(process.env.PORT, 10) || 3000,
  host: process.env.HOST || "0.0.0.0",
  environment: process.env.NODE_ENV || EnvironmentEnum.Local, //one of EnvironmentEnum values
  timezone: process.env.DEFAULT_TIMEZONE || "Asia/Riyadh",
  aesSecret: process.env.AES_SECRET || "2b7e151628aed2a6abf7158809cf4f3c",
  jwt: {
    secret: process.env.JWT_SECRET || "super_secret_321",
    expiresIn: process.env.JWT_EXPIRES_IN || "1h",
    refreshSecret: process.env.JWT_SECRET_REFRESH,
    refreshExpiresIn: process.env.JWT_EXPIRES_IN_REFRESH || "300s",
    tokenExpiry: parseInt(process.env.JWT_TOKEN_EXPIRY, 10) || 540, // 9 minutes in seconds
    redisTokenTtl: parseInt(process.env.JWT_REFRESH_TOKEN_EXPIRY, 10) || 600, // 10 minutes in seconds
  },
  redis: {
    host: process.env.REDIS_HOST || "localhost",
    port: process.env.REDIS_PORT || 6379,
    password: process.env.REDIS_PASS,
    ttl: process.env.REDIS_TTL || 3600,
    db: process.env.REDIS_DB || 0,
  },
  redisSentinel: {
    enabled: String(process.env.REDIS_SENTINEL_ENABLED) === "true" || false,
    namespace:
      process.env.REDIS_SENTINEL_NAMESPACE || "core-banking-redis-namespace",
    host: process.env.REDIS_SENTINEL_HOST || "localhost",
    port: process.env.REDIS_SENTINEL_PORT || 26379,
    password: process.env.REDIS_SENTINEL_PASSWORD,
  },
  throttling: {
    limit: process.env.THROTTLING_LIMIT || 2,
    ttl: process.env.THROTTLING_TTL || 10,
  },
  upload: {
    maxSize: process.env.UPLOAD_MAX_SIZE || 5, // in MB
  },
  fastify: {
    bodyLimit: process.env.FASTIFY_BODYLIMIT || 2, // in MB
  },
  lookupsAPIServiceURL: process.env.LOOKUPS_API_SERVICE_URL,
  apiAdapter: {
    url: process.env.API_ADAPTER_SERVICE_URL,
    timeout: process.env.API_ADAPTER_TIMEOUT || 180, //timeout to the api adapter only not the full lifecycle
  },
});
