import { Module, DynamicModule, Global } from "@nestjs/common";
import { ConfigModule as NestConfigModule } from "@nestjs/config";
import { defaultConfig } from "./default.config";
import { GlobalConfigService } from "./global-config.service";

@Global()
@Module({})
export class GlobalConfigModule {
  static forRoot(
    ...featureConfigs: Array<() => Record<string, any>>
  ): DynamicModule {
    return {
      module: GlobalConfigModule,
      imports: [
        NestConfigModule.forRoot({
          load: [defaultConfig, ...featureConfigs],
          isGlobal: true,
        }),
      ],
      providers: [GlobalConfigService],
      exports: [GlobalConfigService],
    };
  }
}
