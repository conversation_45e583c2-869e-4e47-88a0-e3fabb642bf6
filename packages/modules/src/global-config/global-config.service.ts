import { EnvironmentEnum } from "@enums";
import { Injectable } from "@nestjs/common";
import { ConfigService as NestConfigService } from "@nestjs/config";

@Injectable()
export class GlobalConfigService extends NestConfigService {
  isLocalEnvironment() {
    return this.get("environment") === EnvironmentEnum.Local;
  }

  /*
    Example: this.config.getSet('badCustomerRestrictions', ',', Number)
    will get badCustomerRestrictions key when split by comma, then apply Number func
  */
  getSet<T = string>(
    key: string,
    delimiter = ",",
    transformer: (value: string) => T = (value) => value as T
  ): Set<T> {
    const value = this.get<string>(key);
    return value ? new Set(value.split(delimiter).map(transformer)) : new Set();
  }

  getMongoConfig() {
    const host = this.get<string>("mongo.host");
    const port = this.get<number>("mongo.port");
    const database = this.get<string>("mongo.database");
    const user = this.get<string>("mongo.user");
    const password = this.get<string>("mongo.password");

    return user && password
      ? `mongodb://${user}:${password}@${host}:${port}/${database}`
      : `mongodb://${host}:${port}/${database}`;
  }
}
