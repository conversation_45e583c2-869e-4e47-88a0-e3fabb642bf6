// redis.module.ts
import { DynamicModule, Global, Module, Provider } from "@nestjs/common";
import { RedisCacheService } from "./services/redis-cache.service";
import { REDIS_OPTIONS } from "./constants";
import { Reflector } from "@nestjs/core";
import { CacheInterceptor } from "./interceptors/cache.interceptor";
import { RedisOptions } from "./redis.interfaces";
import {
  RedisApiAdapterService,
  RedisAttachmentsService,
  RedisTnxService,
} from "./services";
import { ConfigService } from "@nestjs/config";
import {
  CommonRedisOptions,
  SentinelConnectionOptions,
  StandaloneConnectionOptions,
} from "ioredis";
import { GlobalConfigService } from "../global-config";
import { RedisUsersService } from "./services/redis-users.service";

// redis.module.ts
@Global()
@Module({})
export class RedisModule {
  static forRootAsync(): DynamicModule {
    return {
      module: RedisModule,
      imports: [],
      providers: [
        {
          provide: REDIS_OPTIONS,
          useFactory: async (configService: GlobalConfigService) => {
            let clientOptions = {
              host: configService.get("redis.host"),
              port: configService.get("redis.port"),
              password: configService.get("redis.password"),
              db: configService.get("redis.db"),
              enableReadyCheck: true,
              maxRetriesPerRequest: 3,
            } as CommonRedisOptions &
              SentinelConnectionOptions &
              StandaloneConnectionOptions;

            const sentinelEnabled = configService.get<boolean>(
              "redisSentinel.enabled"
            );

            if (sentinelEnabled) {
              const hosts = configService.getSet("redisSentinel.host");
              const sentinels = Array.from(hosts).map((host) => ({
                host: host.trim(),
                port: parseInt(configService.get<string>("redisSentinel.port")),
              }));

              clientOptions = {
                name: configService.get<string>("redisSentinel.namespace"),
                sentinels,
                sentinelPassword: configService.get<string>(
                  "redisSentinel.password"
                ),
                password: configService.get<string>("redisSentinel.password"),
              };
            }

            return {
              ttl: configService.get<number>("redis.ttl"),
              clientOptions,
            } as RedisOptions;
          },
          inject: [GlobalConfigService],
        },
        RedisCacheService,
        RedisAttachmentsService,
        RedisTnxService,
        RedisApiAdapterService,
        RedisUsersService,
        Reflector,
        CacheInterceptor,
      ],
      exports: [
        RedisCacheService,
        RedisAttachmentsService,
        RedisTnxService,
        CacheInterceptor,
        RedisApiAdapterService,
        RedisUsersService,
      ],
    };
  }
}
