import { Injectable } from "@nestjs/common";
import { RedisCacheService } from "./redis-cache.service";
import { CacheOptions } from "@types";
import { ApiAdapterRequestDto } from "@dtos";
import { CacheScope } from "@enums";
import { createHash } from "crypto";
import * as stringify from "json-stable-stringify";

@Injectable()
export class RedisApiAdapterService extends RedisCacheService {
  async getOrSet<T>(
    request: Partial<ApiAdapterRequestDto>,
    options: CacheOptions,
    fn: () => Promise<T>
  ): Promise<T> {
    if (!options.enabled) return await fn();

    const key = this.generateKey(request, options.scope);
    const cached = await this.get<string>(key);
    if (cached) return JSON.parse(cached);

    const result = await fn();
    await this.set(key, JSON.stringify(result), { ttl: options.ttl });
    return result;
  }

  generateKey(
    request: Partial<ApiAdapterRequestDto>,
    scope: CacheScope
  ): string {
    const { user, ...requestObj } = request;

    const requestHash = createHash("md5")
      .update(stringify(requestObj))
      .digest("hex");

    if (scope === CacheScope.USER) {
      return `${request.requestId}-${user.jti}-${requestHash}`;
    }

    return `${request.requestId}-${requestHash}`;
  }
}
