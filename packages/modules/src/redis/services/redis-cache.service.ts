import { Inject, Injectable, Logger, On<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@nestjs/common";
import { Redis } from "ioredis";
import { REDIS_OPTIONS } from "../constants";
import { CacheSetOptions, RedisOptions } from "../redis.interfaces";

@Injectable()
export class RedisCacheService implements OnModuleDestroy {
  protected readonly logger = new Logger(RedisCacheService.name);

  private client: Redis;

  constructor(
    @Inject(REDIS_OPTIONS)
    private readonly options: RedisOptions
  ) {
    this.client = new Redis(options.clientOptions);
  }

  async onModuleDestroy() {
    await this.client.quit();
  }

  async set<T>(
    key: string,
    value: T,
    options?: CacheSetOptions
  ): Promise<void> {
    const ttl = options?.ttl ?? this.options.ttl;
    const serialized = JSON.stringify(value);

    if (ttl) {
      await this.client.set(key, serialized, "EX", ttl);
    } else {
      await this.client.set(key, serialized);
    }
  }

  async get<T>(key: string): Promise<T | null> {
    const value = await this.client.get(key);
    return value ? JSON.parse(value) : null;
  }

  async del(key: string): Promise<void> {
    await this.client.del(key);
  }

  async keys(pattern: string): Promise<string[]> {
    return this.client.keys(pattern);
  }

  async flushAll(): Promise<void> {
    await this.client.flushall();
  }

  async deleteByPattern(pattern: string): Promise<number> {
    this.logger.debug(`deleting ${pattern} pattern from redis`);

    let totalDeleted = 0;

    const stream = this.client.scanStream({
      match: pattern,
    });

    for await (const keys of stream) {
      if (keys.length > 0) {
        const deletedCount = await this.client.del(...keys);
        totalDeleted += deletedCount;
      }
    }
    this.logger.debug(`${totalDeleted} items have been deleted from redis`);

    return totalDeleted;
  }

  // Additional health check method
  async ping(): Promise<string> {
    return this.client.ping();
  }
}
