import { Injectable, Logger } from "@nestjs/common";
import { RedisCacheService } from "./redis-cache.service";
import { CurrentUser } from "@types";

@Injectable()
export class RedisUsersService extends RedisCacheService {
  protected readonly logger = new Logger(RedisUsersService.name);

  async invalidateUserData(user: CurrentUser): Promise<void> {
    // api calls pattern
    const pattern = `*-${user.jti}-*`;
    try {
      await this.deleteByPattern(pattern);
    } catch (error) {
      this.logger.error(error);
    }
  }
}
