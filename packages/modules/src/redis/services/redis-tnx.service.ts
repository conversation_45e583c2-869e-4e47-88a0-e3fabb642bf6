import { BadRequestException, Injectable } from "@nestjs/common";
import { RedisCacheService } from "./redis-cache.service";
import { CurrentUser, FCCFileUpload } from "@types";
import { UploadAttachmentDto } from "@dtos";
import { CacheSetOptions } from "../redis.interfaces";

@Injectable()
export class RedisTnxService extends RedisCacheService {
  async setTnx(
    user: CurrentUser,
    product: string,
    tnxObject: any
  ): Promise<void> {
    const key = this.getKey(user, product);
    return this.set(key, tnxObject, {
      ttl: 3600,
    });
  }

  async getTnx<T>(user: CurrentUser, product: string): Promise<T> {
    const tnx = await this.get(this.getKey(user, product));
    if (tnx) {
      return tnx as T;
    }
    throw new BadRequestException("TNX does not found");
  }

  getKey(user: CurrentUser, product: string): string {
    return `tnx_${user.jti}_${product}`;
  }
}
