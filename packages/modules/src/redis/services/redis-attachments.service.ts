import { Injectable } from "@nestjs/common";
import { RedisCacheService } from "./redis-cache.service";
import { FCCFileUpload } from "@types";
import { UploadAttachmentDto } from "@dtos";

@Injectable()
export class RedisAttachmentsService extends RedisCacheService {
  async save(file: UploadAttachmentDto): Promise<UploadAttachmentDto[]> {
    let attachments = await this.getAttachments(file.tnxId);

    if (attachments && attachments.length > 0) {
      attachments = [...attachments, file];
    } else {
      attachments = [file];
    }

    await this.set(this.getKey(file.tnxId), attachments, {
      ttl: 3600,
    });

    return attachments;
  }

  getAttachments(tnxId: string) {
    return this.get<UploadAttachmentDto[]>(this.getKey(tnxId));
  }

  getKey(tnxId: string): string {
    return `attachmens_${tnxId}`;
  }
}
