// cache.decorator.ts
import { Inject } from "@nestjs/common";
import { RedisCacheService } from "../services/redis-cache.service";

export function Cache(value: string = "something", ttl: number = 300) {
  const injectCache = Inject(RedisCacheService);

  return function (
    target: any,
    propertyKey: string,
    descriptor: PropertyDescriptor,
  ) {
    const originalMethod = descriptor.value;
    injectCache(target, "cacheService"); // Inject RedisCacheService

    descriptor.value = async function (...args: any[]) {
      const cacheService: RedisCacheService = this.cacheService;
      const req = args[0]; // HTTP request object

      try {
        const key = generateCacheKey(propertyKey, req, value);
        const cached = await cacheService.get(key);

        if (cached) {
          return cached;
        }

        const result = await originalMethod.apply(this, args);

        // Only cache successful responses
        if (result !== undefined && result !== null) {
          await cacheService.set(key, result, { ttl });
        }

        return result;
      } catch (error) {
        // Log error but don't fail the request
        console.error("Cache operation failed:", error);
        return originalMethod.apply(this, args);
      }
    };

    return descriptor;
  };
}

function generateCacheKey(methodName: string, req: any, key): string {
  return `${methodName}:${key}:${JSON.stringify(req.params || {})}:${JSON.stringify(req.query || {})}`;
}
