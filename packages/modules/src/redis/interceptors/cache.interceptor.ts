// cache.interceptor.ts
import {
  <PERSON><PERSON><PERSON><PERSON>,
  ExecutionContext,
  Injectable,
  NestInterceptor,
} from "@nestjs/common";
import { Observable, of } from "rxjs";
import { tap } from "rxjs/operators";
import { RedisCacheService } from "../services/redis-cache.service";

@Injectable()
export class CacheInterceptor implements NestInterceptor {
  constructor(private readonly cacheService: RedisCacheService) {}

  async intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Promise<Observable<any>> {
    const request = context.switchToHttp().getRequest();
    const response = context.switchToHttp().getResponse();

    // Get cache metadata from the decorator
    const cacheMetadata = this.getCacheMetadata(context);
    if (!cacheMetadata) {
      return next.handle();
    }

    const { ttl = 300, customKey } = cacheMetadata;
    const key = customKey || this.generateCacheKey(context);

    try {
      // Check cache
      const cached = await this.cacheService.get(key);
      if (cached) {
        // Set Age header for cache transparency
        response.set("X-Cache", "HIT");
        return of(cached);
      }

      // Cache miss - proceed with request
      return next.handle().pipe(
        tap(async (data) => {
          if (data && response.statusCode < 400) {
            response.set("X-Cache", "MISS");
            await this.cacheService.set(key, data, { ttl });
          }
        }),
      );
    } catch (error) {
      console.error("Cache error:", error);
      return next.handle();
    }
  }

  private getCacheMetadata(
    context: ExecutionContext,
  ): { ttl: number; customKey?: string } | undefined {
    const handler = context.getHandler();
    return Reflect.getMetadata("cache:metadata", handler);
  }

  private generateCacheKey(context: ExecutionContext): string {
    const request = context.switchToHttp().getRequest();
    const { params, query, originalUrl } = request;
    return `${originalUrl}:${JSON.stringify(params)}:${JSON.stringify(query)}`;
  }
}
