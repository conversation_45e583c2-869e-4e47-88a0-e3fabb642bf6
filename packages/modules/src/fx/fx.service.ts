import { Injectable, Logger } from "@nestjs/common";
import { HttpService } from "@nestjs/axios";
import { ConfigService } from "@nestjs/config";
import { ApiAdapterClientService } from "../api-adapter-client";
import { CurrencyEnum, FccRequestsEnum } from "@enums";
import { CurrentUser } from "@types";

@Injectable()
export class FxService {
  readonly logger = new Logger(FxService.name);

  constructor(protected readonly clientService: ApiAdapterClientService) {}

  async convert(
    fromCurrency: CurrencyEnum | string,
    toCurrency: CurrencyEnum | string,
    user: CurrentUser
  ): Promise<(amount: number) => number> {
    if (!fromCurrency || !toCurrency || fromCurrency === toCurrency) {
      return (amount) => amount;
    }

    const { convertedAmount: rate = 0 } = await this.clientService.fccRequest<
      Record<string, number>
    >({
      requestId: FccRequestsEnum.FX_CONVERT,
      payload: { fromCurrency, toCurrency },
      user,
    });

    return (amount) => Number((amount * rate).toFixed(4));
  }
}
