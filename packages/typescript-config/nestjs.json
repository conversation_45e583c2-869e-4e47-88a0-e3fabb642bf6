{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "resolveJsonModule": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2021", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "paths": {"@/*": ["./src/*"], "@dtos": ["../../packages/dtos/src"], "@types": ["../../packages/types/src"], "@enums": ["../../packages/enums/src"], "@modules": ["../../packages/modules/src"], "@constants": ["../../packages/constants/src"], "@middlewares": ["../../packages/middlewares/src"], "@helpers": ["../../packages/helpers/src"], "@exceptions": ["../../packages/exceptions/src"], "@filters": ["../../packages/filters/src"]}}}