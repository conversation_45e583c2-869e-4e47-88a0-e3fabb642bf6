import { CurrencyEnum, UserLanguage } from "@enums";

export type DepositListOptions = {
  defaultCurrency?: CurrencyEnum | string;
  userLanguage?: UserLanguage;
  calculateEquivalents?: boolean;
};

export type DepositDetailsOptions = {
  defaultCurrency?: CurrencyEnum | string;
  userLanguage?: UserLanguage;
  calculateEquivalents?: boolean;
};

export type DepositsMap = {
  [tdCode: string]: {
    currencies: string[];
    instructions?: {
      key: number;
      value: string;
    }[];
  };
};

export type DepositId = {
  depositId: string;
  accountId: string;
};
