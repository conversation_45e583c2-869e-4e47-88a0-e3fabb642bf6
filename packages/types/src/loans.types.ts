import { CurrencyEnum, UserLanguage } from "@enums";

export type LoanListOptions = {
  defaultCurrency?: CurrencyEnum | string;
  userLanguage?: UserLanguage;
  calculateEquivalents?: boolean;
};

export type LoanDetailsOptions = {
  defaultCurrency?: CurrencyEnum | string;
  userLanguage?: UserLanguage;
  calculateEquivalents?: boolean;
};

export type LoanId = {
  loanId: string;
  accountId: string;
};
