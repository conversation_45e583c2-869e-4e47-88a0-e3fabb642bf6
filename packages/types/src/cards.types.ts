import { CurrencyEnum, UserLanguage } from "@enums";

export type CardDetailsOptions = {
  defaultCurrency?: CurrencyEnum | string;
  userLanguage?: UserLanguage;
  calculateEquivalents?: boolean;
};

export type CardListOptions = {
  defaultCurrency?: CurrencyEnum | string;
  userLanguage?: UserLanguage;
  calculateEquivalents?: boolean;
};

export type CardInstallmentsOptions = {
  userLanguage?: UserLanguage;
};

export type CreditCardId = {
  sema: string;
  id: string;
  maskedCardNumber?: string;
  accountId?: string;
};
