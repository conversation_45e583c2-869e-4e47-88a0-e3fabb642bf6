import { ProductCodeEnum } from "@enums";
import { TranslatedStatusRecord } from "./common.types";

export interface FormattedTransactionListItem {
  refId: string;
  tnxId: string;
  title: string;
  subTitle?: string;
  productCode: ProductCodeEnum;
  subProductCode?: string;
  status: TranslatedStatusRecord;
  date?: string;
  time?: string;
  // Inputter information
  inputter?: {
    firstName?: string;
    lastName?: string;
    loginId?: string;
  };
}

export interface TransactionDetailsField {
  key: string;
  label: string;
  value: string;
  group?: string[];
}
