export interface AppConfigCondition {
  fact: string;
  operator: string;
  value: string;
}

export interface AppConfigAllConditions {
  all: AppConfigCondition[];
}

export interface AppConfigEvent {
  type: string;
  params: Record<string, unknown>;
}

export interface AppConfigConditionObject {
  conditions: AppConfigAllConditions;
  event: AppConfigEvent;
}

export interface AppConfigConditionsArray {
  conditions: AppConfigConditionObject[];
}

export interface AppConfigFacts<T = unknown> {
  [key: string]: T;
}

export interface AppConfigParameter<T> {
    defaultValue: {
        value: T;
    };
    conditionalValues?: Record<string, { value: T }>;
    valueType: string;
}

export interface AppConfigParameters {
    [key: string]: AppConfigParameter<any>;
}