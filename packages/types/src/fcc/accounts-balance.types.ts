export type CurrencyAmount = {
    currencyCode: string;
    amount: string;
};

export type IndividualAccountBalance = {
    accountNumber: string;
    accountName: string | null;
    currencyCode: string;
    amount: string;
    accountType: string;
    principalAmount: string;
    availableAmount: string | null;
    ledgerBalance: number;
    availableBalance: number;
    ledgerBalanceConverted: number;
    availableBalanceConverted: number;
};

export type AccountBalanceSummary = {
    totalAccountBalanceOnBaseCurrency: CurrencyAmount;
    totalAccountBalanceBasedOnCurrency: CurrencyAmount[];
    individualAccountBalances: IndividualAccountBalance[];
};
