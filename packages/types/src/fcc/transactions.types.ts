import { TransactionListItemDto } from "@dtos";
import { TransactionApprovalStatusEnum } from "@enums";

export interface APIMessageResponse {
  message: string;
  messageKey: string;
  userLangaugeMessage: string;
  userLanguage: string;
}

type AdditionalField = {
  name: string | null;
  value: string | null;
};

export type TransactionDetails = {
  id: string;
  referenceId: string;
  currency: string;
  amount: string;
  status: string;
  productCode: string;
  subProductCode: string;
  transactionType: string;
  valueDate: string;
  applicationDate: string;
  account: string;
  additionalFields: AdditionalField[];
};

interface ValidationStep {
  date: string;
  fullName: string;
  description: string;
  userId: number;
  validationNo: string;
  actionLabel: string;
}

interface JourneyItem {
  tnx_stat_code: string;
  tnx_id: string;
  tnx_type_code: string;
  validationSteps: ValidationStep[];
  prod_stat_code: string;
  bo_release_dttm: string;
}

export interface TransactionJourney {
  items: JourneyItem[];
}

export interface TransactionHistoryItem {
  date: string;
  fullName: string;
  role: string;
}

export interface TransactionHistory {
  items: TransactionHistoryItem[];
}

export interface TransactionApproveRejectResponse {
  transactionId: string;
  approvalStatus: TransactionApprovalStatusEnum;
  messageResponse: APIMessageResponse;
}

export interface TransactionsListResponse {
  count: number;
  rowDetails: TransactionListItemDto[];
}
