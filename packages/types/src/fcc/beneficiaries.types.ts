export type FCCBeneficiariesListItem = {
  accountId: string;
  name: string;
  accountNumber: string;
  currency: string;
  bicCode: string;
  bankName: string;
  bankAddress: string;
  country: string;
};

export type FCCBeneficiariesList = {
  items: FCCBeneficiariesListItem[];
  _meta: any;
};

export type FCCIpnBeneficiaryListItem = {
  accountId: string;
  name: string;
  accountNumber: string;
  currency: string;
  clearingSystem: string;
  limitAmount: number;
  nickname?: string;
  description?: string;
  beneficiaryBank?: string;
  bankId?: string;
  cardHolderName?: string;
  entityShortName?: string;
  country?: string;
};

export type FCCIpnBeneficiariesList = {
  data: FCCIpnBeneficiaryListItem[];
  status?: string;
  code?: string;
  message?: string;
  timestamp?: string;
};
