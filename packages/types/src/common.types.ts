import { CacheScope, TransactionStatusEnum } from "@enums";

export type CacheOptions = {
  enabled?: boolean;
  scope?: CacheScope;
  ttl?: number;
};

export interface ProxyConfig {
  host: string;
  port: number;
  auth?: ProxyBasicCredentials;
  protocol?: string;
}
export interface ProxyBasicCredentials {
  username: string;
  password: string;
}

export type TranslatedStatusRecord = {
  translated: string;
  status?: TransactionStatusEnum;
};

export type DateTimeRecord = {
  date: string;
  time: string;
  timestamp?: string;
};

export type LocalizedCurrency = {
  code: string;
  localized: string;
};

export type Pagination = {
  itemsPerPage: number;
  totalItems: number;
  page: number;
  totalPages: number;
};

export type PaginationResponse<T> = {
  pagination: Pagination;
  data: T[];
};
