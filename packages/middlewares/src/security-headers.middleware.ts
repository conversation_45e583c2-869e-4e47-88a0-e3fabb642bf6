import { Injectable, NestMiddleware } from "@nestjs/common";
import { Request, Response, NextFunction } from "express";

export class SecurityHeadersMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    // Content Security Policy
    res.setHeader("Content-Security-Policy", "default-src 'self';");
    // X-XSS-Protection
    res.setHeader("X-XSS-Protection", "1; mode=block");
    // X-Content-Type-Options
    res.setHeader("X-Content-Type-Options", "nosniff");
    // Strict-Transport-Security
    res.setHeader(
      "Strict-Transport-Security",
      "max-age=31536000; includeSubDomains"
    );
    // X-Frame-Options
    res.setHeader("X-Frame-Options", "SAMEORIGIN");
    next();
  }
}
