{"name": "core-banking-services", "private": true, "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "check-types": "turbo run check-types"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^11.0.20", "@types/express": "^5.0.0", "@types/jest": "^29.5.2", "@types/lodash": "^4.17.16", "@types/node": "^20.3.1", "@types/supertest": "^6.0.3", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "eslint": "^8.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.5.3", "source-map-support": "^0.5.21", "supertest": "^7.1.4", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "turbo": "^2.5.0", "typescript": "5.8.2"}, "engines": {"node": ">=20"}, "packageManager": "yarn@1.22.22", "workspaces": ["apps/*", "packages/*"], "dependencies": {"@fastify/multipart": "^9.0.3", "@nestjs/axios": "^4.0.1", "@nestjs/common": "^11.0.20", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.20", "@nestjs/jwt": "^11.0.0", "@nestjs/platform-fastify": "^11.0.0", "@nestjs/schedule": "^6.0.0", "axios": "^1.11.0", "cheerio": "^1.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "html-entities": "^2.6.0", "ioredis": "^5.6.1", "json-stable-stringify": "^1.3.0", "lodash": "^4.17.21", "luxon": "^3.7.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "svg-captcha": "^1.4.0"}, "resolutions": {"form-data": "^4.0.4"}}