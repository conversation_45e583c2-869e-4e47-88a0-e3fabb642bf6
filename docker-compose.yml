version: '3.8'

x-common_env: &common_env
  NODE_ENV: development
  AES_SECRET:
  JWT_SECRET: super_secret_321
  JWT_EXPIRES_IN: 1h
  API_ADAPTER_SERVICE_URL: http://localhost:3001
  LOOKUPS_API_SERVICE_URL: https://dashboard-gateway.apps.ocp-nonprod-01.hodomain.local
  REDIS_HOST: redis
  REDIS_PORT: 6379
  REDIS_PASSWORD: ""
  THROTTLING_LIMIT: 2
  THROTTLING_TTL: 10

services:
  redis:
    container_name: redis
    image: redis:latest
    ports:
      - '6379:6379'
    environment:
      - REDIS_PASSWORD=""

  adapter:
    container_name: api-adapter-service
    build:
      context: .
      dockerfile: apps/api-adapter-service/Dockerfile
    ports:
      - '3001:3000'
    environment:
      NODE_ENV: development
      FCC_URL: http://localhost:8080/com.misys.portal.client.cib
      ESB_URL: http://consumerservice-toolkit-http-cp4iuat.apps.nprodcloudpak.hodomain.local:80
      LOOKUPS_API_SERVICE_URL: https://dashboard-gateway.apps.ocp-nonprod-01.hodomain.local
    volumes:
      - .:/usr/src/app
      - /usr/src/app/node_modules
    depends_on:
      - redis

  accounts:
    container_name: accounts-service
    build:
      context: .
      dockerfile: apps/accounts-service/Dockerfile
    ports:
      - '3002:3000'
    environment:
      <<: *common_env
    depends_on:
      - redis

  auth:
    container_name: auth-service
    build:
      context: .
      dockerfile: apps/auth-service/Dockerfile
    ports:
      - '3003:3003'
    environment:
      <<: *common_env
      ISAM_BASE_URL: https://isam-digital-test.hodomain.local:448
      IGNORE_SSL_VALIDATION: true
      FCC_URL: http://localhost:8080/com.misys.portal.client.cib
      NODE_TLS_REJECT_UNAUTHORIZED: 0
    depends_on:
      - redis

  shared:
    container_name: shared-service
    build:
      context: .
      dockerfile: apps/shared-service/Dockerfile
    ports:
      - '3004:3000'
    environment:
      <<: *common_env
    depends_on:
      - redis

  transfers:
    container_name: transfers-service
    build:
      context: .
      dockerfile: apps/transfers-service/Dockerfile
    ports:
      - '3005:3000'
    environment:
      <<: *common_env
    depends_on:
      - redis

  users:
    container_name: users-service
    build:
      context: .
      dockerfile: apps/users-service/Dockerfile
    ports:
      - '3006:3000'
    environment:
      <<: *common_env
    depends_on:
      - redis

  cards:
    container_name: cards-service
    build:
      context: .
      dockerfile: apps/cards-service/Dockerfile
    ports:
      - '3007:3000'
    environment:
      <<: *common_env
    depends_on:
      - redis

volumes:
  pgdata: