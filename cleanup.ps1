# cleanup.ps1

param (
    [string]$ROOT_PATH
)

# Check if the path was provided
if (-not $ROOT_PATH) {
    Write-Host "Usage: cleanup.ps1 <path>"
    exit 1
}

Write-Host "Starting cleanup process in: $ROOT_PATH"

# Delete all docs folders
Get-ChildItem -Path $ROOT_PATH -Recurse -Directory -Filter "docs" | Remove-Item -Recurse -Force

# Delete all .spec.ts and .spec.tsx files
Get-ChildItem -Path $ROOT_PATH -Recurse -File -Filter "*.spec.ts" | Remove-Item -Force
Get-ChildItem -Path $ROOT_PATH -Recurse -File -Filter "*.spec.tsx" | Remove-Item -Force

# Delete all .test.ts and .test.tsx files
Get-ChildItem -Path $ROOT_PATH -Recurse -File -Filter "*.test.ts" | Remove-Item -Force
Get-ChildItem -Path $ROOT_PATH -Recurse -File -Filter "*.test.tsx" | Remove-Item -Force

# Delete all .web.ts and .web.tsx files
Get-ChildItem -Path $ROOT_PATH -Recurse -File -Filter "*.web.ts" | Remove-Item -Force
Get-ChildItem -Path $ROOT_PATH -Recurse -File -Filter "*.web.tsx" | Remove-Item -Force

# Delete all .env and .env.example files
Get-ChildItem -Path $ROOT_PATH -Recurse -File -Filter ".env" | Remove-Item -Force
Get-ChildItem -Path $ROOT_PATH -Recurse -File -Filter ".env.example" | Remove-Item -Force

# Delete all config/index.ts files
Get-ChildItem -Path $ROOT_PATH -Recurse -File -Filter "index.ts" | Where-Object { $_.DirectoryName -match "config" } | Remove-Item -Force

# Delete all test/ and __tests__/ directories
Get-ChildItem -Path $ROOT_PATH -Recurse -Directory -Filter "test" | Remove-Item -Recurse -Force
Get-ChildItem -Path $ROOT_PATH -Recurse -Directory -Filter "__tests__" | Remove-Item -Recurse -Force

# Delete the config/ directory itself
Remove-Item -Path "$ROOT_PATH\config" -Recurse -Force

# Delete all .md and .mdx files
Get-ChildItem -Path $ROOT_PATH -Recurse -File -Filter "*.md" | Remove-Item -Force
Get-ChildItem -Path $ROOT_PATH -Recurse -File -Filter "*.mdx" | Remove-Item -Force

# Delete all yaml files
Get-ChildItem -Path $ROOT_PATH -Recurse -File -Filter "*.yaml" | Remove-Item -Force
Get-ChildItem -Path $ROOT_PATH -Recurse -File -Filter "*.yml" | Remove-Item -Force

# Delete all tpl files
Get-ChildItem -Path $ROOT_PATH -Recurse -File -Filter "*.tpl" | Remove-Item -Force

# Delete all .feature files
Get-ChildItem -Path $ROOT_PATH -Recurse -File -Filter "*.feature" | Remove-Item -Force

# Delete all json files
Get-ChildItem -Path $ROOT_PATH -Recurse -File -Filter "*.json" | Remove-Item -Force

# Delete all sonar folders
Get-ChildItem -Path $ROOT_PATH -Recurse -Directory -Filter "sonar" | Remove-Item -Recurse -Force

# Delete all charts folders
Get-ChildItem -Path $ROOT_PATH -Recurse -Directory -Filter "chart" | Remove-Item -Recurse -Force

# Delete all mocks folders
Get-ChildItem -Path $ROOT_PATH -Recurse -Directory -Filter "mocks" | Remove-Item -Recurse -Force

# Delete all steps.ts files
Get-ChildItem -Path $ROOT_PATH -Recurse -File -Filter "*.steps.ts" | Remove-Item -Force

# Delete all templates.ts files
Get-ChildItem -Path $ROOT_PATH -Recurse -File -Filter "*.templates.ts" | Remove-Item -Force

Write-Host "Cleanup completed."
