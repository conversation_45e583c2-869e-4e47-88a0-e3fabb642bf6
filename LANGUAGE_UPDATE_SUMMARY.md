# Language Parameter Support for createRestPortalPayload Method

## Overview
Updated the `createRestPortalPayload` method to accept a language parameter and dynamically set the `userSelectedLanguage` field in the REST portal payload based on the user's language preference.

## Changes Made

### 1. IsamService (`apps/auth-service/src/auth/services/isam.service.ts`)

#### Updated `createRestPortalPayload` method:
- **Before**: Hard-coded `userSelectedLanguage: 'ar'`
- **After**: Dynamic language mapping based on input parameter
- **New signature**: `createRestPortalPayload(credentials?, language?: string | UserLanguage)`
- **Language mapping logic**:
  - `'ar-EG'` → `'ar'`
  - `'en-US'` → `'en'`
  - Any string starting with `'ar'` → `'ar'`
  - Default fallback → `'en'`

#### Updated `performRestPortalAuthentication` method:
- **New signature**: `performRestPortalAuthentication(isamResponse, language?: string, customPayload?, customUrl?)`
- **Parameter reordering**: Language moved to second position for cleaner method calls
- Passes language parameter to `createRestPortalPayload` when creating default payload
- **Language handling**: Avoids passing `undefined` values by allowing optional parameters

#### Added imports:
- `UserLanguageMap` from `@constants`
- `UserLanguage` from `@enums`

### 2. AuthService (`apps/auth-service/src/auth/services/auth.service.ts`)

#### Updated `devLogin` method:
- **New signature**: `devLogin(loginDto: LoginDto, language: UserLanguage = UserLanguage['en-US'])`
- Passes language parameter to `createRestPortalPayload`

#### Updated `completeAuthentication` method:
- Passes language parameter to `performRestPortalAuthentication`
- Avoids passing `undefined` values

### 3. TermsAcceptanceService (`apps/auth-service/src/auth/services/terms-acceptance.service.ts`)

#### Updated `acceptTerms` method:
- Passes language parameter to `createRestPortalPayload`

### 4. TerminationService (`apps/auth-service/src/auth/services/termination.service.ts`)

#### Updated `processTerminationResponse` method:
- Passes language parameter to `performRestPortalAuthentication`
- Uses default credentials (username: 'username', company: 'company')

### 5. AuthController (`apps/auth-service/src/auth/auth.controller.ts`)

#### Updated `devLogin` endpoint:
- **New signature**: Accepts `@Headers('accept-language') language?: string`
- Uses `parseLanguageHeader(language)` to convert to `UserLanguage` enum
- Passes parsed language to service method

### 6. Test Updates (`apps/auth-service/test/auth/services/isam.service.spec.ts`)

#### Enhanced `createRestPortalPayload` tests:
- Added test for default English language selection
- Added test for Arabic language selection (`ar-EG` → `ar`)
- Added test for English language selection (`en-US` → `en`)
- Added test for unknown language fallback to English
- Added test for combined credentials and language parameters

## Usage Examples

### API Requests

```bash
# English request
POST /dev/login
Headers: Accept-Language: en-US
Body: { "username": "user", "company": "comp", "password": "pass" }

# Arabic request
POST /dev/login  
Headers: Accept-Language: ar-EG
Body: { "username": "user", "company": "comp", "password": "pass" }
```

### Payload Generated

```javascript
// English payload
{
  "userData": {
    "username": "user",
    "company": "comp", 
    "userSelectedLanguage": "en"
  },
  "requestData": { /* ... */ },
  "vascoData": { /* ... */ }
}

// Arabic payload  
{
  "userData": {
    "username": "user",
    "company": "comp",
    "userSelectedLanguage": "ar" 
  },
  "requestData": { /* ... */ },
  "vascoData": { /* ... */ }
}
```

## Language Mapping Logic

The system uses the following mapping:
1. `UserLanguage['ar-EG']` → `'ar'` (Arabic)
2. `UserLanguage['en-US']` → `'en'` (English)  
3. Any string starting with `'ar'` → `'ar'` (Arabic fallback)
4. Any other string → `'en'` (English fallback)
5. No language provided → `'en'` (Default)

## Impact

- ✅ The REST portal will now receive the correct `userSelectedLanguage` based on client preference
- ✅ Responses from REST portal will be localized according to the selected language  
- ✅ Backward compatibility maintained (defaults to English if no language provided)
- ✅ All authentication flows (login, dev login, terms acceptance) now support language selection
- ✅ Comprehensive test coverage for all language scenarios
- ✅ Cleaner method calls: Language parameter reordering eliminates need for explicit `undefined` values
- ✅ Improved developer experience with more intuitive parameter ordering
