const { spawn, execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Create temporary .env files with unique ports for each service
const setupEnvFiles = () => {
  console.log('Setting up environment files with unique ports...');

  const services = [
    { name: 'api-adapter-service', port: 3001 },
    { name: 'accounts-service', port: 3002 },
    // { name: 'auth-service', port: 3003 },
    { name: 'shared-service', port: 3004 },
    { name: 'transfers-service', port: 3005 },
    { name: 'users-service', port: 3006 },
  ];

  services.forEach(service => {
    const envPath = path.join('apps', service.name, '.env');
    const envExamplePath = path.join('apps', service.name, '.env.example');

    // Check if .env.example exists
    if (fs.existsSync(envExamplePath)) {
      let envContent = fs.readFileSync(envExamplePath, 'utf8');

      // Replace PORT value
      envContent = envContent.replace(/PORT=\d+/, `PORT=${service.port}`);

      // Add mock URLs for external services if they don't exist
      if (service.name === 'api-adapter-service') {
        // Mock FCC URL if needed
        if (!envContent.includes('FCC_URL=')) {
          envContent += '\nFCC_URL=http://isam-digital-test.hodomain.local:448\n';
        }
      }

      // Write to .env file
      fs.writeFileSync(envPath, envContent);
      console.log(`Created .env file for ${service.name} with PORT=${service.port}`);
    } else {
      console.log(`Warning: .env.example not found for ${service.name}`);
    }
  });
};

// Kill any processes using the debug ports
const killProcessOnPort = (port) => {
  console.log(`Checking if port ${port} is in use...`);
  const platform = process.platform;
  let command;

  if (platform === 'win32') {
    command = `netstat -ano | findstr :${port} | findstr LISTENING`;
  } else {
    command = `lsof -i :${port} | grep LISTEN`;
  }

  try {
    const output = execSync(command, { encoding: 'utf8' });

    if (output) {
      console.log(`Port ${port} is in use. Attempting to kill process...`);
      if (platform === 'win32') {
        const pid = output.split(/\s+/)[4];
        execSync(`taskkill /F /PID ${pid}`);
      } else {
        const pid = output.split(/\s+/)[1];
        execSync(`kill -9 ${pid}`);
      }
      console.log(`Process using port ${port} has been terminated.`);
    }
  } catch (error) {
    // If no process is found or command fails, continue
  }
};

// Kill any processes using the service ports
const killServicePorts = () => {
  for (let port = 3000; port <= 3006; port++) {
    killProcessOnPort(port);
  }
};

// Configuration for each service
const services = [
  { name: 'api-adapter', port: 9240, command: 'yarn', args: ['--cwd', 'apps/api-adapter-service', 'start:debug'] },
  { name: 'accounts', port: 9241, command: 'yarn', args: ['--cwd', 'apps/accounts-service', 'start:debug'] },
  // { name: 'auth', port: 9242, command: 'yarn', args: ['--cwd', 'apps/auth-service', 'start:debug'] },
  { name: 'shared', port: 9243, command: 'yarn', args: ['--cwd', 'apps/shared-service', 'start:debug'] },
  { name: 'transfers', port: 9244, command: 'yarn', args: ['--cwd', 'apps/transfers-service', 'start:debug'] },
  { name: 'users', port: 9245, command: 'yarn', args: ['--cwd', 'apps/users-service', 'start:debug'] },
];

// Main execution
console.log('Starting debug environment setup...');

// Setup environment files
setupEnvFiles();

// Kill any processes using the service ports
killServicePorts();

// Kill any processes using the debug ports
services.forEach(service => {
  killProcessOnPort(service.port);
});

// Start each service with a unique debug port
services.forEach(service => {
  console.log(`Starting ${service.name} service in debug mode on port ${service.port}...`);

  // Set NODE_OPTIONS to specify the debug port
  const env = Object.assign({}, process.env, {
    NODE_OPTIONS: `--inspect=0.0.0.0:${service.port}`,
    // Add error handling options
    NODE_NO_WARNINGS: '1',
    // Mock external services for development
    MOCK_EXTERNAL_SERVICES: 'true',
  });

  const child = spawn(service.command, service.args, {
    env,
    stdio: 'inherit',
    shell: true,
  });

  child.on('error', (error) => {
    console.error(`Error starting ${service.name} service:`, error);
  });
});