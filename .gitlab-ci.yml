workflow:
  rules:
    - if: '$CI_PIPELINE_SOURCE == "web"'
    - if: '$CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH =~ /^(develop|staging|main|preprod|l3-sit|l3-uat|sit|test-mongo-ci)$/'
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
    - if: '$CI_PIPELINE_SOURCE == "trigger"' # this line if webhook is the trigger source.
    - if: '$CI_PIPELINE_SOURCE == "schedule"' # Allow the pipeline to trigger from a schedule...

include:
  - component: gitlab.apps.ocp-nonprod-01.hodomain.local/dso/cicd-components/nodejs/nodejs@generic-build-tools
    inputs:
      image: registry.apps.ocp-nonprod-01.hodomain.local/dso/docker-images/ci-all-in-one:node-v24.0.0_java-v17.0.6-20250507072412
      build_tools: yarn
      linting: disabled
      scan_stage: scan
  

  - component: gitlab.apps.ocp-nonprod-01.hodomain.local/dso/cicd-components/fortify/sast@main
    inputs:
      stage: scan
      version_id: "10075"

  - component: gitlab.apps.ocp-nonprod-01.hodomain.local/dso/cicd-components/sonatype-iq-cli/sonatype-iq-cli@main
    inputs:
      name: "dependancies"
      target: "package-lock.json"
    rules:
      - if: '$CI_COMMIT_BRANCH == "develop"'
      - if: '$CI_COMMIT_BRANCH == "staging"'
  - component: gitlab.apps.ocp-nonprod-01.hodomain.local/dso/cicd-components/kaniko/kaniko@devscan
    inputs:
      context: ./
      dockerfile: apps/api-adapter-service/Dockerfile
      name: api-adapter-service
      registry: quay
    rules:
      - if: '$CI_COMMIT_BRANCH =~ /^(develop|staging)$/'
        changes:
          - "apps/api-adapter-service/**/*"
          - "packages/**/*"

  - component: gitlab.apps.ocp-nonprod-01.hodomain.local/dso/cicd-components/kaniko/kaniko@devscan
    inputs:
      context: ./
      dockerfile: apps/accounts-service/Dockerfile
      name: accounts-service
      registry: quay
    rules:
      - if: '$CI_COMMIT_BRANCH =~ /^(develop|staging)$/'
        changes:
          - "apps/accounts-service/**/*"
          - "packages/**/*"

  - component: gitlab.apps.ocp-nonprod-01.hodomain.local/dso/cicd-components/kaniko/kaniko@devscan
    inputs:
      context: ./
      dockerfile: apps/auth-service/Dockerfile
      name: auth-service
      registry: quay
    rules:
      - if: '$CI_COMMIT_BRANCH =~ /^(develop|staging)$/'
        changes:
          - "apps/auth-service/**/*"
          - "packages/**/*"

  - component: gitlab.apps.ocp-nonprod-01.hodomain.local/dso/cicd-components/kaniko/kaniko@devscan
    inputs:
      context: ./
      dockerfile: apps/shared-service/Dockerfile
      name: shared-service
      registry: quay
    rules:
      - if: '$CI_COMMIT_BRANCH =~ /^(develop|staging)$/'
        changes:
          - "apps/shared-service/**/*"
          - "packages/**/*"

  - component: gitlab.apps.ocp-nonprod-01.hodomain.local/dso/cicd-components/kaniko/kaniko@devscan
    inputs:
      context: ./
      dockerfile: apps/transfers-service/Dockerfile
      name: transfers-service
      registry: quay
    rules:
      - if: '$CI_COMMIT_BRANCH =~ /^(develop|staging)$/'
        changes:
          - "apps/transfers-service/**/*"
          - "packages/**/*"

  - component: gitlab.apps.ocp-nonprod-01.hodomain.local/dso/cicd-components/kaniko/kaniko@devscan
    inputs:
      context: ./
      dockerfile: apps/users-service/Dockerfile
      name: users-service
      registry: quay
    rules:
      - if: '$CI_COMMIT_BRANCH =~ /^(develop|staging)$/'
        changes:
          - "apps/users-service/**/*"
          - "packages/**/*"

  - component: gitlab.apps.ocp-nonprod-01.hodomain.local/dso/cicd-components/kaniko/kaniko@devscan
    inputs:
      context: ./
      dockerfile: apps/secure-mail-service/Dockerfile
      name: secure-mail-service
      registry: quay
    rules:
      - if: '$CI_COMMIT_BRANCH =~ /^(develop|staging)$/'
        changes:
          - "apps/secure-mail-service/**/*"
          - "packages/**/*"

  - component: gitlab.apps.ocp-nonprod-01.hodomain.local/dso/cicd-components/kaniko/kaniko@devscan
    inputs:
      context: ./
      dockerfile: apps/deposits-service/Dockerfile
      name: deposits-service
      registry: quay
    rules:
      - if: '$CI_COMMIT_BRANCH =~ /^(develop|staging)$/'
        changes:
          - "apps/deposits-service/**/*"
          - "packages/**/*"

  - component: gitlab.apps.ocp-nonprod-01.hodomain.local/dso/cicd-components/kaniko/kaniko@devscan
    inputs:
      context: ./
      dockerfile: apps/cards-service/Dockerfile
      name: cards-service
      registry: quay
    rules:
      - if: '$CI_COMMIT_BRANCH =~ /^(develop|staging)$/'
        changes:
          - "apps/cards-service/**/*"
          - "packages/**/*"

  - component: gitlab.apps.ocp-nonprod-01.hodomain.local/dso/cicd-components/helm/helm@main
    inputs:
      chart_name: core-banking-services-bb
    rules:
      - if: '$CI_COMMIT_BRANCH == "develop"'
      - if: '$CI_COMMIT_BRANCH == "staging"'

stages: [build, scan, package, validate, publish]
