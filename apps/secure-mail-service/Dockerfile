FROM node:20.19.0-alpine3.20 AS prod

# Set working directory
WORKDIR /app

# Set production environment
ENV NODE_ENV production

# Install minimal Alpine packages
RUN apk add --no-cache ca-certificates

# Copy package.json for vulnerability scanning (<PERSON>vy, RHACS)
COPY --chown=node:node package.json package.json

# Copy yarn.lock for vulnerability scanning (Trivy, RHACS)
COPY --chown=node:node yarn.lock yarn.lock

# Copy production node_modules from CI pipeline
COPY --chown=node:node node_modules node_modules

# Copy built application from CI pipeline
COPY --chown=node:node apps/secure-mail-service/dist apps/secure-mail-service/dist

# switch the user
USER node

# Set working directory
WORKDIR /app/apps/secure-mail-service/dist/apps/secure-mail-service/src

CMD ["node", "--no-deprecation", "--no-warnings", "main.js"]



