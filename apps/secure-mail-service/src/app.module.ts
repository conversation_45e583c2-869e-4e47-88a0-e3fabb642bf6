// app.module.ts
import { Module } from '@nestjs/common';
import {
  ApiAdapterClient,
  CommonModule,
  GlobalConfigModule,
  JwtAuthModule,
  LookupEntity,
  LookupsModule,
  RedisModule,
} from '@modules';
import defaults from './config/defaults';
import { SecureMailModule } from '@/modules/secure-mail/secure-mail.module';
import { SecureMailDynamicListEnum } from '@enums';
import { ProductsModule } from './modules/products/own/products.module';

@Module({
  imports: [
    GlobalConfigModule.forRoot(defaults),
    RedisModule.forRootAsync(),
    LookupsModule.forRoot([
      {
        name: LookupEntity.I18N_SERVER,
      },
      {
        name: LookupEntity.CONTENT_LIST,
        filter: [
          SecureMailDynamicListEnum.PORTAL_TYPES,
          SecureMailDynamicListEnum.USER_PRIVILEGES,
          SecureMailDynamicListEnum.SECURE_MAIL_TYPES,
          SecureMailDynamicListEnum.ACCESS_PERMISSIONS,
          SecureMailDynamicListEnum.TOKEN_TYPES,
          SecureMailDynamicListEnum.PRODUCT_TYPES,
        ],
      },
      {
        name: LookupEntity.BANK_BRANCH,
      },
      {
        name: LookupEntity.BANK_BRANCH_GOVERNATE,
      },
      {
        name: LookupEntity.ACCOUNT_PRODUCT,
      },
      {
        name: LookupEntity.DEPOSIT_PRODUCT,
      },
    ]),
    ApiAdapterClient,
    JwtAuthModule.forRoot(),
    SecureMailModule,
    ProductsModule,
    CommonModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
