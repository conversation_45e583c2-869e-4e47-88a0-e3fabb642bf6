import {
  <PERSON>rrayNotEmpty,
  IsArray,
  IsNotEmpty,
  IsOptional,
  IsString,
} from 'class-validator';
import { SecureMailBaseDto } from './base.dto';

export class AddNewPermissionDto extends SecureMailBaseDto {
  @IsArray()
  @ArrayNotEmpty()
  @IsString({ each: true })
  accessPermission: string[];

  @IsString()
  @IsNotEmpty()
  user: string;

  @IsOptional()
  @IsString()
  tokenType: string;
}
