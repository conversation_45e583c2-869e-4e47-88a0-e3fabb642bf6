import {
  <PERSON>rrayNotEmpty,
  IsArray,
  IsNotEmpty,
  IsOptional,
  IsString,
} from 'class-validator';
import { SecureMailBaseDto } from './base.dto';

export class AmendUserDataDto extends SecureMailBaseDto {
  @IsString()
  @IsNotEmpty()
  user: string;

  @IsString()
  @IsOptional()
  newMobileNumber: string;

  @IsString()
  @IsOptional()
  newEmail: string;

  @IsArray()
  @ArrayNotEmpty()
  @IsString({ each: true })
  portalType: string[];
}
