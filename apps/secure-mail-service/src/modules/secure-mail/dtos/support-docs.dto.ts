import {
  Is<PERSON><PERSON>y,
  IsBoolean,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateIf,
} from 'class-validator';
import { SecureMailBaseDto } from './base.dto';

export class SupportDocsDto extends SecureMailBaseDto {
  @IsNotEmpty()
  @IsBoolean()
  prevRequest: boolean;

  @IsOptional()
  @IsString()
  requestNumber: string;

  @ValidateIf(() => true)
  @IsNotEmpty()
  @IsArray()
  attachments: string[];
}
