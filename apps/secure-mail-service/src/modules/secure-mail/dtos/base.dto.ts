import { SecureMailTypesEnum, TransactionStatusEnum } from '@enums';
import { Expose, Transform } from 'class-transformer';
import { IsArray, IsEnum, IsOptional, IsString } from 'class-validator';

export class SecureMailBaseDto {
  @IsString()
  @IsEnum(SecureMailTypesEnum)
  type: SecureMailTypesEnum;

  @IsOptional()
  @IsString()
  message: string;

  @IsString()
  @IsOptional()
  reauthPassword: string;

  @IsOptional()
  @IsArray()
  attachments: string[];
}

export class SecureMailInitiateDto {
  @IsString()
  @IsOptional()
  @IsEnum(SecureMailTypesEnum)
  type?: SecureMailTypesEnum;

  @IsOptional()
  payload: Record<string, any>;
}

export class SecureMailListQueryDto {
  @Expose()
  @Transform(
    ({ value }) =>
      value ?? [
        TransactionStatusEnum.Approved,
        TransactionStatusEnum.InProgress,
        TransactionStatusEnum.RejectedBank,
      ],
  )
  @IsOptional()
  @IsEnum(TransactionStatusEnum, { each: true })
  status?: TransactionStatusEnum[];
}
