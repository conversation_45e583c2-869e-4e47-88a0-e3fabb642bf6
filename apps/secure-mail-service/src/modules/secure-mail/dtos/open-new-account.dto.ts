import { IsNotEmpty, IsString } from 'class-validator';
import { SecureMailBaseDto } from './base.dto';
import { SecureMailFieldNamesEnum } from '@enums';

export class OpenNewAccountDto extends SecureMailBaseDto {
  @IsNotEmpty()
  @IsString()
  [SecureMailFieldNamesEnum.Currency]: string;

  @IsNotEmpty()
  @IsString()
  [SecureMailFieldNamesEnum.AccountProductCode]: string;

  @IsNotEmpty()
  @IsString()
  [SecureMailFieldNamesEnum.SignatureAccountNumber]: string;

  @IsNotEmpty()
  @IsString()
  employeesNumber: string;

  @IsNotEmpty()
  @IsString()
  businessSize: string;
}
