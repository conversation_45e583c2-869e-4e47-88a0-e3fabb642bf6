import { IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { SecureMailBaseDto } from './base.dto';
import { SecureMailFieldNamesEnum } from '@enums';

export class CancelBankDraftDto extends SecureMailBaseDto {
  @IsString()
  @IsNotEmpty()
  fundTransferNumber: string;

  @IsString()
  @IsOptional()
  [SecureMailFieldNamesEnum.DeliveryBranchCity]: string;

  @IsString()
  @IsNotEmpty()
  [SecureMailFieldNamesEnum.DeliveryBranch]: string;
}
