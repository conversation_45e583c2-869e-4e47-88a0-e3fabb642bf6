import { IsEnum, <PERSON>NotEmpty, <PERSON>Optional, IsString } from 'class-validator';

import { SecureMailBaseDto } from './base.dto';
import { DepositRedemptionTypeEnum } from '@enums';

export class RedeemTermDepositDto extends SecureMailBaseDto {
  @IsString()
  @IsNotEmpty()
  id: string;

  @IsEnum(DepositRedemptionTypeEnum)
  @IsNotEmpty()
  redemptionType: DepositRedemptionTypeEnum;

  @IsOptional()
  redemptionAmount: number;

  @IsOptional()
  redemptionAccountNumber: number;
}
