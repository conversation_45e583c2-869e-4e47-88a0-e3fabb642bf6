import { <PERSON><PERSON>yNotEmpty, IsArray, IsNotEmpty, IsString } from 'class-validator';
import { SecureMailBaseDto } from './base.dto';

export class AssignNewProductDto extends SecureMailBaseDto {
  @IsString()
  productType: string;

  @IsString()
  @IsNotEmpty()
  productNumber: string;

  @IsString()
  @IsNotEmpty()
  user: string;

  @IsArray()
  @ArrayNotEmpty()
  @IsString({ each: true })
  portalType: string[];
}
