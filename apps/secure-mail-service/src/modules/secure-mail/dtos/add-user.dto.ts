import {
  <PERSON><PERSON>yNotEmpty,
  IsArray,
  IsBoolean,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateIf,
} from 'class-validator';
import { SecureMailBaseDto } from './base.dto';

export class AddNewUserDto extends SecureMailBaseDto {
  @IsNotEmpty()
  @IsString()
  fullNameAr: string;

  @IsNotEmpty()
  @IsString()
  fullNameEn: string;

  @IsNotEmpty()
  @IsString()
  mobileNumber: string;

  @IsArray()
  @ArrayNotEmpty()
  @IsString({ each: true })
  privilege: string[];

  @IsArray()
  @ArrayNotEmpty()
  @IsString({ each: true })
  portalType: string[];

  @IsOptional()
  @IsString()
  tokenType: string;

  @IsNotEmpty()
  @IsBoolean()
  hasDelegation: boolean;

  @ValidateIf(() => true)
  @IsNotEmpty()
  @IsArray()
  attachments: string[];
}
