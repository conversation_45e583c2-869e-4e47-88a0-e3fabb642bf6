import { IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { SecureMailBaseDto } from './base.dto';
import { SecureMailFieldNamesEnum } from '@enums';

export class RequestBankDraftDto extends SecureMailBaseDto {
  @IsNotEmpty()
  @IsString()
  amount: string;

  @IsOptional()
  @IsString()
  parentBranch: string;

  @IsString()
  @IsOptional()
  [SecureMailFieldNamesEnum.DeliveryBranchCity]: string;

  @IsString()
  @IsNotEmpty()
  [SecureMailFieldNamesEnum.DeliveryBranch]: string;
}
