import { Injectable } from '@nestjs/common';
import { SecureMailBaseService } from './base.service';
import { ClassConstructor } from 'class-transformer';
import { CancelBankDraftDto } from '../dtos/cancel-bank-draft.dto';
import {
  CurrentUser,
  SecureMailFieldConfigMap,
  SecureMailFieldRecord,
} from '@types';
import {
  SecureMailFieldNamesEnum,
  SecureMailFieldTypeEnum,
  UserLanguage,
} from '@enums';
import {
  DynamicListsLookupService,
  I18nLookupService,
  LocalBranchesLookupService,
} from '@modules';
import { SecureMailInitiateDto } from '../dtos/base.dto';

@Injectable()
export class CancelBankDraftService extends SecureMailBaseService<CancelBankDraftDto> {
  fileSystemName: string = 'CP';
  requestBodyDto: ClassConstructor<CancelBankDraftDto> = CancelBankDraftDto;

  jsonFields: SecureMailFieldConfigMap = {
    fundTransferNumber: {
      type: SecureMailFieldTypeEnum.String,
    },
    [SecureMailFieldNamesEnum.DeliveryBranchCity]: {
      type: SecureMailFieldTypeEnum.String,
    },
    [SecureMailFieldNamesEnum.DeliveryBranch]: {
      type: SecureMailFieldTypeEnum.String,
    },
  };

  constructor(
    protected readonly branchesLookup: LocalBranchesLookupService,
    protected readonly listsLookup: DynamicListsLookupService,
    protected readonly i18n: I18nLookupService,
  ) {
    super(listsLookup, i18n);
  }

  async initiate(
    body: SecureMailInitiateDto,
    user: CurrentUser,
    language: UserLanguage,
  ): Promise<any> {
    return {
      branches: this.branchesLookup.getBranchesList(language),
      governates: this.branchesLookup.getGovernatesList(language),
    };
  }

  getTextFieldValue(
    field: SecureMailFieldRecord,
    key?: string,
    body?: CancelBankDraftDto,
  ) {
    switch (key) {
      case SecureMailFieldNamesEnum.DeliveryBranch:
        return (
          this.branchesLookup.getBranchById(field.value)?.name ?? field.value
        );

      case SecureMailFieldNamesEnum.DeliveryBranchCity:
        const branch = this.branchesLookup.getBranchById(body.deliveryBranch);
        return branch?.governate ?? branch?.name ?? body.deliveryBranch;
    }

    return super.getTextFieldValue(field);
  }
}
