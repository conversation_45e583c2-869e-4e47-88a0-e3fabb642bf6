import { Injectable } from '@nestjs/common';
import { SecureMailBaseService } from './base.service';
import { ClassConstructor } from 'class-transformer';
import { AddNewUserDto } from '../dtos/add-user.dto';
import { DynamicListsLookupService, I18nLookupService } from '@modules';
import {
  SecureMailDynamicListEnum,
  SecureMailFieldTypeEnum,
  UserLanguage,
} from '@enums';
import { CurrentUser, SecureMailFieldConfigMap } from '@types';
import { SecureMailInitiateDto } from '../dtos/base.dto';
@Injectable()
export class AddNewUserService extends SecureMailBaseService<AddNewUserDto> {
  fileSystemName: string = 'CP';
  requestBodyDto: ClassConstructor<AddNewUserDto> = AddNewUserDto;

  jsonFields: SecureMailFieldConfigMap = {
    fullNameAr: {
      type: SecureMailFieldTypeEnum.String,
    },
    fullNameEn: {
      type: SecureMailFieldTypeEnum.String,
    },
    mobileNumber: {
      type: SecureMailFieldTypeEnum.String,
    },
    privilege: {
      type: SecureMailFieldTypeEnum.String,
      dynamicList: SecureMailDynamicListEnum.USER_PRIVILEGES,
    },
    portalType: {
      type: SecureMailFieldTypeEnum.String,
      dynamicList: SecureMailDynamicListEnum.PORTAL_TYPES,
    },
    tokenType: {
      type: SecureMailFieldTypeEnum.String,
      dynamicList: SecureMailDynamicListEnum.TOKEN_TYPES,
    },
    hasDelegation: {
      type: SecureMailFieldTypeEnum.Boolean,
    },
  };

  constructor(
    protected readonly listsLookup: DynamicListsLookupService,
    protected readonly i18n: I18nLookupService,
  ) {
    super(listsLookup, i18n);
  }

  async initiate(
    body: SecureMailInitiateDto,
    currentUser: CurrentUser,
    userLanguage: UserLanguage,
  ): Promise<any> {
    return {
      userPrivileges: this.listsLookup.getValues(
        SecureMailDynamicListEnum.USER_PRIVILEGES,
        userLanguage,
      ),
      portalTypes: this.listsLookup.getValues(
        SecureMailDynamicListEnum.PORTAL_TYPES,
        userLanguage,
      ),
      tokenTypes: this.listsLookup.getValues(
        SecureMailDynamicListEnum.TOKEN_TYPES,
        userLanguage,
      ),
    };
  }
}
