import { Injectable } from '@nestjs/common';
import { SecureMailBaseService } from './base.service';
import { ClassConstructor } from 'class-transformer';
import { SupportDocsDto } from '../dtos/support-docs.dto';
import { SecureMailFieldConfigMap } from '@types';
import { SecureMailFieldTypeEnum } from '@enums';

@Injectable()
export class SupportDocsService extends SecureMailBaseService<SupportDocsDto> {
  requestBodyDto: ClassConstructor<SupportDocsDto> = SupportDocsDto;
  fileSystemName: string = 'PDC';

  jsonFields: SecureMailFieldConfigMap = {
    prevRequest: {
      type: SecureMailFieldTypeEnum.Boolean,
    },
    requestNumber: {
      type: SecureMailFieldTypeEnum.String,
    },
  };
}
