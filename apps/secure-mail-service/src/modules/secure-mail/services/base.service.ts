import { BadRequestException, Injectable } from '@nestjs/common';
import { ClassConstructor, plainToInstance } from 'class-transformer';
import { validate } from 'class-validator';
import { SecureMailBaseDto, SecureMailInitiateDto } from '../dtos/base.dto';
import { SecureMailFieldTypeEnum, UserLanguage } from '@enums';
import {
  CurrentUser,
  SecureMailFieldConfigMap,
  SecureMailFieldRecord,
} from '@types';
import { mapValues } from 'lodash';
import { DynamicListsLookupService, I18nLookupService } from '@modules';
@Injectable()
export abstract class SecureMailBaseService<Dto extends SecureMailBaseDto> {
  abstract readonly requestBodyDto: ClassConstructor<Dto>;
  abstract readonly fileSystemName: string;
  abstract readonly jsonFields: SecureMailFieldConfigMap;

  constructor(
    protected readonly listsLookup: DynamicListsLookupService,
    protected readonly i18n: I18nLookupService,
  ) {}

  async initiate?(
    body: SecureMailInitiateDto | Record<string, any>,
    currentUser: CurrentUser,
    userLanguage: UserLanguage,
  ): Promise<any>;

  async transformBody?(
    body: Dto,
    currentUser: CurrentUser,
    userLanguage: UserLanguage,
  ): Promise<any>;

  async validate?(
    body: Dto,
    user: CurrentUser,
    userLanguage: UserLanguage,
  ): Promise<any>;

  async validateRequest(
    body: Dto,
    user: CurrentUser,
    userLanguage: UserLanguage,
  ) {
    await this.validateRequestBody(body);
    return this.validate?.(body, user, userLanguage);
  }

  async validateRequestBody(body: Dto) {
    const classDto = plainToInstance(this.requestBodyDto, body);
    const errors = await validate(classDto);
    if (errors.length > 0) {
      throw new BadRequestException({
        statusCode: 400,
        message: 'Validation failed',
        errors: errors.map((err) => ({
          property: err.property,
          constraints: err.constraints,
        })),
      });
    }

    return classDto;
  }

  getPayload(body: Dto) {
    const payload = {
      type: body.type,
      fileSystemName: this.fileSystemName,
      freeFormatText: this.buildFreeText(body),
      freeFormatJson: JSON.stringify(this.buildTextJson(body)),
      attachments: body.attachments,
    } as any;

    if (body.reauthPassword) {
      payload.reauthPassword = body.reauthPassword;
    }

    return payload;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  getTextFieldValue(field: SecureMailFieldRecord, key?: string, body?: Dto) {
    switch (field.type) {
      case SecureMailFieldTypeEnum.String:
        if (field.dynamicList) {
          return this.listsLookup.getItemValue(field.dynamicList, field.value);
        }
        break;

      case SecureMailFieldTypeEnum.Boolean:
        return field.value ? 'Yes' : 'No';
    }
    return field.value ?? '';
  }

  buildFreeText(body: Dto): string {
    const textArray = Object.entries(this.buildTextJson(body)).map(
      ([key, field]) =>
        `${this.i18n.translate(`secureMail.jsonFields.${key}`)}: ${this.getTextFieldValue(field, key, body)}`,
    );

    return textArray?.join('\n ');
  }

  buildTextJson(body: Dto): Record<string, any> {
    this.jsonFields.message = {
      type: SecureMailFieldTypeEnum.String,
    };

    return mapValues(this.jsonFields, (config, key) => {
      const value = body[key] ?? '';
      return {
        ...config,
        value,
      } as SecureMailFieldRecord;
    });
  }
}
