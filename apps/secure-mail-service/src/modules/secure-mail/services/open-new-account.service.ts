import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { SecureMailBaseService } from './base.service';
import { ClassConstructor } from 'class-transformer';
import {
  AccountsLookupService,
  ApiAdapterClientService,
  DynamicListsLookupService,
  I18nLookupService,
} from '@modules';
import {
  CurrentUser,
  SecureMailFieldConfigMap,
  SecureMailFieldRecord,
} from '@types';
import {
  ErrorCodeEnum,
  EsbProductTypeEnum,
  EsbRequestsEnum,
  SecureMailFieldNamesEnum,
  SecureMailFieldTypeEnum,
  UserLanguage,
} from '@enums';
import { OpenNewAccountDto } from '../dtos/open-new-account.dto';
import { EsbAccountDetailsDto } from '@dtos';
import {
  ApiValidationException,
  BusinessValidationException,
} from '@exceptions';
import { has } from 'lodash';
import { getCurrencyType } from '@helpers';
import { SecureMailInitiateDto } from '../dtos/base.dto';

@Injectable()
export class OpenNewAccountService extends SecureMailBaseService<OpenNewAccountDto> {
  private readonly logger = new Logger(OpenNewAccountService.name);

  fileSystemName: string = 'ONA';
  requestBodyDto: ClassConstructor<OpenNewAccountDto> = OpenNewAccountDto;

  jsonFields: SecureMailFieldConfigMap = {
    [SecureMailFieldNamesEnum.AccountProductCode]: {
      type: SecureMailFieldTypeEnum.AccountProduct,
    },
    [SecureMailFieldNamesEnum.Currency]: {
      type: SecureMailFieldTypeEnum.Currency,
    },
    [SecureMailFieldNamesEnum.SignatureAccountNumber]: {
      type: SecureMailFieldTypeEnum.String,
    },
    employeesNumber: {
      type: SecureMailFieldTypeEnum.String,
    },
    businessSize: {
      type: SecureMailFieldTypeEnum.String,
    },
  };

  constructor(
    protected readonly listsLookup: DynamicListsLookupService,
    protected readonly i18n: I18nLookupService,
    private readonly accountsLookup: AccountsLookupService,
    private readonly clientService: ApiAdapterClientService,
  ) {
    super(listsLookup, i18n);
  }

  getTextFieldValue(
    field: SecureMailFieldRecord,
    key: string,
    body: OpenNewAccountDto,
  ) {
    if (key === SecureMailFieldNamesEnum.AccountProductCode) {
      const product = this.accountsLookup.getProduct(field.value);
      if (product) {
        return `${product?.name} (Product Code ${field.value})`;
      }
      throw new NotFoundException(`${field.value} product is not found`);
    }
    return super.getTextFieldValue(field, key, body);
  }

  async initiate(
    body: SecureMailInitiateDto,
    user: CurrentUser,
    userLanguage: UserLanguage,
  ): Promise<any> {
    return {
      eligibleProducts: this.accountsLookup.getGroupedEligibleProductList(
        user.segment,
        userLanguage,
      ),
    };
  }

  async validate(
    body: OpenNewAccountDto,
    user: CurrentUser,
    language: UserLanguage,
  ): Promise<any> {
    const product = this.accountsLookup.getProduct(body.accountProductCode);

    const segment = product?.segments?.find(
      ({ segmentId }) => segmentId === user.segment,
    );
    this.logger.log(
      `accountCode: ${body.accountProductCode} - userSegment: ${user.segment} - segmentFound?: ${!!segment}`,
    );
    if (!segment) {
      throw new BusinessValidationException(
        ErrorCodeEnum.NotEligible,
        language,
      );
    }

    const allowMultiplePerCurrency =
      has(segment, 'allowMultiplePerCurrency') &&
      segment?.allowMultiplePerCurrency === 0;

    this.logger.log(`allowMultiplePerCurrency is ${allowMultiplePerCurrency}`);

    if (!allowMultiplePerCurrency) {
      const userAccountsList = await this.clientService.esbRequest<
        EsbAccountDetailsDto[]
      >({
        requestId: EsbRequestsEnum.ACCOUNTS_LIST,
        payload: {
          userId: user.cif,
          productType: EsbProductTypeEnum.CASA,
        },
        user,
        options: {
          language,
        },
      });

      const userAccounts = userAccountsList.map((account) => ({
        accountNumber: account.accountNumber,
        categoryCode: account.categoryCode,
        productCode: account.productInfo.productCode,
      }));

      this.logger.log(`accountsList:`, userAccounts);

      const found = userAccounts.find(
        ({ productCode }) => productCode === Number(body.accountProductCode),
      );

      if (found) {
        this.logger.log(`account is found: ${found.accountNumber}`);
        throw new BusinessValidationException(
          ErrorCodeEnum.ActiveAccountExists,
          language,
          `accounts.opening.${ErrorCodeEnum.ActiveAccountExists}.${getCurrencyType(body.currency)}`,
        );
      }
    }
    return {
      isValid: true,
    };
  }
}
