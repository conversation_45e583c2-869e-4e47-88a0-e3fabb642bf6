import { Injectable } from '@nestjs/common';
import { SecureMailBaseService } from './base.service';
import { ClassConstructor } from 'class-transformer';
import { CpsAddAmendDto } from '../dtos/cps-add-amend.dto';
import { SecureMailFieldConfigMap } from '@types';
import { SecureMailFieldTypeEnum } from '@enums';

@Injectable()
export class CpsAddAmendService extends SecureMailBaseService<CpsAddAmendDto> {
  fileSystemName: string = 'COR3';
  requestBodyDto: ClassConstructor<CpsAddAmendDto> = CpsAddAmendDto;

  jsonFields: SecureMailFieldConfigMap = {
    socialInsuranceNumber: {
      type: SecureMailFieldTypeEnum.String,
    },
    TaxRegistrationNumber: {
      type: SecureMailFieldTypeEnum.String,
    },
  };
}
