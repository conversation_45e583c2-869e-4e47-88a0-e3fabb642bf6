import { SecureMailTypesEnum } from '@enums';
import { BadRequestException, Injectable } from '@nestjs/common';
import { SecureMailBaseService } from './base.service';
import { CpsChargeBackService } from './cps-charge-back.service';
import { AddNewUserService } from './add-user.service';
import { DeleteUserService } from './delete-user.service';
import { StopPaymentsService } from './stop-payments.service';
import { ChangeUserPrivilegeService } from './change-user-privilege.service';
import { CancelSecureMailService } from './cancel-securemail.service';
import { AddNewUserPrivilegeService } from './add-user-permission.service';
import { AssignNewProductService } from './assign-new-product.service';
import { CancelBankDraftService } from './cancel-bank-draft.service';
import { CpsAmendServiceService } from './cps-amend-service.service';
import { SupportDocsService } from './support-docs.service';
import { CpsAddAmendService } from './cps-add-amend.service';
import { AmendUserDataService } from './amend-user-data.service';
import { RequestBankDraftService } from './request-bank-draft.service';
import { OtherPortalsRelatedRequestService } from './other-portals-related-request.service';
import { OpenNewAccountService } from './open-new-account.service';
import { DisputeCreditCardService } from './dispute-credit-card.service';
import { ManageDirectDisputeService } from './manage-direct-debit.service';
import { RedeemTermDepositService } from './redeem-term-deposit.service';

@Injectable()
export class SecureMailDispatcherService {
  private readonly services: Record<
    SecureMailTypesEnum,
    SecureMailBaseService<any>
  >;

  constructor(
    private readonly cpsChargeBackService: CpsChargeBackService,
    private readonly addNewUserService: AddNewUserService,
    private readonly deleteUserService: DeleteUserService,
    private readonly stopPaymentsService: StopPaymentsService,
    private readonly changeUserPrivilegeService: ChangeUserPrivilegeService,
    private readonly cancelSecureMailService: CancelSecureMailService,
    private readonly addNewUserPrivilegeService: AddNewUserPrivilegeService,
    private readonly assignNewProductService: AssignNewProductService,
    private readonly cancelBankDraftService: CancelBankDraftService,
    private readonly cpsAmendServiceService: CpsAmendServiceService,
    private readonly supportDocsService: SupportDocsService,
    private readonly cpsAddAmendService: CpsAddAmendService,
    private readonly amendUserDataService: AmendUserDataService,
    private readonly requestBankDraftService: RequestBankDraftService,
    private readonly otherPortalsRelatedRequestService: OtherPortalsRelatedRequestService,
    private readonly openNewAccountService: OpenNewAccountService,
    private readonly disputeCreditCardService: DisputeCreditCardService,
    private readonly manageDirectDebitService: ManageDirectDisputeService,
    private readonly redeemTdService: RedeemTermDepositService,
  ) {
    this.services = {
      [SecureMailTypesEnum.CPS_CHARGE_BACK_REQUEST]: this.cpsChargeBackService,
      [SecureMailTypesEnum.ADD_NEW_USER]: this.addNewUserService,
      [SecureMailTypesEnum.DELETE_USERS]: this.deleteUserService,
      [SecureMailTypesEnum.STOP_MI_PAYMENTS]: this.stopPaymentsService,
      [SecureMailTypesEnum.CHANGE_USER_PRIVILEGES]:
        this.changeUserPrivilegeService,
      [SecureMailTypesEnum.CANCEL_SECURE_EMAIL]: this.cancelSecureMailService,
      [SecureMailTypesEnum.ADD_NEW_PERMISSION]: this.addNewUserPrivilegeService,
      [SecureMailTypesEnum.ASSIGN_NEW_PRODUCT]: this.assignNewProductService,
      [SecureMailTypesEnum.CANCEL_BANK_DRAFT]: this.cancelBankDraftService,
      [SecureMailTypesEnum.CPS_AMEND_ADD_SERVICE]: this.cpsAmendServiceService,
      [SecureMailTypesEnum.SUPPORTING_DOCUMENTS]: this.supportDocsService,
      [SecureMailTypesEnum.AMEND_USER_DATA]: this.amendUserDataService,
      [SecureMailTypesEnum.REQUEST_BANK_DRAFT]: this.requestBankDraftService,
      [SecureMailTypesEnum.OTHER_PORTALS_RELATED_REQUEST]:
        this.otherPortalsRelatedRequestService,
      [SecureMailTypesEnum.OPEN_NEW_ACCOUNT]: this.openNewAccountService,
      [SecureMailTypesEnum.DISPUTE_CREDIT_CARD]: this.disputeCreditCardService,
      [SecureMailTypesEnum.MANAGE_DIRECT_DEBIT]: this.manageDirectDebitService,
      [SecureMailTypesEnum.REDEEM_TD]: this.redeemTdService,
    };
  }

  getService(type: SecureMailTypesEnum): SecureMailBaseService<any> {
    const service = this.services[type];
    if (!service) {
      throw new BadRequestException(`No service found`);
    }
    return service;
  }
}
