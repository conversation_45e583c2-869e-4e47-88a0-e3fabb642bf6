import { Injectable } from '@nestjs/common';
import { SecureMailBaseService } from './base.service';
import { ClassConstructor } from 'class-transformer';
import { CpsAmendServiceDto } from '../dtos/cps-amend-service.dto';
import { SecureMailFieldConfigMap } from '@types';
import { SecureMailFieldTypeEnum } from '@enums';

@Injectable()
export class CpsAmendServiceService extends SecureMailBaseService<CpsAmendServiceDto> {
  requestBodyDto: ClassConstructor<CpsAmendServiceDto> = CpsAmendServiceDto;
  fileSystemName: string = 'COR3';

  jsonFields: SecureMailFieldConfigMap = {
    socialInsuranceNumber: {
      type: SecureMailFieldTypeEnum.String,
    },
    taxRegisterationNumber: {
      type: SecureMailFieldTypeEnum.String,
    },
  };

  getTextJson(body: CpsAmendServiceDto): Record<string, any> {
    return {
      socialInsuranceNumber: body.socialInsuranceNumber,
      taxRegisterationNumber: body.taxRegisterationNumber,
    };
  }
}
