import { Injectable } from '@nestjs/common';
import { SecureMailBaseService } from './base.service';
import { ClassConstructor } from 'class-transformer';
import { StopPaymentsDto } from '../dtos/stop-payments.dto';
import { SecureMailFieldConfigMap } from '@types';
import { SecureMailFieldTypeEnum } from '@enums';

@Injectable()
export class StopPaymentsService extends SecureMailBaseService<StopPaymentsDto> {
  fileSystemName: string = 'SP';
  requestBodyDto: ClassConstructor<StopPaymentsDto> = StopPaymentsDto;

  jsonFields: SecureMailFieldConfigMap = {
    fundTransferNumber: {
      type: SecureMailFieldTypeEnum.String,
    },
  };
}
