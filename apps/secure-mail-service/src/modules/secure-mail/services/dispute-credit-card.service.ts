import { Injectable } from '@nestjs/common';
import { SecureMailBaseService } from './base.service';
import { SecureMailFieldConfigMap } from '@types';
import { SecureMailFieldTypeEnum } from '@enums';
import { DisputeCreditCardsDto } from '../dtos/dispute-credit-cards.dto';
import { ClassConstructor } from 'class-transformer';
import { DynamicListsLookupService, I18nLookupService } from '@modules';

@Injectable()
export class DisputeCreditCardService extends SecureMailBaseService<DisputeCreditCardsDto> {
  fileSystemName: string = 'CRDSP';

  requestBodyDto: ClassConstructor<DisputeCreditCardsDto> =
    DisputeCreditCardsDto;

  jsonFields: SecureMailFieldConfigMap = {
    disputeReason: { type: SecureMailFieldTypeEnum.String },
  };

  constructor(
    protected readonly listsLookup: DynamicListsLookupService,
    protected readonly i18n: I18nLookupService,
  ) {
    super(listsLookup, i18n);
  }
}
