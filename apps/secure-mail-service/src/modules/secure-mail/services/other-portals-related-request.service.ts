import { Injectable } from '@nestjs/common';
import { SecureMailBaseService } from './base.service';
import { ClassConstructor } from 'class-transformer';
import { DynamicListsLookupService, I18nLookupService } from '@modules';
import { OtherPortalsRelatedRequestDto } from '../dtos/other-portals-related-request.dto';
import { SecureMailFieldConfigMap } from '@types';

@Injectable()
export class OtherPortalsRelatedRequestService extends SecureMailBaseService<OtherPortalsRelatedRequestDto> {
  fileSystemName: string = 'fileSystem2';
  requestBodyDto: ClassConstructor<OtherPortalsRelatedRequestDto> =
    OtherPortalsRelatedRequestDto;

  jsonFields: SecureMailFieldConfigMap = {};

  constructor(
    protected readonly listsLookup: DynamicListsLookupService,
    protected readonly i18n: I18nLookupService,
  ) {
    super(listsLookup, i18n);
  }
}
