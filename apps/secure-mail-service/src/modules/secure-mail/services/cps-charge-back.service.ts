import { Injectable } from '@nestjs/common';
import { CpsChargeBackDto } from '../dtos/cps-charge-back.dto';
import { SecureMailBaseService } from './base.service';
import { ClassConstructor } from 'class-transformer';
import { SecureMailFieldConfigMap } from '@types';
import { SecureMailFieldTypeEnum } from '@enums';

@Injectable()
export class CpsChargeBackService extends SecureMailBaseService<CpsChargeBackDto> {
  jsonFields: SecureMailFieldConfigMap = {
    fundTransferNumber: {
      type: SecureMailFieldTypeEnum.String,
    },
    amount: {
      type: SecureMailFieldTypeEnum.String,
    },
    accountNumber: {
      type: SecureMailFieldTypeEnum.String,
    },
  };
  requestBodyDto: ClassConstructor<CpsChargeBackDto> = CpsChargeBackDto;
  fileSystemName: string = 'COR2';
}
