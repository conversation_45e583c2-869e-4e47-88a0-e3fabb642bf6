import { Injectable, Logger } from '@nestjs/common';
import { SecureMailBaseService } from './base.service';
import { ClassConstructor } from 'class-transformer';
import {
  ApiAdapterClientService,
  DynamicListsLookupService,
  I18nLookupService,
  TermDepositDetailsService,
} from '@modules';
import {
  DepositRedemptionTypeEnum,
  ErrorCodeEnum,
  EsbRequestsEnum,
  SecureMailFieldNamesEnum,
  SecureMailFieldTypeEnum,
  UserLanguage,
} from '@enums';
import { CurrentUser, DepositId, SecureMailFieldConfigMap } from '@types';
import { RedeemTermDepositDto } from '../dtos/redeem-td.dto';
import { BusinessValidationException } from '@exceptions';
import { decryptUserJson } from '@helpers';
import { ConfigService } from '@nestjs/config';
import { BreakDepositProductFulfillmentDto, EsbAccountDetailsDto } from '@dtos';

@Injectable()
export class RedeemTermDepositService extends SecureMailBaseService<RedeemTermDepositDto> {
  private readonly logger = new Logger(RedeemTermDepositService.name);

  fileSystemName: string = 'COR4';

  requestBodyDto: ClassConstructor<RedeemTermDepositDto> = RedeemTermDepositDto;

  jsonFields: SecureMailFieldConfigMap = {
    [SecureMailFieldNamesEnum.AccountId]: {
      type: SecureMailFieldTypeEnum.String,
    },
    [SecureMailFieldNamesEnum.DepositId]: {
      type: SecureMailFieldTypeEnum.String,
    },
    redemptionType: {
      type: SecureMailFieldTypeEnum.String,
    },
    redemptionAccountNumber: {
      type: SecureMailFieldTypeEnum.String,
    },
    redemptionAmount: {
      type: SecureMailFieldTypeEnum.String,
    },
  };

  constructor(
    protected readonly listsLookup: DynamicListsLookupService,
    protected readonly i18n: I18nLookupService,
    protected readonly configService: ConfigService,
    protected readonly detailsService: TermDepositDetailsService,
    protected readonly apiClientService: ApiAdapterClientService,
  ) {
    super(listsLookup, i18n);
  }

  async transformBody(
    body: RedeemTermDepositDto,
    currentUser: CurrentUser,
    userLanguage: UserLanguage,
  ): Promise<any> {
    const { accountId, depositId } = decryptUserJson<DepositId>(
      decodeURIComponent(body.id),
      this.configService.get('aesSecret'),
      currentUser.userId,
    );

    return {
      ...body,
      [SecureMailFieldNamesEnum.DepositId]: depositId,
      [SecureMailFieldNamesEnum.AccountId]: accountId,
    };
  }

  async initiate(
    body: { id: string },
    user: CurrentUser,
    userLanguage: UserLanguage,
  ): Promise<any> {
    const details = await this.getDepositDetails(body?.id, user, userLanguage);

    if (
      details.totalHeld.amount > 0 &&
      details.totalHeld.amount === details.bookBalance.amount
    ) {
      throw new BusinessValidationException(
        ErrorCodeEnum.FullDepositAmountBlocked,
        userLanguage,
      );
    }

    const { fullRedemption, partialRedemption } = details.allowedRedemptionType;
    if (!details.product || (!fullRedemption && !partialRedemption)) {
      throw new BusinessValidationException(
        ErrorCodeEnum.RequestRejected,
        userLanguage,
      );
    }

    const principalAccount =
      await this.apiClientService.esbRequest<EsbAccountDetailsDto>({
        requestId: EsbRequestsEnum.ACCOUNT_DETAILS,
        payload: {
          accountNumber: details.accounts.principle,
        },
        options: { language: userLanguage },
      });

    const drawAccount =
      await this.apiClientService.esbRequest<EsbAccountDetailsDto>({
        requestId: EsbRequestsEnum.ACCOUNT_DETAILS,
        payload: {
          accountNumber: details.accounts.drawn,
        },
        options: { language: userLanguage },
      });

    return {
      details,
      allowedRedemptionType: { fullRedemption, partialRedemption },

      minimumRedemptionAmount: details.product.minimumRedemptionAmount ?? 1,
      minimumBookingAmount: details.product.minimumBookingAmount ?? 5000,
      maximumRedemptionAmount: details.maximumRedemptionAmount,

      principleAccount: {
        id: details.accounts.principle,
        description: principalAccount.accountTypeDesc,
        overdraft: principalAccount.isOverdraft,
      },
      drawnAccount: {
        id: details.accounts.drawn,
        description: drawAccount.accountTypeDesc,
        overdraft: drawAccount?.isOverdraft,
      },

      username: user?.username,
      productName: details?.product?.name,
    };
  }

  async validate(
    body: RedeemTermDepositDto,
    user: CurrentUser,
    userLanguage: UserLanguage,
  ): Promise<any> {
    const details = await this.getDepositDetails(body.id, user, userLanguage);
    if (body.redemptionType === DepositRedemptionTypeEnum.Full) {
      body.redemptionAmount = details.productDetails.amount;
    }
    this.checkLessThanMinimumRedemptionAmount(body, details, userLanguage);
    this.checkIfLessThanMinimumBookingAmount(body, details, userLanguage);
    this.checkIfRedemptionAmountMoreThanDeposit(body, details, userLanguage);
    this.matchingRedemptionTypeAgainstAccount(body, details, userLanguage);

    //to be use account eligiblty service
    await this.checkAccountHasPostingRestrictionOrInactive(body, userLanguage);

    const simulateRedemption: any = await this.simulateRedemption(
      details.productDetails.number,
      body,
      userLanguage,
    );

    return {
      depositNumber: details.productDetails.number,
      redemptionType: body.redemptionType,
      amount: body.redemptionAmount,
      penaltyAmount: simulateRedemption?.penaltyAmount || 0,
      accountNumber: body.redemptionAccountNumber,
    };
  }

  async getDepositDetails(
    encryptedId: string,
    user: CurrentUser,
    userLanguage: UserLanguage,
  ) {
    if (!encryptedId) {
      throw new BusinessValidationException(
        ErrorCodeEnum.NotFound,
        userLanguage,
      );
    }

    const { accountId } = decryptUserJson<DepositId>(
      decodeURIComponent(encryptedId),
      this.configService.get('aesSecret'),
      user.userId,
    );

    return this.detailsService.getDetails(accountId, user, {
      userLanguage,
    });
  }

  private async simulateRedemption(
    depositId: string,
    body: RedeemTermDepositDto,
    userLanguage: UserLanguage,
  ) {
    const simulateRedemption = await this.apiClientService
      .esbRequest<BreakDepositProductFulfillmentDto>({
        requestId: EsbRequestsEnum.DEPOSIT_SIMULATE_REDEMPTION,
        payload: {
          depositId,
          amount: body.redemptionAmount,
        },
      })
      .catch(() => {
        throw new BusinessValidationException(
          ErrorCodeEnum.EndOfDayRunning,
          userLanguage,
        );
      });
    return simulateRedemption;
  }

  private async checkAccountHasPostingRestrictionOrInactive(
    body: RedeemTermDepositDto,
    userLanguage: UserLanguage,
  ) {
    const accountDetails =
      await this.apiClientService.esbRequest<EsbAccountDetailsDto>({
        requestId: EsbRequestsEnum.ACCOUNT_DETAILS,
        payload: {
          accountNumber: body.redemptionAccountNumber,
        },
        options: { language: userLanguage },
      });
    if (!!accountDetails?.accountTrnLimit?.postingRestriction) {
      this.logger.debug(`account has posting restriction`);
      throw new BusinessValidationException(
        ErrorCodeEnum.RequestRejected,
        userLanguage,
      );
    }
    if (accountDetails?.accountStatus?.accountStatusCode != 'Active') {
      this.logger.debug(`account status inactive`);
      throw new BusinessValidationException(
        ErrorCodeEnum.RequestRejected,
        userLanguage,
      );
    }
  }

  private matchingRedemptionTypeAgainstAccount(
    body: RedeemTermDepositDto,
    details: any,
    userLanguage: UserLanguage,
  ) {
    if (
      body.redemptionType === DepositRedemptionTypeEnum.Full &&
      body.redemptionAccountNumber != details.accounts.principle
    ) {
      this.logger.debug(
        `Full redemption account not matching principleLiquidationAccount`,
      );
      throw new BusinessValidationException(
        ErrorCodeEnum.IneligibleRedemptionAccount,
        userLanguage,
      );
    }
    if (
      body.redemptionType === DepositRedemptionTypeEnum.Partial &&
      body.redemptionAccountNumber != details.accounts.drawn
    ) {
      this.logger.debug(
        `Partial redemption account not matching drawLiquidationAccount`,
      );
      throw new BusinessValidationException(
        ErrorCodeEnum.IneligibleRedemptionAccount,
        userLanguage,
      );
    }
  }

  private checkIfLessThanMinimumBookingAmount(
    body: RedeemTermDepositDto,
    details: any,
    userLanguage: UserLanguage,
  ) {
    if (
      details.bookBalance.amount - body.redemptionAmount <
      details.product.minimumBookingAmount
    ) {
      this.logger.debug(
        `TD less than minimum booking amount: ${
          details.bookBalance.amount - body.redemptionAmount <
          details.product.minimumBookingAmount
        }`,
      );
      throw new BusinessValidationException(
        ErrorCodeEnum.LessThanMinimumBookingAmount,
        userLanguage,
      );
    }
  }

  private checkLessThanMinimumRedemptionAmount(
    body: RedeemTermDepositDto,
    details: any,
    userLanguage: UserLanguage,
  ) {
    const { minimumRedemptionAmount } = details.product;
    if (body.redemptionAmount < minimumRedemptionAmount) {
      this.logger.debug(
        `TD less than minimum redemption amount: ${body.redemptionAmount}, 
          ${minimumRedemptionAmount}
        `,
      );
      throw new BusinessValidationException(
        ErrorCodeEnum.LessThanMinimumRedemptionAmount,
        userLanguage,
      );
    }
  }

  private checkIfRedemptionAmountMoreThanDeposit(
    body: RedeemTermDepositDto,
    details: any,
    userLanguage: UserLanguage,
  ) {
    if (body.redemptionAmount > details?.bookBalance?.amount) {
      this.logger.debug(
        `TD book balance less than total redemption amount: ${body.redemptionAmount}, 
          ${details?.bookBalance?.amount}
        }`,
      );
      throw new BusinessValidationException(
        ErrorCodeEnum.MoreThanDepositAmount,
        userLanguage,
      );
    }
  }
}
