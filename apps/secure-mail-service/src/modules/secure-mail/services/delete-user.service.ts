import { Injectable } from '@nestjs/common';
import { SecureMailBaseService } from './base.service';
import { ClassConstructor } from 'class-transformer';
import { DeleteUserDto } from '../dtos/delete-user.dto';
import { CurrentUser, SecureMailFieldConfigMap } from '@types';
import {
  FccRequestsEnum,
  SecureMailDynamicListEnum,
  SecureMailFieldTypeEnum,
  UserLanguage,
} from '@enums';
import {
  DynamicListsLookupService,
  FccClientService,
  I18nLookupService,
} from '@modules';
import { SecureMailInitiateDto } from '../dtos/base.dto';

@Injectable()
export class DeleteUserService extends SecureMailBaseService<DeleteUserDto> {
  fileSystemName: string = 'SS';
  requestBodyDto: ClassConstructor<DeleteUserDto> = DeleteUserDto;

  jsonFields: SecureMailFieldConfigMap = {
    portalType: {
      type: SecureMailFieldTypeEnum.String,
      dynamicList: SecureMailDynamicListEnum.PORTAL_TYPES,
    },
    user: {
      type: SecureMailFieldTypeEnum.String,
    },
  };

  constructor(
    private readonly clientService: FccClientService,
    protected readonly listsLookup: DynamicListsLookupService,
    protected readonly i18n: I18nLookupService,
  ) {
    super(listsLookup, i18n);
  }

  async initiate(
    body: SecureMailInitiateDto,
    user: CurrentUser,
    userLanguage: UserLanguage,
  ): Promise<any> {
    const usersList = await this.clientService.call<any>({
      requestId: FccRequestsEnum.USERS_LIST,
      user,
    });

    return {
      portalTypes: this.listsLookup.getValues(
        SecureMailDynamicListEnum.PORTAL_TYPES,
        userLanguage,
      ),
      usersList,
    };
  }
}
