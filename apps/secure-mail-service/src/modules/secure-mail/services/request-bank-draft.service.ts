import { Injectable } from '@nestjs/common';
import { SecureMailBaseService } from './base.service';
import { ClassConstructor } from 'class-transformer';
import {
  DynamicListsLookupService,
  I18nLookupService,
  LocalBranchesLookupService,
} from '@modules';
import { RequestBankDraftDto } from '../dtos/request-bank-draft.dto';
import {
  CurrentUser,
  SecureMailFieldConfigMap,
  SecureMailFieldRecord,
} from '@types';
import {
  SecureMailFieldNamesEnum,
  SecureMailFieldTypeEnum,
  UserLanguage,
} from '@enums';
import { SecureMailInitiateDto } from '../dtos/base.dto';

@Injectable()
export class RequestBankDraftService extends SecureMailBaseService<RequestBankDraftDto> {
  fileSystemName: string = 'CP';
  requestBodyDto: ClassConstructor<RequestBankDraftDto> = RequestBankDraftDto;

  jsonFields: SecureMailFieldConfigMap = {
    amount: {
      type: SecureMailFieldTypeEnum.Number,
    },
    parentBranch: {
      type: SecureMailFieldTypeEnum.String,
    },
    deliveryBranchCity: {
      type: SecureMailFieldTypeEnum.String,
    },
    deliveryBranch: {
      type: SecureMailFieldTypeEnum.String,
    },
  };

  constructor(
    protected readonly branchesLookup: LocalBranchesLookupService,
    protected readonly listsLookup: DynamicListsLookupService,
    protected readonly i18n: I18nLookupService,
  ) {
    super(listsLookup, i18n);
  }

  async initiate(
    body: SecureMailInitiateDto,
    user: CurrentUser,
    language: UserLanguage,
  ): Promise<any> {
    return {
      branches: this.branchesLookup.getBranchesList(language),
      governates: this.branchesLookup.getGovernatesList(language),
    };
  }

  getTextFieldValue(
    field: SecureMailFieldRecord,
    key?: string,
    body?: RequestBankDraftDto,
  ) {
    switch (key) {
      case SecureMailFieldNamesEnum.DeliveryBranch:
        return (
          this.branchesLookup.getBranchById(field.value)?.name ?? field.value
        );

      case SecureMailFieldNamesEnum.DeliveryBranchCity:
        const branch = this.branchesLookup.getBranchById(body.deliveryBranch);
        return branch?.governate ?? branch?.name ?? body.deliveryBranch;
    }

    return super.getTextFieldValue(field);
  }
}
