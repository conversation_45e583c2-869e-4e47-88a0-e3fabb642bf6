import { Injectable } from '@nestjs/common';
import { SecureMailBaseService } from './base.service';
import { ClassConstructor } from 'class-transformer';
import {
  DynamicListsLookupService,
  FccClientService,
  I18nLookupService,
} from '@modules';
import {
  FccRequestsEnum,
  SecureMailDynamicListEnum,
  SecureMailFieldTypeEnum,
  UserLanguage,
} from '@enums';
import { AmendUserDataDto } from '../dtos/amend-user-data.dto';
import { CurrentUser, SecureMailFieldConfigMap } from '@types';
import { SecureMailInitiateDto } from '../dtos/base.dto';

@Injectable()
export class AmendUserDataService extends SecureMailBaseService<AmendUserDataDto> {
  fileSystemName: string = 'COR9';
  requestBodyDto: ClassConstructor<AmendUserDataDto> = AmendUserDataDto;

  jsonFields: SecureMailFieldConfigMap = {
    user: {
      type: SecureMailFieldTypeEnum.String,
    },
    newMobileNumber: {
      type: SecureMailFieldTypeEnum.String,
    },
    newEmail: {
      type: SecureMailFieldTypeEnum.String,
    },
    portalType: {
      type: SecureMailFieldTypeEnum.String,
      dynamicList: SecureMailDynamicListEnum.PORTAL_TYPES,
    },
  };

  constructor(
    protected readonly listsLookup: DynamicListsLookupService,
    private readonly clientService: FccClientService,
    protected readonly i18n: I18nLookupService,
  ) {
    super(listsLookup, i18n);
  }

  async initiate(
    body: SecureMailInitiateDto,
    user: CurrentUser,
    userLanguage: UserLanguage,
  ): Promise<any> {
    const usersList = await this.clientService.call<any>({
      requestId: FccRequestsEnum.USERS_LIST,
      user,
    });

    return {
      portalTypes: this.listsLookup.getValues(
        SecureMailDynamicListEnum.PORTAL_TYPES,
        userLanguage,
      ),
      usersList,
    };
  }
}
