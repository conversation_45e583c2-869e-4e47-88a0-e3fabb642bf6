import { Injectable } from '@nestjs/common';
import { SecureMailBaseService } from './base.service';
import { ClassConstructor } from 'class-transformer';
import { CancelSecureMailDto } from '../dtos/cancel-sercuremail.dto';
import {
  DynamicListsLookupService,
  FccClientService,
  I18nLookupService,
} from '@modules';
import {
  SecureMailDynamicListEnum,
  SecureMailFieldTypeEnum,
  UserLanguage,
} from '@enums';
import { CurrentUser, SecureMailFieldConfigMap } from '@types';
import { SecureMailInitiateDto } from '../dtos/base.dto';

@Injectable()
export class CancelSecureMailService extends SecureMailBaseService<CancelSecureMailDto> {
  fileSystemName: string = 'COR6';
  requestBodyDto: ClassConstructor<CancelSecureMailDto> = CancelSecureMailDto;

  jsonFields: SecureMailFieldConfigMap = {
    secureMailType: {
      type: SecureMailFieldTypeEnum.String,
      dynamicList: SecureMailDynamicListEnum.SECURE_MAIL_TYPES,
    },
    referenceNumber: {
      type: SecureMailFieldTypeEnum.String,
    },
  };

  constructor(
    protected readonly listsLookup: DynamicListsLookupService,
    protected readonly i18n: I18nLookupService,
  ) {
    super(listsLookup, i18n);
  }

  async initiate(
    body: SecureMailInitiateDto,
    user: CurrentUser,
    userLanguage: UserLanguage,
  ): Promise<any> {
    return {
      secureMailTypes: this.listsLookup.getValues(
        SecureMailDynamicListEnum.SECURE_MAIL_TYPES,
        userLanguage,
      ),
    };
  }
}
