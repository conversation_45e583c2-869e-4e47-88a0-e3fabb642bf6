import { Injectable } from '@nestjs/common';
import { SecureMailBaseService } from './base.service';
import { ClassConstructor } from 'class-transformer';
import { ChangeUserPrivilegeDto } from '../dtos/change-user-privilege.dto';
import {
  DynamicListsLookupService,
  FccClientService,
  I18nLookupService,
} from '@modules';
import { CurrentUser, SecureMailFieldConfigMap } from '@types';
import {
  FccRequestsEnum,
  SecureMailDynamicListEnum,
  SecureMailFieldTypeEnum,
  UserLanguage,
} from '@enums';
import { SecureMailInitiateDto } from '../dtos/base.dto';

@Injectable()
export class ChangeUserPrivilegeService extends SecureMailBaseService<ChangeUserPrivilegeDto> {
  fileSystemName: string = 'COR7';
  requestBodyDto: ClassConstructor<ChangeUserPrivilegeDto> =
    ChangeUserPrivilegeDto;

  jsonFields: SecureMailFieldConfigMap = {
    privilege: {
      type: SecureMailFieldTypeEnum.String,
      dynamicList: SecureMailDynamicListEnum.USER_PRIVILEGES,
    },
    user: {
      type: SecureMailFieldTypeEnum.String,
    },
  };

  constructor(
    private readonly clientService: FccClientService,
    protected readonly listsLookup: DynamicListsLookupService,
    protected readonly i18n: I18nLookupService,
  ) {
    super(listsLookup, i18n);
  }

  async initiate(
    body: SecureMailInitiateDto,
    user: CurrentUser,
    userLanguage: UserLanguage,
  ): Promise<any> {
    const usersList = await this.clientService.call<any>({
      requestId: FccRequestsEnum.USERS_LIST,
      user,
    });

    return {
      userPrivileges: this.listsLookup.getValues(
        SecureMailDynamicListEnum.USER_PRIVILEGES,
        userLanguage,
      ),
      usersList,
    };
  }
}
