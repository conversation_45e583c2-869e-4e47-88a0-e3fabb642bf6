import { ManageDirectDisputeDTO } from '../dtos/manage-direct-debit.dto';
import { SecureMailFieldConfigMap } from '@types';
import { DynamicListsLookupService, I18nLookupService } from '@modules';
import { Injectable } from '@nestjs/common';
import { ClassConstructor } from 'class-transformer';
import { SecureMailFieldTypeEnum } from '@enums';
import { SecureMailBaseService } from './base.service';

@Injectable()
export class ManageDirectDisputeService extends SecureMailBaseService<ManageDirectDisputeDTO> {
  fileSystemName: string = 'MDD';

  requestBodyDto: ClassConstructor<ManageDirectDisputeDTO> =
    ManageDirectDisputeDTO;

  jsonFields: SecureMailFieldConfigMap = {
    directDebitAccount: { type: SecureMailFieldTypeEnum.String },
    directDebitAmount: { type: SecureMailFieldTypeEnum.String },
  };

  constructor(
    protected readonly listsLookup: DynamicListsLookupService,
    protected readonly i18n: I18nLookupService,
  ) {
    super(listsLookup, i18n);
  }
}
