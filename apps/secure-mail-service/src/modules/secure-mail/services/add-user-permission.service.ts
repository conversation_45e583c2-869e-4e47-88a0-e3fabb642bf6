import { Injectable } from '@nestjs/common';
import { SecureMailBaseService } from './base.service';
import { ClassConstructor } from 'class-transformer';
import { AddNewPermissionDto } from '../dtos/add-user-permission.dto';
import {
  DynamicListsLookupService,
  FccClientService,
  I18nLookupService,
} from '@modules';
import {
  FccRequestsEnum,
  SecureMailDynamicListEnum,
  SecureMailFieldTypeEnum,
  UserLanguage,
} from '@enums';
import { CurrentUser, SecureMailFieldConfigMap } from '@types';
import { SecureMailInitiateDto } from '../dtos/base.dto';

@Injectable()
export class AddNewUserPrivilegeService extends SecureMailBaseService<AddNewPermissionDto> {
  fileSystemName: string = 'ERQ';
  requestBodyDto: ClassConstructor<AddNewPermissionDto> = AddNewPermissionDto;

  jsonFields: SecureMailFieldConfigMap = {
    accessPermission: {
      type: SecureMailFieldTypeEnum.String,
      dynamicList: SecureMailDynamicListEnum.ACCESS_PERMISSIONS,
    },
    user: {
      type: SecureMailFieldTypeEnum.String,
    },
    tokenType: {
      type: SecureMailFieldTypeEnum.String,
      dynamicList: SecureMailDynamicListEnum.TOKEN_TYPES,
    },
  };

  constructor(
    private readonly clientService: FccClientService,
    protected readonly listsLookup: DynamicListsLookupService,
    protected readonly i18n: I18nLookupService,
  ) {
    super(listsLookup, i18n);
  }

  async initiate(
    body: SecureMailInitiateDto,
    user: CurrentUser,
    userLanguage: UserLanguage,
  ): Promise<any> {
    const usersList = await this.clientService.call<any>({
      requestId: FccRequestsEnum.USERS_LIST,
      user,
    });

    return {
      accessPermissions: this.listsLookup.getValues(
        SecureMailDynamicListEnum.ACCESS_PERMISSIONS,
        userLanguage,
      ),
      tokenTypes: this.listsLookup.getValues(
        SecureMailDynamicListEnum.TOKEN_TYPES,
        userLanguage,
      ),
      usersList,
    };
  }
}
