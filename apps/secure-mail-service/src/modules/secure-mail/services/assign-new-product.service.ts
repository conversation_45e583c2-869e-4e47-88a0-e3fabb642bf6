import { Injectable } from '@nestjs/common';
import { SecureMailBaseService } from './base.service';
import { ClassConstructor } from 'class-transformer';
import { AssignNewProductDto } from '../dtos/assign-new-product.dto';
import {
  DynamicListsLookupService,
  FccClientService,
  I18nLookupService,
} from '@modules';
import { CurrentUser, SecureMailFieldConfigMap } from '@types';
import {
  FccRequestsEnum,
  SecureMailDynamicListEnum,
  SecureMailFieldTypeEnum,
  UserLanguage,
} from '@enums';
import { SecureMailInitiateDto } from '../dtos/base.dto';

@Injectable()
export class AssignNewProductService extends SecureMailBaseService<AssignNewProductDto> {
  fileSystemName: string = 'NP';
  requestBodyDto: ClassConstructor<AssignNewProductDto> = AssignNewProductDto;

  jsonFields: SecureMailFieldConfigMap = {
    productType: {
      type: SecureMailFieldTypeEnum.String,
      dynamicList: SecureMailDynamicListEnum.PRODUCT_TYPES,
    },
    productNumber: {
      type: SecureMailFieldTypeEnum.String,
    },
    user: {
      type: SecureMailFieldTypeEnum.String,
    },
    portalType: {
      type: SecureMailFieldTypeEnum.String,
      dynamicList: SecureMailDynamicListEnum.PORTAL_TYPES,
    },
  };

  constructor(
    private readonly clientService: FccClientService,
    protected readonly listsLookup: DynamicListsLookupService,
    protected readonly i18n: I18nLookupService,
  ) {
    super(listsLookup, i18n);
  }

  async initiate(
    body: SecureMailInitiateDto,
    user: CurrentUser,
    userLanguage: UserLanguage,
  ): Promise<any> {
    const usersList = await this.clientService.call<any>({
      requestId: FccRequestsEnum.USERS_LIST,
      user,
    });

    return {
      portalTypes: this.listsLookup.getValues(
        SecureMailDynamicListEnum.PORTAL_TYPES,
        userLanguage,
      ),
      productTypes: this.listsLookup.getValues(
        SecureMailDynamicListEnum.PRODUCT_TYPES,
        userLanguage,
      ),
      usersList,
    };
  }
}
