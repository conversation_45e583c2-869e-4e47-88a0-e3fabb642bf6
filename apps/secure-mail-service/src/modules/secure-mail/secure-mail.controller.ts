import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { getCurrentUser, getUserLanguage, JwtAuthGuard } from '@modules';
import { CurrentUser } from '@types';
import { SecureMailService } from '@/modules/secure-mail/secure-mail.service';
import { FccRequestsEnum, UserLanguage } from '@enums';
import { SecureMailInitiateDto } from './dtos/base.dto';
import { TransactionsListQueryDto } from '@dtos';

@Controller('requests')
@UseGuards(JwtAuthGuard)
export class SecureMailController {
  constructor(private readonly secureMailService: SecureMailService) {}

  @Get('approved-list')
  async approvedList(
    @Query() query: TransactionsListQueryDto,
    @getCurrentUser() currentUser: CurrentUser,
    @getUserLanguage() userLanguage: UserLanguage,
  ) {
    return this.secureMailService.list(
      FccRequestsEnum.SECURE_MAIL_APPROVED_LIST,
      query,
      currentUser,
      userLanguage,
    );
  }

  @Get('rejected-list')
  async rejectedList(
    @Query() query: TransactionsListQueryDto,
    @getCurrentUser() currentUser: CurrentUser,
    @getUserLanguage() userLanguage: UserLanguage,
  ) {
    return this.secureMailService.list(
      FccRequestsEnum.SECURE_MAIL_REJECTED_LIST,
      query,
      currentUser,
      userLanguage,
    );
  }

  @Get('/:refId')
  async getDetails(
    @Param('refId') refId: string,
    @getCurrentUser() currentUser: CurrentUser,
    @getUserLanguage() userLanguage: UserLanguage,
  ) {
    return this.secureMailService.getDetails(refId, currentUser, userLanguage);
  }

  @Post('initiate')
  @HttpCode(HttpStatus.OK)
  async initiate(
    @Body() body: any,
    @getCurrentUser() currentUser: CurrentUser,
    @getUserLanguage() userLanguage: UserLanguage,
  ) {
    return this.secureMailService.initiate(body, currentUser, userLanguage);
  }

  @Post('validate')
  @HttpCode(HttpStatus.OK)
  async validate(
    @Body() body: any,
    @getCurrentUser() currentUser: CurrentUser,
    @getUserLanguage() userLanguage: UserLanguage,
  ) {
    return this.secureMailService.validate(body, currentUser, userLanguage);
  }

  @Post('submit')
  async submit(
    @Body() body: any,
    @getCurrentUser() currentUser: CurrentUser,
    @getUserLanguage() userLanguage: UserLanguage,
  ) {
    return this.secureMailService.submit(body, currentUser, userLanguage);
  }
}
