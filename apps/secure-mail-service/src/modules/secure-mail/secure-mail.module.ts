import { Module } from '@nestjs/common';
import { SecureMailController } from '@/modules/secure-mail/secure-mail.controller';
import { SecureMailService } from '@/modules/secure-mail/secure-mail.service';
import { SecureMailDispatcherService } from './services/dispatcher.service';
import { CpsChargeBackService } from './services/cps-charge-back.service';
import { AddNewUserService } from './services/add-user.service';
import { DeleteUserService } from './services/delete-user.service';
import { StopPaymentsService } from './services/stop-payments.service';
import { ChangeUserPrivilegeService } from './services/change-user-privilege.service';
import { CancelSecureMailService } from './services/cancel-securemail.service';
import { AddNewUserPrivilegeService } from './services/add-user-permission.service';
import { AssignNewProductService } from './services/assign-new-product.service';
import { CancelBankDraftService } from './services/cancel-bank-draft.service';
import { CpsAmendServiceService } from './services/cps-amend-service.service';
import { SupportDocsService } from './services/support-docs.service';
import { CpsAddAmendService } from './services/cps-add-amend.service';
import { AmendUserDataService } from './services/amend-user-data.service';
import { RequestBankDraftService } from './services/request-bank-draft.service';
import { OtherPortalsRelatedRequestService } from './services/other-portals-related-request.service';
import { OpenNewAccountService } from './services/open-new-account.service';

import { ManageDirectDisputeService } from './services/manage-direct-debit.service';
import { DisputeCreditCardService } from './services/dispute-credit-card.service';
import { RedeemTermDepositService } from './services/redeem-term-deposit.service';
@Module({
  providers: [
    SecureMailService,
    SecureMailDispatcherService,

    CpsChargeBackService,
    AddNewUserService,

    DeleteUserService,
    StopPaymentsService,
    AddNewUserPrivilegeService,
    ChangeUserPrivilegeService,
    CancelSecureMailService,

    AssignNewProductService,
    CancelBankDraftService,
    CpsAmendServiceService,
    SupportDocsService,
    CpsAddAmendService,

    AmendUserDataService,
    RequestBankDraftService,
    OtherPortalsRelatedRequestService,
    OpenNewAccountService,

    DisputeCreditCardService,
    ManageDirectDisputeService,

    RedeemTermDepositService,
  ],
  controllers: [SecureMailController],
})
export class SecureMailModule {}
