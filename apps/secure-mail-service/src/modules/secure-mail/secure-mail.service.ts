import { Injectable, Logger } from '@nestjs/common';
import {
  FccClientService,
  I18nLookupService,
  RedisTnxService,
  TransactionDetailsFormatterService,
  TransactionListFormatterService,
  TransactionStatusService,
} from '@modules';
import { CurrentUser, TransactionsListResponse } from '@types';
import {
  ErrorCodeEnum,
  FccRequestsEnum,
  ProductCodeEnum,
  SubTransactionStatusCodeEnum,
  UserLanguage,
} from '@enums';
import { v4 as UUIDV4 } from 'uuid';
import {
  getSecureMailReauthInputs,
  getTransactionStatusByCode,
  getTransactionSubStatusByCode,
} from '@helpers';
import { SecureMailDispatcherService } from './services/dispatcher.service';
import { SecureMailBaseDto, SecureMailInitiateDto } from './dtos/base.dto';
import {
  FccGenericTnxResponseDto,
  FccSEGenericTnxResponseDto,
  TransactionJourneyResponseDto,
  TransactionsListQueryDto,
} from '@dtos';
import { groupBy, last } from 'lodash';
import { BusinessValidationException } from '@exceptions';

@Injectable()
export class SecureMailService {
  private readonly logger = new Logger(SecureMailService.name);

  constructor(
    private readonly clientService: FccClientService,
    private readonly tnxService: RedisTnxService,
    private readonly i18n: I18nLookupService,

    private readonly dispatcher: SecureMailDispatcherService,
    private readonly statusService: TransactionStatusService,

    readonly detailsFormatterService: TransactionDetailsFormatterService,
    readonly listFormatterService: TransactionListFormatterService,
  ) {}

  async list(
    requestId: FccRequestsEnum,
    query: TransactionsListQueryDto,
    currentUser: CurrentUser,
    userLanguage: UserLanguage,
  ) {
    const { rowDetails = [] } =
      await this.clientService.fccRequest<TransactionsListResponse>({
        requestId,
        payload: query,
        user: currentUser,
      });

    return this.listFormatterService.formatMany(rowDetails, userLanguage);
  }

  async getDetails(
    refId: string,
    user: CurrentUser,
    userLanguage: UserLanguage,
  ) {
    const { items = [] } =
      await this.clientService.fccRequest<TransactionJourneyResponseDto>({
        requestId: FccRequestsEnum.TRANSACTION_JOURNEY_DETAILS,
        payload: { refId },
        user,
      });

    const results = {
      details: {},
      journey: [],
      history: {},
    };

    if (items.length > 0) {
      const { validationSteps } = items[0];

      if (validationSteps.length > 0) {
        results.history = groupBy(validationSteps, (item) =>
          item?.role?.toLowerCase(),
        );
      }

      const tnxId = last(items).tnxId;

      const details =
        await this.clientService.fccRequest<FccGenericTnxResponseDto>({
          requestId: FccRequestsEnum.TRANSACTION_DETAILS,
          payload: {
            eventId: tnxId,
            productCode: ProductCodeEnum.SecureEmail,
          },
          user,
        });

      results.details = this.detailsFormatterService.format(
        details,
        ProductCodeEnum.SecureEmail,
        userLanguage,
      );
    }

    results.journey = items.map((tnxObj) => {
      const { tnxId, tnxStatCode, prodStatCode, boReleaseDttm = '' } = tnxObj;
      return {
        tnxId,
        status: this.statusService.getTranslatedStatusByProduct(
          ProductCodeEnum.SecureEmail,
          userLanguage,
          tnxStatCode,
          SubTransactionStatusCodeEnum.Unknown,
          prodStatCode,
        ),
        date: boReleaseDttm,
      };
    });

    return results;
  }

  async initiate(
    body: SecureMailInitiateDto,
    user: CurrentUser,
    userLanguage: UserLanguage,
  ) {
    if (!body.type) {
      throw new BusinessValidationException(
        ErrorCodeEnum.NotFound,
        userLanguage,
      );
    }

    const idempotencyKey = UUIDV4();
    const secureMailTxRecord =
      await this.clientService.call<FccSEGenericTnxResponseDto>({
        requestId: FccRequestsEnum.SECURE_MAIL_INITIATE,
        user,
        options: {
          idempotencyKey,
        },
      });

    await this.tnxService.setTnx(user, 'SECURE_MAIL', {
      ...secureMailTxRecord,
      idempotencyKey,
    });

    let initiateResults = {};

    if (body.type) {
      const service = this.dispatcher.getService(body.type);
      initiateResults = await service.initiate?.(body, user, userLanguage);
    }

    return {
      refId: secureMailTxRecord.refId,
      tnxId: secureMailTxRecord.tnxId,
      reauth: getSecureMailReauthInputs(secureMailTxRecord),
      ...initiateResults,
    };
  }

  async validate(
    body: SecureMailBaseDto,
    user: CurrentUser,
    userLanguage: UserLanguage,
  ) {
    const service = this.dispatcher.getService(body.type);
    return service.validateRequest(body, user, userLanguage);
  }

  async submit(
    body: SecureMailBaseDto,
    user: CurrentUser,
    userLanguage: UserLanguage,
  ) {
    const service = this.dispatcher.getService(body.type);

    await service.validateRequest(body, user, userLanguage);

    const transformedBody = service.transformBody
      ? await service.transformBody(body, user, userLanguage)
      : body;
    const payload = service.getPayload(transformedBody);

    this.logger.debug(payload);

    const { idempotencyKey, tnxId, refId } = await this.tnxService.getTnx<any>(
      user,
      'SECURE_MAIL',
    );
    const transaction =
      await this.clientService.call<FccSEGenericTnxResponseDto>({
        requestId: FccRequestsEnum.SECURE_MAIL_SUBMIT,
        payload: {
          tnxId,
          refId,
          ...payload,
        },
        user,
        options: {
          idempotencyKey,
        },
      });
    const status = getTransactionStatusByCode(transaction.tnxStatCode);
    const subStatus = getTransactionSubStatusByCode(transaction.subTnxStatCode);

    return {
      refId: transaction.refId,
      tnxId: transaction.tnxId,
      status,
      subStatus,
      message: this.i18n.translate(
        `secureMail.submit.${subStatus}`,
        userLanguage,
      ),
      details: this.detailsFormatterService.format(
        transaction,
        ProductCodeEnum.SecureEmail,
        userLanguage,
      ),
    };
  }
}
