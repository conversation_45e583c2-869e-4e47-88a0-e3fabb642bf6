import { Module } from '@nestjs/common';
import { ProductsController } from './products.controller';
import { DepositsProductService } from './services/deposits.service';
import { CreditCardsProductService } from './services/credit-cards.service';
import { AccountsProductService } from './services/accounts.service';
import { LoansProductService } from './services/loans.service';

@Module({
  imports: [],
  controllers: [ProductsController],
  providers: [
    DepositsProductService,
    CreditCardsProductService,
    AccountsProductService,
    LoansProductService,
  ],
})
export class ProductsModule {}
