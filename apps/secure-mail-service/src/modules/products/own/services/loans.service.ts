import { EsbAccountListItemDto, EsbLoanListItemDto } from '@dtos';
import { EsbProductTypeEnum, EsbRequestsEnum, UserLanguage } from '@enums';
import { ApiAdapterClientService } from '@modules';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { CurrentUser } from '@types';

@Injectable()
export class LoansProductService {
  constructor(
    private readonly clientService: ApiAdapterClientService,
    private readonly config: ConfigService,
  ) {}

  async getList(currentUser: CurrentUser, language: UserLanguage) {
    const list = await this.clientService
      .esbRequest<EsbLoanListItemDto[]>({
        requestId: EsbRequestsEnum.LOANS_LIST,
        user: currentUser,
        payload: {
          userId: currentUser.cif,
        },
        options: {
          language,
        },
      })
      .catch(() => [] as EsbLoanListItemDto[]);

    return list.map((loan) => this.format(loan));
  }

  format(loan: EsbLoanListItemDto) {
    return {
      title: `${loan?.amount?.amt} ${loan?.amount?.curCode} ${loan.tenor}`,
      value: loan.productIdent,
    };
  }
}
