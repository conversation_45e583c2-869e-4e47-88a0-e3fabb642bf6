import { EsbCreditCardListItemDto, EsbDepositListItemDto } from '@dtos';
import { UserLanguage, EsbRequestsEnum, EsbProductTypeEnum } from '@enums';
import {
  ApiAdapterClientService,
  CreditCardDetailsService as CardDetailsService,
} from '@modules';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { CurrentUser } from '@types';

@Injectable()
export class CreditCardsProductService {
  constructor(
    private readonly clientService: ApiAdapterClientService,
    private readonly config: ConfigService,
  ) {}

  async getList(currentUser: CurrentUser, language: UserLanguage) {
    const list = await this.clientService
      .esbRequest<EsbCreditCardListItemDto[]>({
        requestId: EsbRequestsEnum.CREDIT_CARD_LIST,
        user: currentUser,
        payload: {
          userId: currentUser.cif,
        },
        options: {
          language,
        },
      })
      .catch(() => [] as EsbCreditCardListItemDto[]);

    return list.map((card) => this.format(card));
  }

  format(card: EsbCreditCardListItemDto) {
    return {
      title: `${card.maskedCardNumber}`,
      value: card.maskedCardNumber,
    };
  }
}
