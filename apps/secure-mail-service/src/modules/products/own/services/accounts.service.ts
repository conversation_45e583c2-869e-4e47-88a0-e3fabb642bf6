import { EsbAccountListItemDto } from '@dtos';
import { EsbProductTypeEnum, EsbRequestsEnum, UserLanguage } from '@enums';
import { ApiAdapterClientService } from '@modules';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { CurrentUser } from '@types';

@Injectable()
export class AccountsProductService {
  constructor(
    private readonly clientService: ApiAdapterClientService,
    private readonly config: ConfigService,
  ) {}

  async getList(currentUser: CurrentUser, language: UserLanguage) {
    const list = await this.clientService
      .esbRequest<EsbAccountListItemDto[]>({
        requestId: EsbRequestsEnum.ACCOUNTS_LIST,
        user: currentUser,
        payload: {
          userId: currentUser.cif,
        },
        options: {
          language,
        },
      })
      .catch(() => [] as EsbAccountListItemDto[]);

    return list.map((account) => this.format(account));
  }

  format(account: EsbAccountListItemDto) {
    return {
      title: `${account.accountNumber} ${account.curCode}`,
      value: account.accountNumber,
    };
  }
}
