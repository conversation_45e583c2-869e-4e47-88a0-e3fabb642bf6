import { EsbDepositListItemDto } from '@dtos';
import { EsbProductTypeEnum, EsbRequestsEnum, UserLanguage } from '@enums';
import { ApiAdapterClientService } from '@modules';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { CurrentUser } from '@types';

@Injectable()
export class DepositsProductService {
  constructor(
    private readonly clientService: ApiAdapterClientService,
    private readonly config: ConfigService,
  ) {}

  async getList(currentUser: CurrentUser, language: UserLanguage) {
    const list = await this.clientService
      .esbRequest<EsbDepositListItemDto[]>({
        requestId: EsbRequestsEnum.DEPOSIT_LIST,
        user: currentUser,
        payload: {
          userId: currentUser.cif,
        },
        options: {
          language,
        },
      })
      .catch(() => [] as EsbDepositListItemDto[]);

    return list.map((deposit) => this.format(deposit));
  }

  format(deposit: EsbDepositListItemDto) {
    return {
      title: `${deposit.productIdent} ${deposit.amt.amount} ${deposit.amt.currencyCode}`,
      value: deposit.productIdent,
    };
  }
}
