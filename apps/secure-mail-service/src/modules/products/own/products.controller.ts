import { Controller, Get, UseGuards } from '@nestjs/common';
import { getCurrentUser, getUserLanguage, JwtAuthGuard } from '@modules';
import { CurrentUser } from '@types';
import { UserLanguage } from '@enums';
import { DepositsProductService } from './services/deposits.service';
import { CreditCardsProductService } from './services/credit-cards.service';
import { AccountsProductService } from './services/accounts.service';
import { LoansProductService } from './services/loans.service';

@Controller('products/own')
@UseGuards(JwtAuthGuard)
export class ProductsController {
  constructor(
    private readonly depositsService: DepositsProductService,
    private readonly creditCardsService: CreditCardsProductService,
    private readonly accountsService: AccountsProductService,
    private readonly loansService: LoansProductService,
  ) {}

  @Get('accounts')
  async getAccountsList(
    @getCurrentUser() currentUser: CurrentUser,
    @getUserLanguage() userLanguage: UserLanguage,
  ) {
    return this.accountsService.getList(currentUser, userLanguage);
  }

  @Get('loans')
  async getLoansList(
    @getCurrentUser() currentUser: CurrentUser,
    @getUserLanguage() userLanguage: UserLanguage,
  ) {
    return this.loansService.getList(currentUser, userLanguage);
  }

  @Get('deposits')
  async getDepositsList(
    @getCurrentUser() currentUser: CurrentUser,
    @getUserLanguage() userLanguage: UserLanguage,
  ) {
    return this.depositsService.getList(currentUser, userLanguage);
  }

  @Get('credit-cards')
  async getCreditCardsList(
    @getCurrentUser() currentUser: CurrentUser,
    @getUserLanguage() userLanguage: UserLanguage,
  ) {
    return this.creditCardsService.getList(currentUser, userLanguage);
  }
}
