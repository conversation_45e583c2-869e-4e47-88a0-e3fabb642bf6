import { Controller, Post, Get, UseGuards, Body } from '@nestjs/common';
import { ValidateBankSwiftDto } from './static-data.dtos';
import { StaticDataService } from './static-data.service';
import { CurrentUser } from '@types';
import { getCurrentUser, JwtAuthGuard } from '@modules';
/**
 * Controller for static data endpoints.
 */
@Controller('static-data')
@UseGuards(JwtAuthGuard)
export class StaticDataController {
  constructor(readonly staticDataService: StaticDataService) {}

  /**
   * GET /static-data/countries
   * Accepts optional validated query params and returns country list.
   */
  @Get('country-list')
  async getCountries(@getCurrentUser() currentUser: CurrentUser) {
    return this.staticDataService.getCountryList(currentUser);
  }

  /**
   * Get /static-data/currencies
   * No request body needed as the FCC endpoint works with an empty payload.
   * Returns currency list.
   */
  @Get('currency-list')
  async getCurrencies(@getCurrentUser() currentUser: CurrentUser) {
    return this.staticDataService.getCurrencyList(currentUser);
  }

  @Post('swift/validate')
  async validateBankSwift(
    @Body() { swiftCode }: ValidateBankSwiftDto,
    @getCurrentUser() currentUser: CurrentUser,
  ) {
    return this.staticDataService.getBankSwiftDetails(swiftCode, currentUser);
  }

  @Get('banks-list')
  async getBanksList(@getCurrentUser() currentUser: CurrentUser) {
    return this.staticDataService.getBanksList(currentUser);
  }

  @Get('users-list')
  getUsersList(@getCurrentUser() currentUser: CurrentUser) {
    return this.staticDataService.getUsersList(currentUser);
  }
}
