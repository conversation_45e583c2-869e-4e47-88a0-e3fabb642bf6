import { ApiAdapterClientService } from '@modules';
import { CurrentUser } from '@types';
import { FccRequestsEnum } from '@enums';
import { Injectable } from '@nestjs/common';
import { FccClientService } from '@modules';

@Injectable()
export class StaticDataService {
  constructor(
    readonly apiAdapterService: ApiAdapterClientService,
    private readonly clientService: FccClientService,
  ) {}

  /**
   * Retrieves the list of countries.
   * @param params - Validated query params DTO.
   */
  async getCountryList(user: CurrentUser) {
    const countries = await this.apiAdapterService.fccRequest<any>({
      requestId: FccRequestsEnum.COUNTRY_LIST,
      payload: {},
      user,
    });
    return countries;
  }

  /**
   * Retrieves the list of currencies.
   * Payload is an empty object since the FCC endpoint works with an empty payload.
   */
  async getCurrencyList(user: CurrentUser) {
    const response = await this.apiAdapterService.fccRequest({
      requestId: FccRequestsEnum.CURRENCY_LIST,
      payload: {},
      user,
    });
    return response;
  }

  async getBankSwiftDetails(swiftCode: string, user: CurrentUser) {
    const response = await this.apiAdapterService.fccRequest({
      requestId: FccRequestsEnum.BANKS_SWIFT_DETAILS,
      payload: {
        bicCode: swiftCode,
      },
      user,
    });
    return response;
  }

  async getBanksList(user: CurrentUser) {
    const response = await this.apiAdapterService.fccRequest({
      requestId: FccRequestsEnum.BANKS_LIST,
      payload: {},
      user,
    });
    return response;
  }

  async getUsersList(user: CurrentUser): Promise<any> {
    const usersList = await this.clientService.call<any>({
      requestId: FccRequestsEnum.USERS_LIST,
      user,
    });

    return usersList;
  }
}
