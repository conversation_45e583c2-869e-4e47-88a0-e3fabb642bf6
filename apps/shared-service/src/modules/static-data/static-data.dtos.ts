import { CurrencyEnum } from '@enums';
import { Type } from 'class-transformer';
import {
  IsOptional,
  IsNumber,
  Min,
  Max,
  IsString,
  IsNotEmpty,
} from 'class-validator';

/**
 * DTO for Country List query parameters.
 * All fields are optional and validated as per FCC query parameters.
 */
export class CountryListRequestDto {
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(1000)
  limit?: number;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  @Max(999)
  offset?: number;
}

export class ValidateBankSwiftDto {
  @IsNotEmpty()
  @IsString()
  swiftCode: string;
}
