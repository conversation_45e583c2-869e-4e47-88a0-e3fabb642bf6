import {
  Body,
  Controller,
  Get,
  Headers,
  HttpCode,
  HttpStatus,
  Post,
  Res,
} from '@nestjs/common';
import { AppConfigService } from './config.service';
import { AppConfigFacts } from '@types';

@Controller('app-config')
export class AppConfigController {
  constructor(private appConfigService: AppConfigService) {}

  @Post()
  @HttpCode(HttpStatus.OK)
  async buildAppConfig(
    @Res({ passthrough: true }) reply,
    @Headers('If-None-Match') hash: string,
    @Body() facts: AppConfigFacts,
  ) {
    const { data, etag } = await this.appConfigService.buildAppConfig(facts);

    reply.header('Access-Control-Expose-Headers', 'ETag');
    reply.header('ETag', etag);

    if (etag && hash === etag) {
      reply.status(304);
      return;
    }

    return data;
  }
}
