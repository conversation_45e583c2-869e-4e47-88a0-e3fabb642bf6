import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Engine } from 'json-rules-engine';
import {
  AppConfigConditionObject,
  AppConfigFacts,
  AppConfigParameters,
} from '@types';
import { LookupsRemoteDataService } from '@modules';

@Injectable()
export class AppConfigService {
  constructor(
    private readonly configService: ConfigService,
    private readonly lookupsDataService: LookupsRemoteDataService,
  ) {}

  async loadAppConfig() {
    const {
      data: { AppDynamicConfig },
    } = await this.lookupsDataService.loadRawData('AppDynamicConfig');

    return AppDynamicConfig;
  }

  async calculateConditions(
    conditions: AppConfigConditionObject[],
    facts: AppConfigFacts,
  ) {
    if (conditions?.length === 0) {
      return {};
    }
    const engine = new Engine(conditions, {
      allowUndefinedFacts: true,
    });
    return engine.run(facts).then((results) =>
      results.events?.reduce((acc, item) => {
        acc[item.type] = true;
        return acc;
      }, {}),
    );
  }

  evaluateParameters(
    params: AppConfigParameters,
    conditions: Record<string, boolean>,
  ): Record<string, any> {
    const evaluatedParams: Record<string, any> = {};

    for (const key in params) {
      const param = params[key];
      let valueSet = false;

      if (param.conditionalValues) {
        for (const conditionKey in param.conditionalValues) {
          if (conditions[conditionKey] !== undefined) {
            if (conditions[conditionKey] === true) {
              evaluatedParams[key] =
                param.conditionalValues[conditionKey].value;
              valueSet = true;
              break;
            }
          }
        }
      }

      if (!valueSet) {
        evaluatedParams[key] = param.defaultValue.value;
      }
    }

    return evaluatedParams;
  }
  /*
    this should be cached in redis based on the facts + config version combination in order to avoid unnecessary calculations
  */
  async buildAppConfig(facts: AppConfigFacts) {
    const { conditions, parameters } = await this.loadAppConfig();
    const results = await this.calculateConditions(conditions, facts);

    return {
      etag: '',
      data: this.evaluateParameters(parameters, results),
    };
  }
}
