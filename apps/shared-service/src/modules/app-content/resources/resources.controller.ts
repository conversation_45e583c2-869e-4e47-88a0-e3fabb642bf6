import { UserLanguage } from '@enums';
import { getUserLanguage, LazyResourcesLookupService } from '@modules';
import { Controller, Get, Param } from '@nestjs/common';

@Controller('resources')
export class AppResourcesController {
  constructor(private resourcesService: LazyResourcesLookupService) {}

  @Get('/:identifier')
  async getResources(
    @Param('identifier') identifier: string,
    @getUserLanguage() userLanguage: UserLanguage,
  ) {
    const resource = await this.resourcesService.load(
      `Resource-${identifier}`,
      userLanguage,
    );
    return resource.getAll();
  }
}
