import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { LookupsRemoteDataService } from '@modules';

@Injectable()
export class AppI18nService {
  constructor(
    private readonly configService: ConfigService,
    private readonly lookupsDataService: LookupsRemoteDataService,
  ) {}

  async loadAppConfig() {
    const {
      data: { I18nClient },
      headers: { etag },
    } = await this.lookupsDataService.loadRawData('I18nClient');

    return { json: I18nClient, etag };
  }
}
