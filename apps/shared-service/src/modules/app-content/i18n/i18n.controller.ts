import { <PERSON>, Get, Head<PERSON>, <PERSON><PERSON> } from '@nestjs/common';
import { AppI18nService } from './i18n.service';

@Controller('i18n')
export class AppI18nController {
  constructor(private appI18nService: AppI18nService) {}

  @Get()
  async getTranslations(
    @Res({ passthrough: true }) reply,
    @Headers('If-None-Match') hash: string,
  ) {
    const { json, etag } = await this.appI18nService.loadAppConfig();

    reply.header('Access-Control-Expose-Headers', 'ETag');
    reply.header('ETag', etag);

    if (etag && hash === etag) {
      reply.status(304);
      return;
    }

    return json;
  }
}
