import { Controller, Get, Query } from '@nestjs/common';
import { BranchesService } from './branches.service';
import { UserLanguage } from '@enums';
import { getUserLanguage } from '@modules';
import { GetBranchsQueryDto } from './branches.dtos';

@Controller('cib-branches')
export class CibBranchesController {
  constructor(private branchesService: BranchesService) {}

  @Get('/list')
  async getBranches(
    @Query() query: GetBranchsQueryDto,
    @getUserLanguage() language: UserLanguage,
  ) {
    return this.branchesService.getNearbyBranches(query, language);
  }

  @Get('/governates')
  async getBranchesGovernates(@getUserLanguage() language: UserLanguage) {
    return this.branchesService.getGovernates(language);
  }
}
