import { UserLanguage } from '@enums';
import { LazyBranchesLookupService, LookupEntity } from '@modules';
import { Injectable } from '@nestjs/common';
import { GetBranchsQueryDto } from './branches.dtos';

@Injectable()
export class BranchesService {
  constructor(private branchesService: LazyBranchesLookupService) {}

  async getNearbyBranches(query: GetBranchsQueryDto, language: UserLanguage) {
    let branches: Record<string, any>[] = await this.branchesService.load(
      LookupEntity.BANK_BRANCH,
      language,
    );

    if (query.governate && query.governate !== '') {
      branches = branches.filter(
        (branch) => branch.governate == query.governate,
      );
    }

    if (query.lat && query.lng) {
      const userLat = parseFloat(query.lat);
      const userLng = parseFloat(query.lng);
      return branches
        .map((branch) => ({
          ...branch,
          distance: this.getDistance(userLat, userLng, branch.lat, branch.lng),
        }))
        .sort((a, b) => a.distance! - b.distance!);
    }
    return branches;
  }

  async getGovernates(language: UserLanguage) {
    return await this.branchesService.load(
      LookupEntity.BANK_BRANCH_GOVERNATE,
      language,
    );
  }

  private getDistance(
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number,
  ): number {
    const R = 6371;
    const dLat = (lat2 - lat1) * (Math.PI / 180);
    const dLon = (lon2 - lon1) * (Math.PI / 180);
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(lat1 * (Math.PI / 180)) *
        Math.cos(lat2 * (Math.PI / 180)) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }
}
