import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { AppConfigService } from './config/config.service';
import { AppConfigController } from './config/config.controller';
import { AppI18nController } from './i18n/i18n.controller';
import { AppI18nService } from './i18n/i18n.service';
import { AppResourcesController } from './resources/resources.controller';
import { CibBranchesController } from './cib-branches/branches.controller';
import { BranchesService } from './cib-branches/branches.service';

@Module({
  imports: [],
  controllers: [
    AppConfigController,
    AppI18nController,
    AppResourcesController,
    CibBranchesController,
  ],
  providers: [AppConfigService, AppI18nService, BranchesService, Logger],
})
export class AppContentModule {}
