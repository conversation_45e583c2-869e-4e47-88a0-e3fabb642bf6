import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import { AuthTypeService } from '@/modules/auth-type/auth-type.service';
import { AuthTypeRequestDto } from '@/modules/auth-type/dto/auth.request.dto';
import { getCurrentUser, JwtAuthGuard } from '@modules';
import { CurrentUser } from '@types';

@Controller('auth-type')
@UseGuards(JwtAuthGuard)
export class AuthTypeController {
  constructor(private readonly authTypeService: AuthTypeService) {}

  @Post('/')
  getAuthType(
    @Body() authTypeRequestDto: AuthTypeRequestDto,
    @getCurrentUser() currentUser: CurrentUser,
  ) {
    return this.authTypeService.getAuthType(authTypeRequestDto, currentUser);
  }
}
