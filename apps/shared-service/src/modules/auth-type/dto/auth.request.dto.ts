import { IsOptional } from 'class-validator';

export class AuthTypeRequestDto {
  @IsOptional()
  productCode: string;

  @IsOptional()
  subProductCode: string;

  //@IsOptional()
  //bankName: string;

  @IsOptional()
  reauthOperation: string;

  @IsOptional()
  reauthScreenXML: string;

  @IsOptional()
  operation: string;

  @IsOptional()
  tnxTypeCode: string;

  @IsOptional()
  entity: string;

  @IsOptional()
  currency: string;

  @IsOptional()
  amount: string;

  @IsOptional()
  bank_abbv_name: string;

  @IsOptional()
  mode: string;
  
}
