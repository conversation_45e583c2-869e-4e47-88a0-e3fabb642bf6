import { ApiAdapterClientService } from '@modules';
import { Injectable } from '@nestjs/common';
import { AuthTypeRequestDto } from '@/modules/auth-type/dto/auth.request.dto';
import { CurrentUser } from '@types';
import { FccRequestsEnum } from '@enums';

@Injectable()
export class AuthTypeService {
  constructor(private readonly apiAdapterService: ApiAdapterClientService) {}

  async getAuthType(payload: AuthTypeRequestDto, user: CurrentUser) {
    const response = await this.apiAdapterService.fccRequest({
      requestId: FccRequestsEnum.GENERIC_AUTH_TYPE,
      version: 'v1',
      payload,
      user,
    });

    switch (response) {
      case '01':
        return {
          type: 'NO_AUTH_TYPE',
          timestamp: new Date().toISOString(),
        };
      case 'OTP':
        return {
          type: 'OTP_AUTH_TYPE',
          timestamp: new Date().toISOString(),
        };
      default:
        return {
          type: 'UNAUTHORIZED',
          timestamp: new Date().toISOString(),
        };
    }
  }
}
