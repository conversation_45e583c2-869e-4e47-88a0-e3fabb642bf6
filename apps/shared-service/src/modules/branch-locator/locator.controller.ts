import { Controller, Get, Param, Query } from '@nestjs/common';
import { getUserLanguage } from '@modules';
import { UserLanguage } from '@enums';
import { BranchLocatorService } from './locator.service';
import { GetBranchsLocationsDto } from '@dtos';

@Controller('branch-locator')
export class BranchLocatorController {
  constructor(private readonly locatorService: BranchLocatorService) {}

  @Get('/locations')
  async getLocations(
    @Query() query: GetBranchsLocationsDto,
    @getUserLanguage() language: UserLanguage,
  ): Promise<any> {
    return await this.locatorService.getLocations(query, language);
  }

  @Get('/locations/:id')
  async getLocationDetails(
    @Param('id') id: string,
    @getUserLanguage() language: UserLanguage,
  ): Promise<any> {
    return await this.locatorService.getLocationDetails(id, language);
  }
}
