import {
  Body,
  Controller,
  Post,
  UseGuards,
  Delete,
  Param,
  Get,
} from '@nestjs/common';
import { getCurrentUser, JwtAuthGuard } from '@modules';
import { CurrentUser } from '@types';
import { UploadAttachmentDto } from '@dtos';
import { DocumentsService } from './documents.service';

@Controller('documents')
@UseGuards(JwtAuthGuard)
export class DocumentsController {
  constructor(private readonly documentService: DocumentsService) {}

  @Post('upload')
  async uploadDocument(
    @Body() uploadAttachmentDto: UploadAttachmentDto,
    @getCurrentUser() currentUser: CurrentUser,
  ) {
    return this.documentService.upload(uploadAttachmentDto, currentUser);
  }

  @Delete(':documentId')
  async deleteDocument(
    @getCurrentUser() currentUser: CurrentUser,
    @Param('documentId') documentId: string,
  ) {
    return this.documentService.delete(documentId, currentUser);
  }

  @Get(':tnxId')
  async retrieveDocuments(
    @getCurrentUser() currentUser: CurrentUser,
    @Param('tnxId') tnxId: string,
  ) {
    return this.documentService.list(tnxId, currentUser);
  }

  @Get('download/:attId')
  async downloadDocuments(
    @getCurrentUser() currentUser: CurrentUser,
    @Param('attId') attId: string,
  ) {
    return this.documentService.download(attId, currentUser);
  }
}
