import { Injectable, Logger } from '@nestjs/common';
import { ApiAdapterClientService, RedisAttachmentsService } from '@modules';
import { CurrentUser } from '@types';
import { FccRequestsEnum } from '@enums';
import { UploadAttachmentDto } from '@dtos';

@Injectable()
export class DocumentsService {
  private readonly logger = new Logger(DocumentsService.name);

  constructor(
    private readonly apiAdapterClientService: ApiAdapterClientService,
    private readonly redisAttachmentsService: RedisAttachmentsService,
  ) {}

  async upload(attachment: UploadAttachmentDto, user: CurrentUser) {
    this.logger.debug(
      `uploading document ${attachment.fileName} / ${attachment.refId} / ${attachment.tnxId}`,
    );

    const { id, size, type } = await this.apiAdapterClientService.fccRequest<{
      id: string;
      size: string;
      type: string;
    }>({
      requestId: FccRequestsEnum.UPLOAD_DOCUMENT,
      payload: attachment,
      user,
    });

    return {
      id,
      size: `${size}`,
      type,
      //to be removed (to unblock the FE team)
      fileName: attachment?.fileName,
      name: attachment?.fileName,
      title: attachment?.fileTitle,
      status: 'SUCCESS',
      response: 'success',
      refId: attachment?.refId,
      tnxId: attachment?.tnxId,
    };
  }

  async delete(documentId: string, user: CurrentUser) {
    return this.apiAdapterClientService.fccRequest({
      requestId: FccRequestsEnum.DELETE_ATTACHMENT,
      payload: {
        documentId,
      },
      user,
    });
  }

  async list(tnxId: string, user: CurrentUser) {
    return this.apiAdapterClientService.fccRequest({
      requestId: FccRequestsEnum.GET_ATTACHMENT_LIST,
      payload: { eventId: tnxId },
      user,
    });
  }

  async download(attId: string, user: CurrentUser) {
    const response = await this.apiAdapterClientService.fccRequest({
      requestId: FccRequestsEnum.DOWNLOAD_ATTACHMENT,
      payload: { attId },
      user,
    });

    this.logger.debug(`Downloading document with attachment ID: ${attId}`);

    return response;
  }
}
