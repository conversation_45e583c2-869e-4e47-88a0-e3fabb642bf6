import { Injectable, Logger } from '@nestjs/common';
import { ApiAdapterClientService, FxService } from '@modules';
import { CurrentUser } from '@types';
import { FccRequestsEnum, UserLanguage } from '@enums';
import { FxRateRequestDto } from './global.dto';
import { CutOffTimeRequestDto } from '@dtos';
import { DateTime } from 'luxon';

@Injectable()
export class GlobalService {
  private readonly logger = new Logger(GlobalService.name);

  constructor(
    private readonly clientService: ApiAdapterClientService,
    private readonly fxService: FxService,
  ) {}

  async validateCutOffTime(
    payload: CutOffTimeRequestDto,
    user: CurrentUser,
    language: UserLanguage,
  ) {
    const response = await this.clientService.fccRequest({
      requestId: FccRequestsEnum.CUT_OFF_TIME,
      payload: {
        ...payload,
        date: payload.date ?? DateTime.now().toFormat('dd/MM/yyyy'),
      },
      user,
      options: {
        language,
      },
    });
    return response;
  }

  async getFxRate(
    { fromCurrency, toCurrency }: FxRateRequestDto,
    user: CurrentUser,
  ) {
    const fxConvert = await this.fxService.convert(
      fromCurrency,
      toCurrency,
      user,
    );
    return {
      boardExchangeRate: fxConvert(1),
    };
  }
}
