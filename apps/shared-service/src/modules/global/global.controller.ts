import { Controller, Get, UseGuards, Query, Body, Post } from '@nestjs/common';
import {
  AccountsValidationService,
  getCurrentUser,
  getUserLanguage,
  JwtAuthGuard,
} from '@modules';
import { CurrentUser } from '@types';
import { FxRateRequestDto } from './global.dto';
import { GlobalService } from './global.service';
import { CutOffTimeRequestDto, ValidateIbanDto } from '@dtos';
import { UserLanguage } from '@enums';

@Controller('global')
@UseGuards(JwtAuthGuard)
export class GlobalController {
  constructor(
    private readonly globalService: GlobalService,
    private readonly accountsValidationService: AccountsValidationService,
  ) {}

  @Get('cut-off-time')
  validateCutOffTime(
    @Query() query: CutOffTimeRequestDto,
    @getCurrentUser() currentUser: CurrentUser,
    @getUserLanguage() userLanguage: UserLanguage,
  ) {
    return this.globalService.validateCutOffTime(
      query,
      currentUser,
      userLanguage,
    );
  }

  @Get('fx-rate')
  getFxRate(
    @Query() query: FxRateRequestDto,
    @getCurrentUser() currentUser: CurrentUser,
  ) {
    return this.globalService.getFxRate(query, currentUser);
  }

  @Post('iban/validate')
  async validateIban(@Body() body: ValidateIbanDto) {
    return this.accountsValidationService.validateIban(body);
  }
}
