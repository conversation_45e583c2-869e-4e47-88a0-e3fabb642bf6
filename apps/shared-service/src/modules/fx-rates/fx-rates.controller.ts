import { Controller, Get } from '@nestjs/common';
import { getUserLanguage } from '@modules';
import { UserLanguage } from '@enums';
import { ForexRatesService } from './fx-rates.service';

@Controller('fx-rates')
export class ForexRatesController {
  constructor(private readonly service: ForexRatesService) {}

  @Get('/all')
  async getForexRates(@getUserLanguage() language: UserLanguage): Promise<any> {
    return await this.service.getForexRates(language);
  }
}
