import {
  CurrencyEnum,
  EsbRequestsEnum,
  RequestModuleEnum,
  UserLanguage,
} from '@enums';
import { ApiAdapterClientService, I18nLookupService } from '@modules';
import { Injectable } from '@nestjs/common';
import { ForexRateListRecord } from '@types';

@Injectable()
export class ForexRatesService {
  currencyOrder: CurrencyEnum[] = [
    CurrencyEnum.EGP,
    CurrencyEnum.USD,
    CurrencyEnum.EUR,
    CurrencyEnum.GBP,
    CurrencyEnum.CHF,
    CurrencyEnum.AED,
    CurrencyEnum.SAR,
    CurrencyEnum.KWD,
    CurrencyEnum.AUD,
    CurrencyEnum.CAD,
    CurrencyEnum.CNY,
    CurrencyEnum.DKK,
    CurrencyEnum.JPY,
    CurrencyEnum.KES,
    CurrencyEnum.NOK,
    CurrencyEnum.QAR,
    CurrencyEnum.SEK,
    CurrencyEnum.THB,
    CurrencyEnum.BHD,
    CurrencyEnum.HKD,
    CurrencyEnum.ILS,
    CurrencyEnum.INR,
    CurrencyEnum.JOD,
    CurrencyEnum.KRW,
    CurrencyEnum.LBP,
    CurrencyEnum.LYD,
    CurrencyEnum.OMR,
    CurrencyEnum.PLN,
    CurrencyEnum.SGD,
    CurrencyEnum.SYP,
    CurrencyEnum.UAH,
  ];

  constructor(
    private readonly i18n: I18nLookupService,
    private readonly apiAdapterService: ApiAdapterClientService,
  ) {}

  async getForexRates(language: UserLanguage) {
    const results = await this.apiAdapterService.call<ForexRateListRecord[]>({
      module: RequestModuleEnum.ESB,
      requestId: EsbRequestsEnum.FOREX_RATE,
      payload: {},
      options: {
        language,
      },
    });

    const currencies = [];
    const rates = [];

    const sortedResults = this.sortByCurrencyOrder(results);

    sortedResults.forEach((entry) => {
      for (const [currencyCode, rateInfo] of Object.entries(entry)) {
        const isApplicable =
          rateInfo.Buy > 0.001 &&
          this.i18n.has(`global.CurrencyNames.${currencyCode}`, language);
        if (isApplicable) {
          const name = this.i18n.translate(
            `global.CurrencyNames.${currencyCode}`,
            language,
          );
          const symbol = this.i18n.translate(
            `global.CurrencySymbols.${currencyCode}`,
            language,
          );

          currencies.push({ id: currencyCode, name, symbol });
          rates.push({
            id: currencyCode,
            name,
            symbol,
            spotbuyrate: rateInfo.Buy,
            spotsellrate: rateInfo.Sell,
            spotvaluationrate: rateInfo.Mid,
          });
        }
      }
    });

    return { currencies, rates };
  }

  sortByCurrencyOrder(data: ForexRateListRecord[]): ForexRateListRecord[] {
    return data.sort((a, b) => {
      const [currencyA] = Object.keys(a) as CurrencyEnum[];
      const [currencyB] = Object.keys(b) as CurrencyEnum[];

      const indexA = this.currencyOrder.indexOf(currencyA);
      const indexB = this.currencyOrder.indexOf(currencyB);

      // Put unknown currencies at the end
      return (
        (indexA === -1 ? Infinity : indexA) -
        (indexB === -1 ? Infinity : indexB)
      );
    });
  }
}
