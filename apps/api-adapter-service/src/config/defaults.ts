import * as process from 'node:process';

export default () => ({
  port: parseInt(process.env.PORT, 10) || 3000,
  esbURL: process.env.ESB_URL,
  fccURL: process.env.FCC_URL,
  cibegURL: process.env.CIBEG_URL || 'https://www.cibeg.com',
  publicProxy: {
    host: process.env.PUBLIC_PROXY_HOST || 'cibproxy.hodomain.local',
    port: process.env.PUBLIC_PROXY_PORT || 8080,
  },
  branchLocator: {
    ds: process.env.BRANCHLOCATOR_DS || 'AC6F12511F364A89BF1A2FEC2B419D17',
  },
  estatement: {
    encryptionURL:
      process.env.ESTATEMENT_ENCRYPTION_URL ||
      'http://estatment-uat.hodomain.local:5345/Estatement.asmx/EncryptAcc',
    publicURL:
      process.env.ESTATEMENT_PUBLIC_URL ||
      'https://estatement-test.cibeg.com/Statements/Statements.aspx?CD=',
    username: process.env.ESTATEMENT_USERNAME,
    password: process.env.ESTATEMENT_PASSWORD,
  },
});
