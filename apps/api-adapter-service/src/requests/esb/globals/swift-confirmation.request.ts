import { Request } from '@/adapter/decorators/request.decorator';
import { Injectable } from '@nestjs/common';
import { EsbRequestsEnum, RequestModuleEnum } from '@enums';
import { ClassConstructor, Expose, Transform } from 'class-transformer';
import { IsNotEmpty, IsOptional } from 'class-validator';
import { EsbRequest } from '@/adapter/requests/esb.request';

export class SwiftConfirmationRequestDto {
  @Expose()
  @IsOptional()
  referenceId: string;

  @Expose()
  @IsNotEmpty()
  adviceType: string;
}

@Injectable()
@Request(EsbRequestsEnum.SWIFT_CONFIRMATION, RequestModuleEnum.ESB)
export class SwiftConfirmationRequest extends EsbRequest {
  payloadDto: ClassConstructor<any> = SwiftConfirmationRequestDto;

  SOAPAction: string =
    'http://www.cibeg.com/esb/wsvc/v2.0/remittancetransaction/retrieveremittanceadvice';

  xmlRootPath: string[] = [
    'RetrieveRemittanceAdviceResponse',
    'AdviceInfo',
    'SWIFTMsg',
  ];

  soapTemplate(payload: SwiftConfirmationRequestDto, header: string): string {
    return `<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:head="http://www.cibeg.com/esb/common/v1.0/header">
            ${header}
            <soapenv:Body>
        <rem:RetrieveRemittanceAdviceRequest xmlns:rem="http://www.cibeg.com/esb/financial/services/v2.0/remittancetransaction">
            <TransferId>${payload.referenceId}</TransferId>
            <AdviceType>${payload.adviceType}</AdviceType>
        </rem:RetrieveRemittanceAdviceRequest>
    </soapenv:Body>
   </soapenv:Envelope>`;
  }

  format(data: any) {
    const formatted = super.format(data);
    return {
      swiftMessage: formatted,
    };
  }
}
