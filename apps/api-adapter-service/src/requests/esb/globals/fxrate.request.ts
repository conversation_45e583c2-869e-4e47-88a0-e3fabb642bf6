import { Request } from '@/adapter/decorators/request.decorator';
import { Injectable } from '@nestjs/common';
import { EsbRequest } from '@/adapter/requests/esb.request';
import { ClassConstructor } from 'class-transformer';
import { EsbRequestsEnum, RequestModuleEnum } from '@enums';
import { ForexRatesEsbResponseDto } from '@dtos';

@Injectable()
@Request(EsbRequestsEnum.FOREX_RATE, RequestModuleEnum.ESB)
export class ForexRateRequest extends EsbRequest {
  serviceName: string = '01000310';

  payloadDto: ClassConstructor<any> = null;
  responseDto: ClassConstructor<any> = ForexRatesEsbResponseDto;

  xmlRootPath: string[] = ['RetrieveForExRateSheetResponse'];

  readonly SOAPAction: string =
    'http://www.cibeg.com/esb/wsvc/v5.0/forexrate/retrieveforexrate';

  soapTemplate(payload: any, header: string): string {
    return `<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:head="http://www.cibeg.com/esb/common/v1.0/header" xmlns:for="http://www.cibeg.com/esb/financial/services/v5.0/forexrate">
    ${header}
    <soapenv:Body>
      <for:RetrieveForExRateSheetRequest>
        <CurCode xmlns="">
          <CurCodeValue xmlns="">ALL</CurCodeValue>
        </CurCode>
        <MarketCode xmlns="">11</MarketCode>
      </for:RetrieveForExRateSheetRequest>
    </soapenv:Body>
  </soapenv:Envelope>`;
  }

  format(data: any) {
    const formatted = super.format(data) as ForexRatesEsbResponseDto;

    const ratesMap: Record<string, Record<'Buy' | 'Sell' | 'Mid', number>> = {};

    formatted.rates.forEach((info) => {
      info.forExRateSheetCurRate.forEach((rate) => {
        const currency = rate.newForExRate.contraCurCode.curCodeValue;
        const indicator = rate.buySellIndicator as 'Buy' | 'Sell' | 'Mid';
        const exchRate = rate.newForExRate.exchRate;

        if (!ratesMap[currency]) {
          ratesMap[currency] = { Buy: 0, Sell: 0, Mid: 0 };
        }

        ratesMap[currency][indicator] = exchRate;
      });
    });

    const ratesArray = Object.entries(ratesMap).map(([currency, values]) => ({
      [currency]: values,
    }));

    return ratesArray;
  }
}
