import { Injectable } from '@nestjs/common';
import { EsbRequest } from '@/adapter/requests/esb.request';
import { ClassConstructor } from 'class-transformer';
import { Request } from '@/adapter/decorators/request.decorator';
import { CacheTTL, EsbRequestsEnum, RequestModuleEnum } from '@enums';
import { EsbBankListListItemDto } from '@dtos';

@Injectable()
@Request(EsbRequestsEnum.IPN_BANK_LIST, RequestModuleEnum.ESB)
export class IPNBankListRequest extends EsbRequest {
  serviceName: string = '********';
  payloadDto: ClassConstructor<any> = null;
  xmlRootPath = [
    'RetrieveFinancialInstitutionInformationResponse',
    'BankList',
    'BankInfo',
  ];
  responseDto: ClassConstructor<any> = EsbBankListListItemDto;

  cacheEnabled: boolean = true;
  cacheTtl: number = CacheTTL.OneHour;

  SOAPAction: string =
    'http://www.cibeg.com/esb/wsvc/v6.5/financialinstitution/retrievefinancialinstitution';
  soapTemplate(payload: any, header: string): string {
    return `<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:head="http://www.cibeg.com/esb/common/v1.0/header" xmlns:fin="http://www.cibeg.com/esb/utilities/services/v6.5/financialinstitution">
            ${header}
    <soapenv:Body>
      <fin:RetrieveFinancialInstitutionInformationRequest>
         <BankList>
            <ChannelBankList>IPN</ChannelBankList>
         </BankList>
      </fin:RetrieveFinancialInstitutionInformationRequest>
   </soapenv:Body>
</soapenv:Envelope>`;
  }
}
