import { Request } from '@/adapter/decorators/request.decorator';
import { Injectable } from '@nestjs/common';
import { EsbRequestsEnum, RequestModuleEnum } from '@enums';
import { ClassConstructor, Expose, Transform } from 'class-transformer';
import { IsOptional } from 'class-validator';
import { EsbRequest } from '@/adapter/requests/esb.request';
import { BreakDepositProductFulfillmentDto } from '@dtos';

export class DepositSimulateRedemptionRequestDto {
  @Expose()
  @IsOptional()
  @Transform(({ value }) => value || '0000000000')
  amount: string;

  @Expose()
  @IsOptional()
  depositId: string;
}

@Injectable()
@Request(EsbRequestsEnum.DEPOSIT_SIMULATE_REDEMPTION, RequestModuleEnum.ESB)
export class DepositSimulateRedemptionRequest extends EsbRequest {
  serviceName: string = '01000310';
  payloadDto: ClassConstructor<any> = DepositSimulateRedemptionRequestDto;
  responseDto: ClassConstructor<any> = BreakDepositProductFulfillmentDto;
  opMode = 'V';
  SOAPAction: string =
    'http://www.cibeg.com/esb/wsvc/v5.5/depositproductfulfilment/breakdepositproductfulfilment';
  xmlRootPath = ['BreakDepositProductFulfillmentResponse'];

  soapTemplate(
    payload: DepositSimulateRedemptionRequestDto,
    header: string,
  ): string {
    return `<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:head="http://www.cibeg.com/esb/common/v1.0/header" xmlns:caf="http://www.cibeg.com/esb/financial/services/v5.5/currentaccountfulfilment" xmlns:com="http://www.cibeg.com/esb/financial/objects/v5.5/commontypes" xmlns:par="http://www.cibeg.com/esb/financial/services/v5.5/partyregistration" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
            ${header}
    <soapenv:Body>
    	<ns:BreakDepositProductFulfillmentRequest xmlns:ns="http://www.cibeg.com/esb/financial/services/v5.5/depositproductfulfilment">
    	  <ProductIdent>${payload.depositId}</ProductIdent>
    	  <ExtRef>111111</ExtRef>
    	  <PrincipalIncrease>-${payload.amount}</PrincipalIncrease>
    	  <Controls xmlns="">
    		<IS_GTS>NULL</IS_GTS>
    		<AuthFlag>0</AuthFlag>
    	  </Controls>
    	</ns:BreakDepositProductFulfillmentRequest>
   </soapenv:Body>
</soapenv:Envelope>`;
  }
}
