import { Request } from '@/adapter/decorators/request.decorator';
import { Injectable } from '@nestjs/common';
import { EsbProductTypeEnum, EsbRequestsEnum, RequestModuleEnum } from '@enums';
import { ClassConstructor } from 'class-transformer';
import { EsbDepositListItemDto } from '@dtos';
import { RetrievePartyPositionRequest } from '../common/partyposition.request';

@Injectable()
@Request(EsbRequestsEnum.DEPOSIT_LIST, RequestModuleEnum.ESB)
export class DepositListRequest extends RetrievePartyPositionRequest {
  productType: EsbProductTypeEnum = EsbProductTypeEnum.Deposit;
  serviceName: string = '01000310';
  responseDto: ClassConstructor<any> = EsbDepositListItemDto;
  xmlRootPath = ['RetrievePartyPositionMeasurementResponse', 'DepositList'];
}
