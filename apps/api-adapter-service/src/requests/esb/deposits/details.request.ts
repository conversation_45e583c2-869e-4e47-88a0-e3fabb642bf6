import { Request } from '@/adapter/decorators/request.decorator';
import { Injectable } from '@nestjs/common';
import { EsbDepositTypeEnum, EsbRequestsEnum, RequestModuleEnum } from '@enums';
import { ClassConstructor, Expose, Transform } from 'class-transformer';
import { IsOptional } from 'class-validator';
import { EsbRequest } from '@/adapter/requests/esb.request';
import { EsbDepositDetailsItemDto } from '@dtos';

export class DepositDetailsRequestDto {
  @Expose()
  @IsOptional()
  @Transform(({ value }) => value || '0000000000')
  depositNumber: string;

  @Expose()
  @IsOptional()
  @Transform(({ value }) => value || EsbDepositTypeEnum.TD)
  depositType: EsbDepositTypeEnum;
}

@Injectable()
@Request(EsbRequestsEnum.DEPOSIT_DETAILS, RequestModuleEnum.ESB)
export class DepositDetailsRequest extends EsbRequest {
  serviceName: string = '01000310';
  payloadDto: ClassConstructor<any> = DepositDetailsRequestDto;
  responseDto: ClassConstructor<any> = EsbDepositDetailsItemDto;

  SOAPAction: string =
    'http://www.cibeg.com/esb/wsvc/v6.5/depositproductfulfilment/retrievedepositproductfulfilment';
  xmlRootPath = ['RetrieveDepositProductFulfilmentResponse'];

  soapTemplate(payload: DepositDetailsRequestDto, header: string): string {
    return `<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:head="http://www.cibeg.com/esb/common/v1.0/header" xmlns:dep="http://www.cibeg.com/esb/financial/services/v6.5/depositproductfulfilment" xmlns:com="http://www.cibeg.com/esb/financial/objects/v6.5/commontypes" xmlns:dep1="http://www.cibeg.com/esb/financial/objects/v6.5/depositproduct">
            ${header}
    <soapenv:Body>
   <dep:RetrieveDepositProductFulfilmentRequest>
   <com:PartyIdent>0000000000</com:PartyIdent>
   <com:ProductIdent>${payload.depositNumber}</com:ProductIdent>
   <dep1:DepositProductType>${payload.depositType}</dep1:DepositProductType>
   </dep:RetrieveDepositProductFulfilmentRequest>
</soapenv:Body>
</soapenv:Envelope>`;
  }
}
