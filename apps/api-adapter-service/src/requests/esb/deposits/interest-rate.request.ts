import { Request } from '@/adapter/decorators/request.decorator';
import { Injectable } from '@nestjs/common';
import { EsbRequestsEnum, RequestModuleEnum } from '@enums';
import { ClassConstructor, Expose, Transform } from 'class-transformer';
import { IsOptional } from 'class-validator';
import { EsbRequest } from '@/adapter/requests/esb.request';
import { DateTime } from 'luxon';

export class DepositInterestRateRequestDto {
  @Expose()
  @IsOptional()
  @Transform(({ value }) => value || '0000000000')
  amount: string;

  @Expose()
  @IsOptional()
  productCode: string;
}

@Injectable()
@Request(EsbRequestsEnum.DEPOSIT_INTEREST_RATE, RequestModuleEnum.ESB)
export class DepositInterestRateRequest extends EsbRequest {
  serviceName: string = '01000310';
  payloadDto: ClassConstructor<any> = DepositInterestRateRequestDto;
  // responseDto: ClassConstructor<any> = EsbDepositDetailsItemDto;

  SOAPAction: string =
    'http://www.cibeg.com/esb/wsvc/v5.5/consumeradvicefulfillment/retrieveConsumerAdviceFulfillment';
  xmlRootPath = [
    'RetrieveConsumerAdviceFulfillmentResponse',
    'DepositProdInfo',
    'Interest',
    'IntRate',
  ];

  soapTemplate(payload: DepositInterestRateRequestDto, header: string): string {
    return `<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:head="http://www.cibeg.com/esb/common/v1.0/header" xmlns:dep="http://www.cibeg.com/esb/financial/services/v6.5/depositproductfulfilment" xmlns:com="http://www.cibeg.com/esb/financial/objects/v6.5/commontypes" xmlns:dep1="http://www.cibeg.com/esb/financial/objects/v6.5/depositproduct">
            ${header}
    <soapenv:Body>
      <ns:RetrieveConsumerAdviceFulfillmentRequest xmlns:ns="http://www.cibeg.com/esb/financial/services/v5.5/consumeradvicefulfillment">
         <TotalCurAmt>
            <Amt>${payload.amount}</Amt>
         </TotalCurAmt>
         <OriginationDt>${DateTime.now().toFormat('yyyy-MM-dd')}</OriginationDt>
         <DepositProduct>${payload.productCode}</DepositProduct>
      </ns:RetrieveConsumerAdviceFulfillmentRequest>
   </soapenv:Body>
</soapenv:Envelope>`;
  }
}
