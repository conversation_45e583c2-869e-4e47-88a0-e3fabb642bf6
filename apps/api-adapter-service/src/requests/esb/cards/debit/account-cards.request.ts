import { Request } from '@/adapter/decorators/request.decorator';
import { Injectable } from '@nestjs/common';
import { EsbProductTypeEnum, EsbRequestsEnum, RequestModuleEnum } from '@enums';
import { ClassConstructor, Expose } from 'class-transformer';
import { IsNotEmpty } from 'class-validator';
import { EsbRequest } from '@/adapter/requests/esb.request';
import { EsbDebitCardListItemDto } from '@dtos';

export class LinkedCardListRequestDto {
  @Expose()
  @IsNotEmpty()
  accountNumber: string;
}

@Injectable()
@Request(EsbRequestsEnum.LINKED_DEBIT_CARDS, RequestModuleEnum.ESB)
export class CardListRequest extends EsbRequest {
  serviceName: string = '********';
  payloadDto: ClassConstructor<any> = LinkedCardListRequestDto;
  responseDto: ClassConstructor<any> = EsbDebitCardListItemDto;
  SOAPAction: string =
    'http://www.cibeg.com/esb/wsvc/v7.0/partyposition/retrievepartypositionmeasuremen';
  xmlRootPath = ['RetrievePartyPositionMeasurementResponse', 'DebitCardsList'];

  soapTemplate(payload: LinkedCardListRequestDto, header: string): string {
    return `<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:head="http://www.cibeg.com/esb/common/v1.0/header" xmlns:par="http://www.cibeg.com/esb/financial/services/v7.0/partyposition" xmlns:com="http://www.cibeg.com/esb/financial/objects/v7.0/commontypes">
        ${header}
       <soapenv:Body>
				  <par:RetrievePartyPositionMeasurementRequest>
					  <com:PartyIdent>**********</com:PartyIdent>
					  <CardAcctIdent>${payload.accountNumber}</CardAcctIdent>
					  <LegalSelKey>
						  <LegalIdentType>IdntyCardNb</LegalIdentType>
						  <LegalIdentValue>0</LegalIdentValue>
					  </LegalSelKey>
					  <ProductType>${EsbProductTypeEnum.DebitCard}</ProductType>
				  </par:RetrievePartyPositionMeasurementRequest>
			  </soapenv:Body>
      </soapenv:Envelope>`;
  }
}
