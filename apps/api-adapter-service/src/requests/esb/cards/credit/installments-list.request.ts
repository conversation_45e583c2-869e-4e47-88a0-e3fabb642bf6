import { Request } from '@/adapter/decorators/request.decorator';
import { Injectable } from '@nestjs/common';
import { EsbProductTypeEnum, EsbRequestsEnum, RequestModuleEnum } from '@enums';
import { ClassConstructor, Expose } from 'class-transformer';
import { IsNotEmpty } from 'class-validator';
import { EsbRequest } from '@/adapter/requests/esb.request';
import { EsbInstallmentsListItemDto } from '@dtos';

export class CreditCardInstallmentsRequestDto {
  @Expose()
  @IsNotEmpty()
  partyIdent: string;

  @Expose()
  @IsNotEmpty()
  cardId: string;
}

@Injectable()
@Request(EsbRequestsEnum.CREDIT_CARD_INSTALLMENTS, RequestModuleEnum.ESB)
export class CreditCardInstallmentsRequest extends EsbRequest {
  serviceName: string = '01000310';
  payloadDto: ClassConstructor<any> = CreditCardInstallmentsRequestDto;
  responseDto: ClassConstructor<any> = EsbInstallmentsListItemDto;
  SOAPAction: string =
    'http://www.cibeg.com/esb/wsvc/v5.5/cardsfulfilment/retrievecreditcardprogramtransaction';
  xmlRootPath = [
    'RetrieveCreditCardProgramTransactionResponse',
    'TransactionList',
  ];

  soapTemplate(
    payload: CreditCardInstallmentsRequestDto,
    header: string,
  ): string {
    return `
      <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" 
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
        xmlns:head="http://www.cibeg.com/esb/common/v1.0/header" 
        xmlns:caf="http://www.cibeg.com/esb/financial/services/v5.5/currentaccountfulfilment" 
        xmlns:com="http://www.cibeg.com/esb/financial/objects/v5.5/commontypes" 
        xmlns:par="http://www.cibeg.com/esb/financial/services/v5.5/partyregistration" 
        xmlns:xsd="http://www.w3.org/2001/XMLSchema">
        ${header}
        <soapenv:Body>
                <ns:RetrieveCreditCardProgramTransactionRequest 
                    xmlns:ns="http://www.cibeg.com/esb/financial/services/v5.5/cardsfulfilment">
                    <CardSelKeys>
                        <CardPartyIdent>${payload.partyIdent}</CardPartyIdent>
                        <CardIdent>${payload.cardId}</CardIdent>
                        <CardType>${EsbProductTypeEnum.CreditCard}</CardType>
                    </CardSelKeys>
                    <CardIdentifier>AC</CardIdentifier>
                </ns:RetrieveCreditCardProgramTransactionRequest>
            </soapenv:Body>
        </soapenv:Envelope>
    `;
  }
}
