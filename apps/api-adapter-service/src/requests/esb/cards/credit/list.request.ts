import { Request } from '@/adapter/decorators/request.decorator';
import { Injectable } from '@nestjs/common';
import { EsbProductTypeEnum, EsbRequestsEnum, RequestModuleEnum } from '@enums';
import { ClassConstructor } from 'class-transformer';
import { EsbCreditCardListItemDto } from '@dtos';
import { RetrievePartyPositionRequest } from '../../common/partyposition.request';

@Injectable()
@Request(EsbRequestsEnum.CREDIT_CARD_LIST, RequestModuleEnum.ESB)
export class EsbCreditCardsListRequest extends RetrievePartyPositionRequest {
  productType: EsbProductTypeEnum = EsbProductTypeEnum.CreditCard;
  serviceName: string = '01000310';
  opMode: string = 'CORP';
  responseDto: ClassConstructor<any> = EsbCreditCardListItemDto;
  xmlRootPath = ['RetrievePartyPositionMeasurementResponse', 'CreditCardsList'];
}
