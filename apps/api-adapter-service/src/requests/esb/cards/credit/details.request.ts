import { Request } from '@/adapter/decorators/request.decorator';
import { Injectable } from '@nestjs/common';
import { EsbProductTypeEnum, EsbRequestsEnum, RequestModuleEnum } from '@enums';
import { ClassConstructor, Expose } from 'class-transformer';
import { IsNotEmpty } from 'class-validator';
import { EsbRequest } from '@/adapter/requests/esb.request';
import { EsbCreditCardDetailsDto } from '@dtos';

export class CreditCardDetailsRequestDto {
  @Expose()
  @IsNotEmpty()
  partyIdent: string;

  @Expose()
  @IsNotEmpty()
  cardId: string;
}

@Injectable()
@Request(EsbRequestsEnum.CREDIT_CARD_DETAILS, RequestModuleEnum.ESB)
export class CreditCardDetailsRequest extends EsbRequest {
  serviceName: string = '19999999';
  payloadDto: ClassConstructor<any> = CreditCardDetailsRequestDto;
  readonly responseDto: ClassConstructor<any> = EsbCreditCardDetailsDto;

  SOAPAction: string =
    'http://www.cibeg.com/esb/wsvc/v6.5/cardsfulfilment/retrievecardsfulfilment';
  xmlRootPath = ['RetrieveCardsFulfilmentResponse'];

  soapTemplate(payload: CreditCardDetailsRequestDto, header: string): string {
    return `
      <soapenv:Envelope
        xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
        xmlns:head="http://www.cibeg.com/esb/common/v1.0/header"
        xmlns:car="http://www.cibeg.com/esb/financial/services/v6.5/cardsfulfilment"
      >
        ${header}
        <soapenv:Body>
          <car:RetrieveCardsFulfilmentRequest>
            <CardPartyIdent>${payload.partyIdent}</CardPartyIdent>
            <CardIdent>${payload.cardId}</CardIdent>
            <CardType>${EsbProductTypeEnum.CreditCard}</CardType>
          </car:RetrieveCardsFulfilmentRequest>
        </soapenv:Body>
      </soapenv:Envelope>
    `;
  }
}
