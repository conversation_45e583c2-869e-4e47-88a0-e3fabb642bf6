import { Modu<PERSON> } from '@nestjs/common';
import { CustomerDetailsRequest } from '@/requests/esb/customers/details.request';
import { AccountDetailsRequest } from './accounts/details.request';
import { ForexRateRequest } from './globals/fxrate.request';
import { CardListRequest } from '@/requests/esb/cards/debit/account-cards.request';
import { CreditCardDetailsRequest } from './cards/credit/details.request';
import { CreditCardInstallmentsRequest } from './cards/credit/installments-list.request';
import { SwiftConfirmationRequest } from '@/requests/esb/globals/swift-confirmation.request';
import { AccountsListRequest } from './accounts/list.request';
import { DepositListRequest } from '@/requests/esb/deposits/list.request';
import { DepositDetailsRequest } from '@/requests/esb/deposits/details.request';
import { DepositInterestRateRequest } from '@/requests/esb/deposits/interest-rate.request';
import { EsbCreditCardsListRequest } from './cards/credit/list.request';
import { EsbLoansListRequest } from './loans/list.request';
import { DepositSimulateRedemptionRequest } from '@/requests/esb/deposits/simulate-redemption.request';
import { LoanDetailsRequest } from '@/requests/esb/loans/details.request';
import { IPNBankListRequest } from '@/requests/esb/ipn/bank-list.request';

@Module({
  imports: [],
  controllers: [],
  providers: [
    CustomerDetailsRequest,
    AccountDetailsRequest,
    ForexRateRequest,
    CardListRequest,
    CreditCardDetailsRequest,
    CreditCardInstallmentsRequest,
    SwiftConfirmationRequest,
    AccountsListRequest,
    DepositListRequest,
    DepositDetailsRequest,
    EsbCreditCardsListRequest,
    EsbLoansListRequest,
    DepositInterestRateRequest,
    DepositSimulateRedemptionRequest,
    LoanDetailsRequest,
    IPNBankListRequest,
  ],
})
export class ESBModule {}
