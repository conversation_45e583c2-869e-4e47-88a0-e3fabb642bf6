import { Request } from '@/adapter/decorators/request.decorator';
import { Injectable } from '@nestjs/common';
import { EsbRequestsEnum, RequestModuleEnum } from '@enums';
import { ClassConstructor, Expose, Transform } from 'class-transformer';
import { IsOptional } from 'class-validator';
import { EsbRequest } from '@/adapter/requests/esb.request';
import { EsbLoanDetailsDto } from '@dtos';

export class LoanDetailsRequestDto {
  @Expose()
  @IsOptional()
  @Transform(({ value }) => value || '0000000000')
  loanNumber: string;
}

@Injectable()
@Request(EsbRequestsEnum.LOANS_DETAILS, RequestModuleEnum.ESB)
export class LoanDetailsRequest extends EsbRequest {
  serviceName: string = '01000310';
  payloadDto: ClassConstructor<any> = LoanDetailsRequestDto;
  responseDto: ClassConstructor<any> = EsbLoanDetailsDto;

  SOAPAction: string =
    'http://www.cibeg.com/esb/wsvc/v5.5/loanfulfilment/retrieveloanfulfilment';
  xmlRootPath = ['RetrieveLoanFulfilmentResponse'];

  soapTemplate(payload: LoanDetailsRequestDto, header: string): string {
    return `<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:head="http://www.cibeg.com/esb/common/v1.0/header" xmlns:loan="http://www.cibeg.com/esb/financial/services/v5.5/loanfulfilment" xmlns:com="http://www.cibeg.com/esb/financial/objects/v5.5/commontypes" xmlns:loan1="http://www.cibeg.com/esb/financial/objects/v5.5/loans">
            ${header}
    <soapenv:Body>
    <loan:RetrieveLoanFulfilmentRequest>
            <com:PartyIdent>0000000000</com:PartyIdent>
            <com:ProductIdent>${payload.loanNumber}</com:ProductIdent>
            <loan1:LoanProductType>Loan</loan1:LoanProductType>
        </loan:RetrieveLoanFulfilmentRequest>
</soapenv:Body>
</soapenv:Envelope>`;
  }
}
