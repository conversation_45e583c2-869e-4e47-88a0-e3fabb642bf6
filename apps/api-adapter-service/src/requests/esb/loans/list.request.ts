import { Request } from '@/adapter/decorators/request.decorator';
import { Injectable } from '@nestjs/common';
import {
  CacheTTL,
  EsbProductTypeEnum,
  EsbRequestsEnum,
  RequestModuleEnum,
} from '@enums';
import { ClassConstructor } from 'class-transformer';
import { EsbLoanListItemDto } from '@dtos';
import { RetrievePartyPositionRequest } from '../common/partyposition.request';

@Injectable()
@Request(EsbRequestsEnum.LOANS_LIST, RequestModuleEnum.ESB)
export class EsbLoansListRequest extends RetrievePartyPositionRequest {
  productType: EsbProductTypeEnum = EsbProductTypeEnum.LOAN;
  serviceName: string = '01000310';
  responseDto: ClassConstructor<any> = EsbLoanListItemDto;
  xmlRootPath = ['RetrievePartyPositionMeasurementResponse', 'LoanList'];
  cacheTtl = CacheTTL.FiveMinutes;
  cacheEnabled: boolean = true;
}
