import { EsbRequest } from '@/adapter/requests/esb.request';
import { EsbProductTypeEnum } from '@enums';
import { Injectable } from '@nestjs/common';
import { ClassConstructor, Expose } from 'class-transformer';
import { IsOptional } from 'class-validator';
import { castArray } from 'lodash';

export class RetrievePartyPositionRequestDto {
  @Expose()
  @IsOptional()
  userId: string;
}

@Injectable()
export abstract class RetrievePartyPositionRequest extends EsbRequest {
  abstract readonly productType: EsbProductTypeEnum;

  SOAPAction: string =
    'http://www.cibeg.com/esb/wsvc/v7.0/partyposition/retrievepartypositionmeasuremen';

  payloadDto: ClassConstructor<any> = RetrievePartyPositionRequestDto;

  soapTemplate(
    payload: RetrievePartyPositionRequestDto,
    header: string,
  ): string {
    return `<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:head="http://www.cibeg.com/esb/common/v1.0/header" xmlns:par="http://www.cibeg.com/esb/financial/services/v7.0/partyposition" xmlns:com="http://www.cibeg.com/esb/financial/objects/v7.0/commontypes">
            ${header}
            <soapenv:Body>
              <par:RetrievePartyPositionMeasurementRequest>
                  <com:PartyIdent>${payload.userId}</com:PartyIdent>
                  <LegalSelKey>
                      <LegalIdentType>IdntyCardNb</LegalIdentType>
                      <LegalIdentValue>0</LegalIdentValue>
                  </LegalSelKey>
                  <ProductType>${this.productType}</ProductType>
              </par:RetrievePartyPositionMeasurementRequest>
            </soapenv:Body>
          </soapenv:Envelope>`;
  }

  format(data: any) {
    const formatted = super.format(data);
    return castArray(formatted);
  }
}
