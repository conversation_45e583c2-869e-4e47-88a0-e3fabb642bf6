import { Request } from '@/adapter/decorators/request.decorator';
import { Injectable } from '@nestjs/common';
import { EsbRequestsEnum, RequestModuleEnum } from '@enums';
import { ClassConstructor, Expose, Transform } from 'class-transformer';
import { IsNotEmpty, IsOptional } from 'class-validator';
import { EsbRequest } from '@/adapter/requests/esb.request';
import { EsbAccountDetailsDto } from '@dtos';

export class AccountDetailsRequestRequestDto {
  @Expose()
  @IsOptional()
  @Transform(({ value }) => value || '**********')
  userId: string;

  @Expose()
  @IsNotEmpty()
  accountNumber: string;
}

@Injectable()
@Request(EsbRequestsEnum.ACCOUNT_DETAILS, RequestModuleEnum.ESB)
export class AccountDetailsRequest extends EsbRequest {
  serviceName: string = '********';
  payloadDto: ClassConstructor<any> = AccountDetailsRequestRequestDto;
  responseDto: ClassConstructor<any> = EsbAccountDetailsDto;
  SOAPAction: string =
    'http://www.cibeg.com/esb/wsvc/v6.5/currentaccountfulfilment/retrievecurrentaccountfulfilment';
  xmlRootPath = ['RetrieveCurrentAccountFulfilmentResponse'];

  soapTemplate(
    payload: AccountDetailsRequestRequestDto,
    header: string,
  ): string {
    return `<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:head="http://www.cibeg.com/esb/common/v1.0/header" xmlns:caf="http://www.cibeg.com/esb/financial/services/v6.0/currentaccountfulfilment" xmlns:com="http://www.cibeg.com/esb/financial/objects/v6.0/commontypes" xmlns:par="http://www.cibeg.com/esb/financial/services/v6.0/partyregistration" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
        ${header}
        <soapenv:Body>
          <ns:RetrieveCurrentAccountFulfilmentRequest
            xmlns:ns="http://www.cibeg.com/esb/financial/services/v6.0/currentaccountfulfilment">
            <com:PartyIdent>${payload.userId}</com:PartyIdent>
            <com:AcctNum>${payload.accountNumber}</com:AcctNum>
          </ns:RetrieveCurrentAccountFulfilmentRequest>
        </soapenv:Body>
      </soapenv:Envelope>`;
  }

  format(data: string) {
    const formatted = super.format(data);
    return {
      ...formatted,
      isOverdraft: formatted?.accountTrnLimit?.overdraft?.currentAmount !== 0,
    };
  }
}
