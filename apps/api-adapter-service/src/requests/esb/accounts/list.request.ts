import { Request } from '@/adapter/decorators/request.decorator';
import { Injectable } from '@nestjs/common';
import { EsbProductTypeEnum, EsbRequestsEnum, RequestModuleEnum } from '@enums';
import { ClassConstructor } from 'class-transformer';
import { EsbAccountDetailsDto } from '@dtos';
import { RetrievePartyPositionRequest } from '../common/partyposition.request';

@Injectable()
@Request(EsbRequestsEnum.ACCOUNTS_LIST, RequestModuleEnum.ESB)
export class AccountsListRequest extends RetrievePartyPositionRequest {
  productType: EsbProductTypeEnum = EsbProductTypeEnum.CASA;
  serviceName: string = '********';
  responseDto: ClassConstructor<any> = EsbAccountDetailsDto;
  xmlRootPath = ['RetrievePartyPositionMeasurementResponse', 'AccountList'];
}
