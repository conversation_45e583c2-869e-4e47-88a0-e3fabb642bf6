import { Request } from '@/adapter/decorators/request.decorator';
import { Injectable } from '@nestjs/common';
import { EsbRequest } from '@/adapter/requests/esb.request';
import { IsNotEmpty } from 'class-validator';
import { ClassConstructor, Expose } from 'class-transformer';
import { CacheTTL, EsbRequestsEnum, RequestModuleEnum } from '@enums';
import { EsbCustomerDetailsDto } from '@dtos';

export class UserDetailsRequestDTO {
  @Expose()
  @IsNotEmpty()
  userId: string;
}

@Injectable()
@Request(EsbRequestsEnum.CUSTOMER_DETAILS, RequestModuleEnum.ESB)
export class CustomerDetailsRequest extends EsbRequest {
  serviceName: string = '01000310';
  payloadDto: ClassConstructor<any> = UserDetailsRequestDTO;
  responseDto: ClassConstructor<any> = EsbCustomerDetailsDto;
  xmlRootPath: string[] = [
    'RetrievePartyRegistrationResponse',
    'PartyInfoDetails',
  ];

  cacheEnabled: boolean = true;
  cacheTtl: number = CacheTTL.TenMinutes;

  readonly SOAPAction: string =
    'http://www.cibeg.com/esb/wsvc/v7.0/partyregistration/retrievepartyregistration';

  soapTemplate(payload: UserDetailsRequestDTO, header: string): string {
    return `<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:head="http://www.cibeg.com/esb/common/v1.0/header" xmlns:par="http://www.cibeg.com/esb/financial/services/v7.0/partyregistration" xmlns:com="http://www.cibeg.com/esb/financial/objects/v7.0/commontypes">
    ${header}
    <soapenv:Body>
        <par:RetrievePartyRegistrationRequest>
            <com:PartyIdent>${payload.userId}</com:PartyIdent>
        </par:RetrievePartyRegistrationRequest>
    </soapenv:Body>
  </soapenv:Envelope>`;
  }
}
