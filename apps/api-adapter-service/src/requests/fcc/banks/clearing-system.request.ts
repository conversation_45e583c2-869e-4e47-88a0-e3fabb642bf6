import { Request } from '@/adapter/decorators/request.decorator';
import { Injectable, RequestMethod } from '@nestjs/common';
import { FccRequestsEnum, RequestModuleEnum } from '@enums';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';
import { ClassConstructor, Expose } from 'class-transformer';
import { IsNotEmpty, IsString } from 'class-validator';

export class ClearingSystemRequestDto {
  @Expose()
  @IsString()
  @IsNotEmpty()
  accountId: string;
}

@Injectable()
@Request(FccRequestsEnum.CLEARING_SYSTEM, RequestModuleEnum.FCC)
export class ClearingSystemRequestRequest extends FccRestRequest {
  payloadDto: ClassConstructor<any> = ClearingSystemRequestDto;

  ApiUrl(payload: ClearingSystemRequestDto): string {
    return `dxp/get-clearing-system/${payload.accountId}`;
  }

  requestMethod: RequestMethod = RequestMethod.GET;
}
