import { Request } from '@/adapter/decorators/request.decorator';
import { Injectable, RequestMethod } from '@nestjs/common';
import { FccRequestsEnum, RequestModuleEnum } from '@enums';
import { ClassConstructor, Expose } from 'class-transformer';
import { IsNotEmpty, IsString } from 'class-validator';
import { FccListDataRestRequest } from '@/adapter/requests/fcc-listdata.request';

export class SwiftDetailsRequestDto {
  @Expose()
  @IsString()
  @IsNotEmpty()
  bicCode: string;
}

@Injectable()
@Request(FccRequestsEnum.BANKS_SWIFT_DETAILS, RequestModuleEnum.FCC)
export class SwiftDetailsRequest extends FccListDataRestRequest {
  payloadDto: ClassConstructor<any> = SwiftDetailsRequestDto;

  getRequestPayload(payload: SwiftDetailsRequestDto) {
    const filters = JSON.stringify({ bic: payload.bicCode });

    return {
      FilterValues: encodeURIComponent(filters),
      Name: 'core/listdef/systemfeatures/MT103swiftBanks',
    };
  }

  requestMethod: RequestMethod = RequestMethod.GET;

  format(data: any): any {
    const formatted = super.format(data);
    if (formatted?.count > 0) {
      const {
        country,
        address,
        city,
        bank_name: bankName,
        postCode,
        bic: bicJson,
        alternate_code: alternateCode,
      } = formatted.rowDetails[0];

      const bic = JSON.parse(bicJson);

      return {
        isValid: true,
        bankName,
        address,
        city,
        country,
        postCode,
        bic: bic.displayedFieldValue,
        alternateCode,
      };
    }
    return { isValid: false };
  }
}
