import { FccRequestsEnum, RequestModuleEnum } from '@enums';
import { Request } from '@/adapter/decorators/request.decorator';
import { Injectable, RequestMethod } from '@nestjs/common';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';
import { ClassConstructor, Expose } from 'class-transformer';
import { IsNotEmpty } from 'class-validator';
import { FccLoanDetailsDto } from '../../../../../../packages/dtos/src/api-adapter/fcc-loans.dtos';

export class AccountDetailsRequestDTO {
  @Expose()
  @IsNotEmpty()
  accountId: number;
}

@Injectable()
@Request(FccRequestsEnum.LOAN_DETAILS, RequestModuleEnum.FCC)
export class LoanDetailsRequest extends FccRestRequest {
  ApiUrl(payload: AccountDetailsRequestDTO): string {
    return `accounts/${payload.accountId}`;
  }

  responseDto: ClassConstructor<any> = FccLoanDetailsDto;
  requestMethod: RequestMethod = RequestMethod.GET;
}
