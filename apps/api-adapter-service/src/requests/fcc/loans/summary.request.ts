import { FccRequestsEnum, RequestModuleEnum } from '@enums';
import { Request } from '@/adapter/decorators/request.decorator';
import { Injectable, RequestMethod } from '@nestjs/common';
import { FccListDataRestRequest } from '@/adapter/requests/fcc-listdata.request';
import { ClassConstructor, Expose } from 'class-transformer';
import { MoneyTransformer } from '@helpers';

export class SingleItemResponseDto {
  @Expose({ name: 'end_date' })
  endDate: string;

  @Expose({ name: 'account_type' })
  accountType: string;

  @Expose({ name: 'unique_id' })
  uniqueId: string;

  @Expose({ name: 'convertedPrincipal_amount' })
  @MoneyTransformer()
  convertedPrincipalAmount: string;

  @Expose({ name: 'UserAccount@EntityAccount@nick_name' })
  userAccountEntityAccountNickName: string;

  @Expose({ name: 'description' })
  description: string;

  @Expose({ name: '_group_id' })
  groupId: string;

  @Expose({ name: 'principal_amount' })
  @MoneyTransformer()
  principalAmount: string;

  @Expose({ name: 'account_no' })
  accountNo: string;

  @Expose({ name: 'RunningStatement@AvailableBalance@value_date' })
  availableBalanceValueDate: string;

  @Expose({ name: 'maturity_amount' })
  @MoneyTransformer()
  maturityAmount: string;

  @Expose({ name: 'cur_code' })
  curCode: string;

  @Expose({ name: 'account_id' })
  accountId: string;

  @Expose({ name: 'branch_no' })
  branchNo: string;

  @Expose({ name: 'bank_id' })
  bankId: string;

  @Expose({ name: 'bo_cust_number' })
  boCustNumber: string;

  @Expose({ name: 'nickname' })
  nickname: string;

  @Expose({ name: 'acct_name' })
  acctName: string;

  @Expose({ name: 'owner_type' })
  ownerType: string;

  @Expose({ name: '_row_type' })
  rowType: string;

  @Expose({ name: 'start_date' })
  startDate: string;

  @Expose({ name: 'UserAccount@EntityAccount@entity@abbv_name' })
  userAccountEntityAccountEntityAbbvName: string;
}

@Injectable()
@Request(FccRequestsEnum.LOANS_SUMMARY, RequestModuleEnum.FCC)
export class LoanAccountSummaryRequest extends FccListDataRestRequest {
  singleItemResponseDto: ClassConstructor<any> = SingleItemResponseDto;

  requestMethod: RequestMethod = RequestMethod.GET;
  cacheEnabled: boolean = true;

  ApiUrl(): string {
    return 'listdata?Name=cash/listdef/customer/AB/accountSummaryLoan';
  }

  format(data: any) {
    const formatted = super.format(data);
    return formatted?.rowDetails ?? [];
  }
}
