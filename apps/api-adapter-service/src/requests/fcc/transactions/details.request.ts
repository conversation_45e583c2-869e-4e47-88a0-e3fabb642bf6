import { Request } from '@/adapter/decorators/request.decorator';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';
import { FccGenericTnxResponseDto } from '@dtos';
import {
  CacheTTL,
  FccRequestsEnum,
  ProductCodeEnum,
  RequestModuleEnum,
} from '@enums';
import { Injectable, RequestMethod } from '@nestjs/common';
import { ClassConstructor, Expose, plainToClass } from 'class-transformer';
import { IsEnum, IsNotEmpty, IsString } from 'class-validator';
import { FccGenricTnxRequest } from '../common/generic-tnx.request';

/**
 * DTO for the transaction details request payload.
 */
export class TransactionDetailsRequestDTO {
  @Expose()
  eventId: string;

  @Expose()
  @IsNotEmpty()
  @IsEnum(ProductCodeEnum)
  productCode: ProductCodeEnum;
}

/**
 * Handles transaction-details GET request and defines the API URL.
 * Requires transactionId in the payload to construct the URL.
 */
@Injectable()
@Request(FccRequestsEnum.TRANSACTION_DETAILS, RequestModuleEnum.FCC)
export class TransactionDetailsRequest extends FccGenricTnxRequest {
  requestMethod: RequestMethod = RequestMethod.GET;
  payloadDto: ClassConstructor<any> = TransactionDetailsRequestDTO;
  responseDto = FccGenericTnxResponseDto;

  cacheEnabled: boolean = true;
  cacheTtl: number = CacheTTL.FiveMinutes;

  /**
   * Constructs the API URL using the transactionId from the payload.
   * @param payload Should contain transactionId
   */
  ApiUrl(): string {
    return `details`;
  }
}
