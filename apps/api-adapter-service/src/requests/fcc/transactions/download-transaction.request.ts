import { Injectable, RequestMethod } from '@nestjs/common';
import { Request } from '@/adapter/decorators/request.decorator';
import { FccRequestsEnum, RequestModuleEnum, ResponseTypeEnum } from '@enums';
import { ClassConstructor, Expose } from 'class-transformer';
import { IsNotEmpty, IsString } from 'class-validator';
import { FccPortalRequest } from '@/adapter/requests/fcc-portal.request';

export class DownloadTransactionRequestDto {
  @Expose()
  @IsString()
  @IsNotEmpty()
  referenceId: string;

  @Expose()
  @IsString()
  @IsNotEmpty()
  tnxId: string;

  @Expose()
  @IsString()
  @IsNotEmpty()
  productCode: string;
}

@Injectable()
@Request(FccRequestsEnum.DOWNLOAD_TRANSACTION, RequestModuleEnum.FCC, 'v1')
export class DownloadTransactionRequest extends FccPortalRequest {
  readonly payloadDto: ClassConstructor<any> = DownloadTransactionRequestDto;
  requestMethod: RequestMethod = RequestMethod.GET;
  responseType = ResponseTypeEnum.ArrayBuffer;

  ApiUrl(): string {
    return `screen/ReportingPopup`;
  }

  getRequestPayload(payload: any) {
    return {
      option: 'EXPORT_PDF_FULL',
      referenceid: payload.referenceId,
      tnxid: payload.tnxId,
      productcode: payload.productCode,
    };
  }

  format(data: any) {
    const buffer = Buffer.isBuffer(data) ? data : Buffer.from(data);

    return {
      response: 'success',
      data: buffer.toString('base64'),
    };
  }
}
