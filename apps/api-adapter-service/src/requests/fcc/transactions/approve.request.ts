import { Request } from '@/adapter/decorators/request.decorator';
import { Injectable, RequestMethod } from '@nestjs/common';
import { FccRequestsEnum, RequestModuleEnum } from '@enums';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';
import { ClassConstructor, Expose } from 'class-transformer';
import { IsNotEmpty, IsOptional } from 'class-validator';

/**
 * DTO for the approve transaction request.
 * Contains the transactionId as a required property.
 */
export class ApproveTransactionRequestDTO {
  @Expose()
  @IsNotEmpty()
  transactionId: string;

  @Expose()
  @IsOptional()
  referenceKey: string;

  @Expose()
  @IsOptional()
  reauthSecret: string;
}

/**
 * Handles the approve transaction POST request.
 * Requires transactionId in the payload to construct the URL.
 */
@Injectable()
@Request(FccRequestsEnum.APPROVE_TRANSACTION, RequestModuleEnum.FCC)
export class ApproveTransactionRequest extends FccRestRequest {
  payloadDto: ClassConstructor<any> = ApproveTransactionRequestDTO;
  requestMethod: RequestMethod = RequestMethod.POST;

  /**
   * Constructs the API URL.
   * @param payload ApproveTransactionRequestDTO
   */
  ApiUrl(payload: ApproveTransactionRequestDTO): string {
    return `transactions/${encodeURIComponent(payload.transactionId)}/approve`;
  }

  /**
   * Returns an empty object as the request payload.
   */
  getRequestPayload({
    referenceKey,
    reauthSecret,
  }: ApproveTransactionRequestDTO): Record<string, any> {
    return {
      reauth: {
        referenceKey,
        reauthSecret,
      },
    };
  }
}
