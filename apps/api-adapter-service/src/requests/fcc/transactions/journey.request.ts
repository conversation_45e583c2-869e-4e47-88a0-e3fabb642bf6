import { Request } from '@/adapter/decorators/request.decorator';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';
import { TransactionJourneyResponseDto } from '@dtos';
import { FccRequestsEnum, RequestModuleEnum } from '@enums';
import { Injectable, RequestMethod } from '@nestjs/common';
import { ClassConstructor, Expose, Type } from 'class-transformer';
import { IsNotEmpty } from 'class-validator';

export class TransactionJourneyRequestDTO {
  @Expose()
  @IsNotEmpty()
  refId: string;
}

@Injectable()
@Request(FccRequestsEnum.TRANSACTION_JOURNEY_DETAILS, RequestModuleEnum.FCC)
export class TransactionJourneyRequest extends FccRestRequest {
  payloadDto: ClassConstructor<any> = TransactionJourneyRequestDTO;
  responseDto: ClassConstructor<any> = TransactionJourneyResponseDto;

  requestMethod: RequestMethod = RequestMethod.GET;

  ApiUrl(payload: TransactionJourneyRequestDTO): string {
    return `detailJourney/${payload.refId}`;
  }

  getRequestPayload() {
    return {};
  }
}
