import { Request } from '@/adapter/decorators/request.decorator';
import { Injectable, RequestMethod } from '@nestjs/common';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';
import { ClassConstructor, Expose } from 'class-transformer';
import { IsNotEmpty, IsOptional } from 'class-validator';
import { FccRequestsEnum, RequestModuleEnum } from '@enums';

/**
 * DTO for the reject transaction request payload.
 */
export class RejectTransactionRequestDTO {
  @Expose()
  @IsNotEmpty()
  transactionId: string;

  @Expose()
  @IsNotEmpty()
  comments: string;

  @Expose()
  @IsOptional()
  referenceKey: string;

  @Expose()
  @IsOptional()
  reauthSecret: string;
}

/**
 * Handles the reject transaction POST request.
 * Both the transactionId and rejectComment must be provided in the payload.
 */
@Injectable()
@Request(FccRequestsEnum.REJECT_TRANSACTION, RequestModuleEnum.FCC)
export class RejectTransactionRequest extends FccRestRequest {
  payloadDto: ClassConstructor<any> = RejectTransactionRequestDTO;
  requestMethod: RequestMethod = RequestMethod.POST;

  /**
   * Constructs the API URL using the transactionId from the payload.
   * @param payload Should contain transactionId and rejectComment
   */
  ApiUrl(payload: {
    transactionId: string;
    comments: { rejectComments: string };
  }): string {
    return `transactions/${encodeURIComponent(payload.transactionId)}/reject`;
  }

  /**
   * Returns the payload for the POST request.
   * @param payload Should contain comments object
   */
  getRequestPayload({
    comments,
    referenceKey,
    reauthSecret,
  }: RejectTransactionRequestDTO) {
    return {
      comments: {
        rejectComments: comments,
      },
      reauth: {
        referenceKey,
        reauthSecret,
      },
    };
  }
}
