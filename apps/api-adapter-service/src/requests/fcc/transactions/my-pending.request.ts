import { Request } from '@/adapter/decorators/request.decorator';
import { TransactionListItemDto } from '@dtos';
import { FccRequestsEnum, RequestModuleEnum } from '@enums';
import { Injectable, RequestMethod } from '@nestjs/common';
import { ClassConstructor } from 'class-transformer';
import { TransactionsListRequest } from './list.request';
@Injectable()
@Request(FccRequestsEnum.MY_PENDING_TRANSACTIONS, RequestModuleEnum.FCC)
export class MyPendingTransactionsRequest extends TransactionsListRequest {
  listName: string = 'core/listdef/customer/MC/dxpOpenTODOSubmitMCList';
  singleItemResponseDto: ClassConstructor<any> = TransactionListItemDto;
  requestMethod: RequestMethod = RequestMethod.GET;
}
