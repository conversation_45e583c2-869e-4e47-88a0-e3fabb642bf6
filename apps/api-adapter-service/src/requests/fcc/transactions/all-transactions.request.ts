import { Request } from '@/adapter/decorators/request.decorator';
import { TransactionListItemDto, TransactionsListQueryDto } from '@dtos';
import {
  FccRequestsEnum,
  RequestModuleEnum,
  TransactionStatusEnum,
} from '@enums';
import { Injectable, RequestMethod } from '@nestjs/common';
import { ClassConstructor } from 'class-transformer';
import { TransactionsListRequest } from './list.request';
import { toJoinedString, transactionStatusMapper } from '@helpers';

@Injectable()
@Request(FccRequestsEnum.ALL_TRANSACTIONS, RequestModuleEnum.FCC)
export class AllTransactionsRequest extends TransactionsListRequest {
  listName: string = 'core/listdef/customer/MC/dxpOpenMCTransactionSearchList';
  singleItemResponseDto: ClassConstructor<any> = TransactionListItemDto;
  requestMethod: RequestMethod = RequestMethod.GET;

  getFilters(payload: TransactionsListQueryDto): Record<string, any> {
    const filters = super.getFilters(payload);

    /**
     * Default statuses to include for activity log listings when no status
     * Excludes the "Incomplete Draft" (01,01,02).
     */
    const effectiveStatus =
      payload.status ??
      (Object.values(TransactionStatusEnum) as TransactionStatusEnum[]);

    const { tnxStatCode, subTnxStatCode, prodStatCode } =
      transactionStatusMapper(effectiveStatus);

    const statusFilters = {
      tnx_stat_code_val: tnxStatCode && toJoinedString(tnxStatCode),
      sub_tnx_stat_code_val: subTnxStatCode && toJoinedString(subTnxStatCode),
      ...(payload.status && {
        prod_stat_code_val: prodStatCode && toJoinedString(prodStatCode),
      }),
    };

    return { ...filters, ...statusFilters };
  }
}
