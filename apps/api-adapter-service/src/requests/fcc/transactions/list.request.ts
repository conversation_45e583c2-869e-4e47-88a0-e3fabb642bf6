import { FccListDataRestRequest } from '@/adapter/requests/fcc-listdata.request';
import { TransactionListItemDto, TransactionsListQueryDto } from '@dtos';
import { toJoinedString } from '@helpers';
import { Injectable, RequestMethod } from '@nestjs/common';
import { ClassConstructor } from 'class-transformer';

@Injectable()
export abstract class TransactionsListRequest extends FccListDataRestRequest {
  abstract readonly listName: string;

  singleItemResponseDto: ClassConstructor<any> = TransactionListItemDto;
  requestMethod: RequestMethod = RequestMethod.GET;

  getFilters(payload: TransactionsListQueryDto): Record<string, any> {
    return {
      AmountRange: payload.fromAmount,
      AmountRange2: payload.toAmount,
      cur_code: payload.curCode && toJoinedString(payload.curCode),
      timeframe: payload.timeframe,
      account_no: payload.accountNo,
      inp_date_range: payload.inpDateRange,
      inp_date_range2: payload.inpDateRange2,
      product_code: payload.productCode,
      sub_product_code: payload.subProductCode,
      ref_id: payload.refId,
      inputter_user_id: payload.inputterUserId,
      exclude_inputter_user_id: payload.excludeInputterUserId,
    };
  }

  getRequestPayload(payload: TransactionsListQueryDto) {
    const filters = JSON.stringify(this.getFilters(payload));

    const start = JSON.stringify({
      first: payload.first,
      rows: payload.rows,
    });

    const requestPayload = {
      Name: this.listName,
      action: 'GetJSONData',
      FilterValues: encodeURIComponent(filters),
      Start: encodeURIComponent(start),
    };

    // Add sorting parameter if provided
    if (payload.sort) {
      requestPayload['sort'] = payload.sort;
    }

    return requestPayload;
  }

  format(data: any) {
    const formatted = super.format(data);
    return {
      count: formatted?.count || 0,
      rowDetails: formatted?.rowDetails || [],
    };
  }
}
