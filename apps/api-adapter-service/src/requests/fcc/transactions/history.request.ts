import { Request } from '@/adapter/decorators/request.decorator';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';
import { CacheTTL, FccRequestsEnum, RequestModuleEnum } from '@enums';
import { Injectable, RequestMethod } from '@nestjs/common';
import { ClassConstructor, Expose } from 'class-transformer';
import { IsNotEmpty } from 'class-validator';

export class TransactionHistoryRequestDTO {
  @Expose()
  @IsNotEmpty()
  transactionId: string;
}

@Injectable()
@Request(FccRequestsEnum.TRANSACTION_HISTORY, RequestModuleEnum.FCC)
export class TransactionHistoryRequest extends FccRestRequest {
  payloadDto: ClassConstructor<any> = TransactionHistoryRequestDTO;
  requestMethod: RequestMethod = RequestMethod.GET;

  cacheEnabled: boolean = true;
  cacheTtl: number = CacheTTL.FiveMinutes;

  ApiUrl(payload: TransactionHistoryRequestDTO): string {
    return `transactions/${payload.transactionId}/history`;
  }

  getRequestPayload() {
    return {};
  }
}
