import { FccRequestsEnum, RequestModuleEnum } from '@enums';
import { Request } from '@/adapter/decorators/request.decorator';
import { Injectable, RequestMethod } from '@nestjs/common';
import { FccListDataRestRequest } from '@/adapter/requests/fcc-listdata.request';

import { ClassConstructor, Expose } from 'class-transformer';
import { MoneyTransformer } from '@helpers';

export class SingleItemResponseDto {
  @Expose({ name: 'account_id' })
  accountID: string;

  @Expose({ name: 'card_number' })
  cardNumber: string;

  @Expose({ name: 'avail_limit' })
  @MoneyTransformer()
  availableLimit: string;

  @Expose({ name: 'credit_limit' })
  @MoneyTransformer()
  creditLimit: string;

  @Expose({ name: 'description' })
  description: string;

  @Expose({ name: 'cur_code' })
  currencyCode: string;

  @Expose({ name: 'acct_name' })
  accountName: string;
}

@Injectable()
@Request(FccRequestsEnum.CREDIT_CARDS_SUMMARY, RequestModuleEnum.FCC)
export class CreditCardSummaryRequest extends FccListDataRestRequest {
  singleItemResponseDto: ClassConstructor<any> = SingleItemResponseDto;

  requestMethod: RequestMethod = RequestMethod.GET;

  cacheEnabled: boolean = true;

  getRequestPayload() {
    return {
      Name: 'cash/listdef/customer/AB/accountSummaryCreditCard',
      action: 'GetAccountSummaryJSONData',
      rows: '10',
      start: '0',
    };
  }

  format(data: any) {
    const formatted = super.format(data);
    return formatted?.rowDetails ?? [];
  }
}
