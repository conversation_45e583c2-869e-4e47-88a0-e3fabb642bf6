import { FccRequestsEnum, RequestModuleEnum } from '@enums';
import { Request } from '@/adapter/decorators/request.decorator';
import { Injectable, RequestMethod } from '@nestjs/common';
import { ClassConstructor } from 'class-transformer';
import { FccListDataRestRequest } from '@/adapter/requests/fcc-listdata.request';
import {
  CreditCardMovementsItemResponseDto,
  CardsMovementsListRequestDto,
} from '@dtos';
import { DateTime } from 'luxon';
import { isEmpty } from 'lodash';

@Injectable()
@Request(
  FccRequestsEnum.CREDIT_CARD_MOVEMENTS_LIST,
  RequestModuleEnum.FCC,
  'v1',
)
export class CreditCardMovementsListRequest extends FccListDataRestRequest {
  listName: string = '/cash/listdef/customer/AB/dxpAccountStatementCreditCard';
  requestMethod: RequestMethod = RequestMethod.GET;
  payloadDto: ClassConstructor<CardsMovementsListRequestDto> =
    CardsMovementsListRequestDto;
  singleItemResponseDto: ClassConstructor<CreditCardMovementsItemResponseDto> =
    CreditCardMovementsItemResponseDto;

  getRequestPayload(payload: CardsMovementsListRequestDto) {
    const dateFrom = payload.dateFrom
      ? payload.dateFrom
      : DateTime.now().minus({ months: 3 }).toFormat('dd/MM/yyyy');

    const dateTo = payload.dateTo
      ? payload.dateTo
      : DateTime.now().toFormat('dd/MM/yyyy');

    const filters = {
      account_id: payload.accountId,
      create_date: encodeURIComponent(dateFrom),
      create_date2: encodeURIComponent(dateTo),
      trantype: '*',
    } as any;

    if (payload.searchTranId && !isEmpty(payload.searchTranId)) {
      filters.transaction_id = encodeURIComponent(payload.searchTranId);
    }

    if (payload.search && !isEmpty(payload.search)) {
      filters.description = encodeURIComponent('*' + payload.search + '*');
    }

    if (payload.amountFrom && !isEmpty(payload.amountFrom)) {
      filters.transaction_amt_from = encodeURIComponent(payload.amountFrom);
    }

    if (payload.amountTo && !isEmpty(payload.amountTo)) {
      filters.transaction_amt_to = encodeURIComponent(payload.amountTo);
    }

    const start = {
      first: payload.first ?? 0,
      rows: payload.rows ?? 5,
    } as any;

    //sorting
    if (payload.sortField && payload.sortOrder) {
      const sortMap: Record<string, string> = {
        transactionDate: 'transaction_date',
      };

      const mappedField = sortMap[payload.sortField];

      if (mappedField) {
        start.sortField = mappedField;
        start.sortOrder = payload.sortOrder ?? -1;
      }
    }

    const requestPayload = {
      Name: this.listName,
      action: 'GetAccountStatementJSONData',
      FilterValues: encodeURIComponent(JSON.stringify(filters)),
      Start: encodeURIComponent(JSON.stringify(start)),
    };

    if (payload.sort) {
      requestPayload['sort'] = payload.sort;
    }

    return requestPayload;
  }
}
