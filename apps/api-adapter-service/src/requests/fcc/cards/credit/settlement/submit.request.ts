import { Injectable, RequestMethod } from '@nestjs/common';
import { Request } from '@/adapter/decorators/request.decorator';
import { CurrencyEnum, FccRequestsEnum, RequestModuleEnum } from '@enums';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';
import * as snakecaseKeys from 'snakecase-keys';
import { DateTime } from 'luxon';
import { FccFtGenericTnxResponseDto } from '@dtos';

/**
 * Handles credit card transfer submission requests to FCC
 */
@Injectable()
@Request(
  FccRequestsEnum.CREDIT_CARD_SETTLEMENT_SUBMIT,
  RequestModuleEnum.FCC,
  'v1',
)
export class CreditCardTransferSubmitRequest extends FccRestRequest {
  requestMethod: RequestMethod = RequestMethod.POST;
  responseDto = FccFtGenericTnxResponseDto;

  ApiUrl(): string {
    return `genericsave`;
  }

  /**
   * Transforms the payload for FCC API format
   * @param payload Request payload from service
   * @returns Formatted payload for FCC API
   */
  getRequestPayload(payload: any) {
    const {
      // attachments = [],
      refId,
      tnxId,
      ...restPayload
    } = payload;
    const snakeCasePayload = snakecaseKeys(restPayload);

    const todayDate = DateTime.now().toFormat('dd/MM/yyyy');

    return {
      common: {
        screen: 'FundTransferScreen',
        operation: 'SUBMIT',
        option: '',
        mode: 'DRAFT',
        referenceid: refId,
        tnxid: tnxId,
        tnxtype: '01',
      },
      transaction: {
        swiftBicCodeRegexValue:
          '^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9][A-Z2-9]([A-Z0-9]{3,3}){0,1}$',

        swiftregexValue: "^[a-zA-Z0-9 :,/'?.+()\\r\\n-]*$",

        product_code: 'FT',
        sub_product_code: 'CRD',

        ft_type: '02',

        issuing_bank_name: 'CIB',
        issuing_bank_abbv_name: 'CIB',

        iss_date: todayDate,
        base_cur_code: payload.beneficiaryActCurCode,
        tnx_amt: payload.ftAmt,
        tnx_cur_code: payload.ftCurCode,
        transfer_date: todayDate,
        payment_details_ft: 'payment_details',
        ft_cur_code: payload.ftCurCode ?? CurrencyEnum.EGP,
        ...snakeCasePayload,

        // is798: '',
        // old_ctl_dttm: '',
        // old_inp_dttm: '',

        // bulk_template_id: '',
        // bulk_ref_id: '',
        // bulk_tnx_id: '',

        // issuing_bank_iso_code: '',

        // counterparty_id: '',
        // beneficiary_mode: '',

        // modifiedBeneficiary: '',

        // brch_code: '00001',
        // company_id: '112169',
        // company_name: 'TESTMATRIX',
        // tnx_id: '**************',

        // token: 'VFmx2lXzdOjKrhylBsphyMd8vW3urTJyf9nAp6TfAvE=',
        // org_term_year: '',
        // org_inco_term: '',

        // applicant_name: 'TEST MATRIX',
        // applicant_address_line_1: '',
        // applicant_address_line_2: '',
        // applicant_dom: '',
        // applicant_abbv_name: 'TESTMATRIX',
        // applicant_reference: '**********',

        // pre_approved: '',
        // pre_approved_status: '',
        // fx_tolerance_rate_value: '',
        // applicant_act_nickname: '',
        // beneficiary_nickname: '',
        // beneficiary_act_nickname: '',
        // applicant_act_name1: '',
        // beneficiary_account_name1: '',
        // fx_tolerance_rate_amt_value: '',
        // intermediary_flag: '',
        // reauth_perform: 'Y',
        // applicant_act_pab: 'Y',
        // recurring_flag: 'N',
        // appl_date: transferDate,

        // template_id: '',
        // recurring_start_date: '',
        // recurring_frequency: '',
        // recurring_on: '',
        // recurring_end_date: '',

        // appl_date_hidden: transferDate || todayDate,
        // allow_both_fields: 'N',
        // recurring_payment_enabled: 'N',
        // bk_sub_product_code: '',

        // sub_product_code_custom: '',
        // file_upload_require: '',
        // iss_date_unsigned: '',
        // sub_product_code_unsigned: 'CRD',
        // display_entity_unsigned: '',
        // ft_cur_code_unsigned: '',
        // applicant_act_cur_code_unsigned: '',
        // ft_amt_unsigned: '',

        // recurring_start_date_unsigned: '',
        // recurring_end_date_unsigned: '',
        // cust_ref_id: '',
        // beneficiary_reference: '',

        // fx_rates_type: '',
        // fx_exchange_rate: '',
        // fx_exchange_rate_cur_code: '',
        // fx_exchange_rate_amt: '',
        // fx_tolerance_rate: '',
        // fx_tolerance_rate_cur_code: '',
        // fx_tolerance_rate_amt: '',
        // fxBuyOrSell: '',
        // fxTnxAmt: '',
        // fx_nbr_contracts: '',

        // notify_beneficiary: 'N',

        // notify_beneficiary_choice: '',
        // notify_beneficiary_email: '',
        // free_format_text: '',
        // downloadfiles: '[object Object]',
        // todo_list_id: '4137810',

        // attachments: {
        //   docId: attachments,
        // },
      },
    };
  }
}
