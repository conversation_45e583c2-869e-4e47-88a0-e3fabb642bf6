import { FccRequestsEnum, RequestModuleEnum } from '@enums';
import { Request } from '@/adapter/decorators/request.decorator';
import { Injectable, RequestMethod } from '@nestjs/common';
import { FccListDataRestRequest } from '@/adapter/requests/fcc-listdata.request';

import { ClassConstructor } from 'class-transformer';
import { CreditCardListItemDto } from '@dtos';

@Injectable()
@Request(FccRequestsEnum.CREDIT_CARDS_LIST, RequestModuleEnum.FCC)
export class CreditCardListRequest extends FccListDataRestRequest {
  singleItemResponseDto: ClassConstructor<any> = CreditCardListItemDto;

  requestMethod: RequestMethod = RequestMethod.GET;

  getRequestPayload() {
    return {
      Name: 'cash/listdef/customer/AB/creditCardList',
    };
  }

  format(data: any) {
    const formatted = super.format(data);
    return formatted?.rowDetails ?? [];
  }
}
