import { FccRequestsEnum, RequestModuleEnum } from '@enums';
import { Request } from '@/adapter/decorators/request.decorator';
import { Injectable, RequestMethod } from '@nestjs/common';
import { FccListDataRestRequest } from '@/adapter/requests/fcc-listdata.request';
import { ClassConstructor, Expose } from 'class-transformer';
import { FloatNumberTransformer, MoneyTransformer } from '@helpers';

export class SingleItemResponseDto {
  @Expose({ name: 'NAV_Value' })
  @MoneyTransformer()
  fundPrice: string;

  @Expose({ name: 'Total_Value' })
  @FloatNumberTransformer()
  total: string;

  @Expose({ name: 'Quantity' })
  @FloatNumberTransformer()
  quantity: string;

  @Expose({ name: 'transaction_type' })
  fundName: string;

  @Expose({ name: 'Customer_Number' })
  customerNumber: string;
}

@Injectable()
@Request(FccRequestsEnum.MUTUAL_FUND_SUMMARY, RequestModuleEnum.FCC)
export class MutualFundSummaryRequest extends FccListDataRestRequest {
  singleItemResponseDto: ClassConstructor<any> = SingleItemResponseDto;

  requestMethod: RequestMethod = RequestMethod.GET;

  cacheEnabled: boolean = true;

  getRequestPayload() {
    return {
      Name: 'client/listdef/customer/accountMFBalanceHomeScreen',
      action: 'GetJSONData',
    };
  }

  format(data: any) {
    const formatted = super.format(data);
    return formatted?.rowDetails ?? [];
  }
}
