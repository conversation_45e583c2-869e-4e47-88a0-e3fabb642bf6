import { Request } from '@/adapter/decorators/request.decorator';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';
import { CutOffTimeRequestDto } from '@dtos';
import {
  CacheScope,
  CacheTTL,
  CutOffTimeContextEnum,
  FccRequestsEnum,
  ProductCodeEnum,
  RequestModuleEnum,
  TermDepositProductCodeEnum,
} from '@enums';
import { Injectable, RequestMethod } from '@nestjs/common';
import { ClassConstructor, Expose, Transform } from 'class-transformer';
import * as snakecaseKeys from 'snakecase-keys';

class CutOffTimeResponseDto {
  @Expose()
  valid: boolean;

  @Expose()
  autoForwardEnabled: boolean;

  @Expose()
  @Transform(({ obj }) => {
    if (obj?.errorMessage) {
      const dateRegex = /\b\d{2}\/\d{2}\/\d{4}\b/;
      const match = obj?.errorMessage.match(dateRegex);
      if (match) {
        return match[0];
      }
    }
    return '';
  })
  date: string;
}

@Injectable()
@Request(FccRequestsEnum.CUT_OFF_TIME, RequestModuleEnum.FCC)
export class CutOffTimeRequest extends FccRestRequest {
  requestMethod: RequestMethod = RequestMethod.GET;
  payloadDto: ClassConstructor<any> = CutOffTimeRequestDto;
  responseDto: ClassConstructor<any> = CutOffTimeResponseDto;

  cacheEnabled: boolean = true;
  cacheScope: CacheScope = CacheScope.GLOBAL;
  cacheTtl: number = CacheTTL.OneHour;

  ApiUrl(): string {
    return 'dxp/cutoff-time';
  }

  getRequestPayload(payload: CutOffTimeRequestDto) {
    const { context, date, ...rest } = payload;
    const body = { dateValues: date, ...rest } as any;
    switch (context) {
      case CutOffTimeContextEnum.TERM_DEPOSIT:
        body.productCode = ProductCodeEnum.TermDeposit;
        body.subProductCode = TermDepositProductCodeEnum.CSTD;
        break;
    }
    return snakecaseKeys(body);
  }
}
