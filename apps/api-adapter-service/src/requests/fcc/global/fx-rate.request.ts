import { Injectable, RequestMethod } from '@nestjs/common';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';
import { Request } from '@/adapter/decorators/request.decorator';
import { CacheScope, FccRequestsEnum, RequestModuleEnum } from '@enums';
import { ClassConstructor, Expose } from 'class-transformer';
import { FloatNumberTransformer } from '@helpers';

class FxRateResponseDto {
  @Expose()
  @FloatNumberTransformer()
  boardExchangeRate: number;
}

@Injectable()
@Request(FccRequestsEnum.FX_RATE, RequestModuleEnum.FCC, 'v1')
export class FxRateRequest extends FccRestRequest {
  requestMethod: RequestMethod = RequestMethod.GET;
  responseDto: ClassConstructor<any> = FxRateResponseDto;

  cacheEnabled: boolean = true;
  cacheScope: CacheScope = CacheScope.GLOBAL;

  ApiUrl(): string {
    return `fxrate`;
  }
}
