import { Injectable, RequestMethod } from '@nestjs/common';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';
import { Request } from '@/adapter/decorators/request.decorator';
import { FccRequestsEnum, RequestModuleEnum } from '@enums';
import { ClassConstructor, Expose } from 'class-transformer';
import { IsNotEmpty, IsString } from 'class-validator';

export class ConfigurationDetailsRequestDTO {
  @Expose()
  @IsNotEmpty()
  @IsString({ each: true })
  keys: string[];
}

@Injectable()
@Request(FccRequestsEnum.CONFIGURATION_DETAILS, RequestModuleEnum.FCC, 'v1')
export class ConfigurationDetailsRequest extends FccRestRequest {
  requestMethod: RequestMethod = RequestMethod.GET;
  payloadDto: ClassConstructor<any> = ConfigurationDetailsRequestDTO;

  ApiUrl(): string {
    return `getConfigurationDetails`;
  }

  getRequestPayload({ keys = [] }: ConfigurationDetailsRequestDTO) {
    return {
      configuredKey: keys?.join(','),
    };
  }
}
