import { Injectable, RequestMethod } from '@nestjs/common';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';
import { Request } from '@/adapter/decorators/request.decorator';
import { FccRequestsEnum, RequestModuleEnum } from '@enums';

@Injectable()
@Request(FccRequestsEnum.GENERIC_AUTH_TYPE, RequestModuleEnum.FCC, 'v1')
export class AuthTypeRequest extends FccRestRequest {
  requestMethod: RequestMethod = RequestMethod.POST;

  ApiUrl(): string {
    return `getReauthType`;
  }

  format(data: any) {
    return data.response;
  }
}
