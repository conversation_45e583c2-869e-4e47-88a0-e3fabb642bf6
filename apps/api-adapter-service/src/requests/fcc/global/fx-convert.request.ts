import { Injectable, RequestMethod } from '@nestjs/common';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';
import { Request } from '@/adapter/decorators/request.decorator';
import {
  CacheScope,
  CacheTTL,
  FccRequestsEnum,
  RequestModuleEnum,
} from '@enums';
import { ClassConstructor, Expose } from 'class-transformer';
import { FloatNumberTransformer } from '@helpers';
import { IsNotEmpty } from 'class-validator';

class FxConvertRequestDto {
  @Expose()
  @IsNotEmpty()
  fromCurrency: number;

  @Expose()
  @IsNotEmpty()
  toCurrency: number;
}

class FxConvertResponseDto {
  @Expose()
  @FloatNumberTransformer()
  toCurrencyAmount: number;
}
@Injectable()
@Request(FccRequestsEnum.FX_CONVERT, RequestModuleEnum.FCC, 'v1')
export class FxConvertRequest extends FccRestRequest {
  requestMethod: RequestMethod = RequestMethod.POST;
  payloadDto: ClassConstructor<any> = FxConvertRequestDto;
  responseDto: ClassConstructor<any> = FxConvertResponseDto;

  cacheEnabled: boolean = false;
  cacheScope: CacheScope = CacheScope.GLOBAL;
  cacheTtl: number = CacheTTL.FiveMinutes;

  //moved to convertCurrency because convert gives wrong numbers
  ApiUrl(): string {
    return `convertCurrency`;
  }

  getRequestPayload(payload: FxConvertRequestDto) {
    // always convert 1000 to cache it as global
    return { ...payload, fromCurrencyAmount: 1000 };
  }

  format(data: FxConvertResponseDto) {
    const formatted = super.format(data);
    return {
      convertedAmount: formatted.toCurrencyAmount / 1000,
    };
  }
}
