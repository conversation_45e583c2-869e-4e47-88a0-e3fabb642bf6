import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';
import { FccGenericTnxResponseDto } from '@dtos';
import { plainToClass } from 'class-transformer';

export abstract class FccGenricTnxRequest extends FccRestRequest {
  format(data: FccGenericTnxResponseDto) {
    if (this.responseDto) {
      return plainToClass(this.responseDto, data, {
        excludeExtraneousValues: false,
      });
    }
    return data;
  }
}
