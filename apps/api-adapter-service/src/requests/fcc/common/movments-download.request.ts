import { Injectable, RequestMethod } from '@nestjs/common';
import { FccPortalRequest } from '@/adapter/requests/fcc-portal.request';
import { MovementsDownloadRequestDto } from '@dtos';
import { ClassConstructor } from 'class-transformer';
import { jsonToUrlEncoded } from '@helpers';
import { ResponseTypeEnum } from '@enums';

@Injectable()
export class MovementsDownloadRequest extends FccPortalRequest {
  requestMethod: RequestMethod = RequestMethod.POST;
  payloadDto: ClassConstructor<MovementsDownloadRequestDto> =
    MovementsDownloadRequestDto;

  responseType = ResponseTypeEnum.ArrayBuffer;

  ApiUrl(): string {
    return `screen/AccountBalanceScreen?operation=LIST_STATEMENTS`;
  }

  getRequestPayload(payload: MovementsDownloadRequestDto) {
    const filename = `statement.${payload.format === 'swift' ? 'txt' : payload.format}`;
    const body = {
      account_id: payload.accountId,
      owner_type: '01',
      bic_code: '',
      stmtrange: '4',
      create_date: payload.dateFrom,
      create_date2: payload.dateTo,
      limit_range: '90',
      export_list: payload.format,
      filename,
      in_memory_export: true,
      descriptionf: '',
      trantype: '**',
    };
    return jsonToUrlEncoded(body);
  }

  format(data: any) {
    const buffer = Buffer.isBuffer(data) ? data : Buffer.from(data);

    const base64 = buffer.toString('base64');

    const isHtmlEncoded = this.isBase64Html(data);
    if (isHtmlEncoded) {
      return {
        response: 'failed',
        isHtmlEncoded: true,
      };
    }

    return {
      response: 'success',
      data: base64,
    };
  }

  isBase64Html(str: string): boolean {
    try {
      const decoded = Buffer.from(str, 'base64').toString('utf8');
      return (
        decoded.startsWith('<!DOCTYPE html>') || decoded.startsWith('<html')
      );
    } catch {
      return false;
    }
  }
}
