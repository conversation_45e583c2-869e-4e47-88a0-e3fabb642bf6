import { Injectable, RequestMethod } from '@nestjs/common';
import { ClassConstructor } from 'class-transformer';
import { FccListDataRestRequest } from '@/adapter/requests/fcc-listdata.request';
import { DateTime } from 'luxon';
import { MovementsListRequestDto } from '@dtos';
import { isEmpty } from 'lodash';

@Injectable()
export abstract class MovementsListRequest extends FccListDataRestRequest {
  abstract readonly listName: string;
  requestMethod: RequestMethod = RequestMethod.GET;
  payloadDto: ClassConstructor<MovementsListRequestDto> =
    MovementsListRequestDto;

  getRequestPayload(payload: MovementsListRequestDto) {
    const dateFrom = payload.dateFrom
      ? payload.dateFrom
      : DateTime.now().minus({ months: 3 }).toFormat('dd/MM/yyyy');
    const dateTo = payload.dateTo
      ? payload.dateTo
      : DateTime.now().toFormat('dd/MM/yyyy');

    const filters = {
      account_id: payload.accountId,
      create_date: encodeURIComponent(dateFrom),
      create_date2: encodeURIComponent(dateTo),
    } as any;

    if (payload.amountFrom) {
      filters.amountfrom = payload.amountFrom;
    }

    if (payload.amountTo) {
      filters.amountto = payload.amountTo;
    }

    if (payload.search && !isEmpty(payload.search)) {
      filters.descriptionf = encodeURIComponent('*' + payload.search + '*');
    }

    const start = {
      first: payload.first ?? 0,
      rows: payload.rows ?? 5,
    } as any;

    if (payload.sortField && payload.sortOrder) {
      const sortMap: Record<string, string> = {
        postDate: 'line@post_date',
        valueDate: 'line@value_date',
      };

      const mappedField = sortMap[payload.sortField];

      if (mappedField) {
        start.sortField = mappedField;
        start.sortOrder = payload.sortOrder ?? -1;
      }
    }

    const requestPayload = {
      Name: this.listName,
      FilterValues: encodeURIComponent(JSON.stringify(filters)),
      Start: encodeURIComponent(JSON.stringify(start)),
    };

    if (payload.sort) {
      requestPayload['sort'] = payload.sort;
    }

    return requestPayload;
  }
}
