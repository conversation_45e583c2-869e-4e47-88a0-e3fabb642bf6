import { FccRequestsEnum, RequestModuleEnum } from '@enums';
import { Request } from '@/adapter/decorators/request.decorator';
import { Injectable, RequestMethod } from '@nestjs/common';
import { FccListDataRestRequest } from '@/adapter/requests/fcc-listdata.request';
import { ClassConstructor, Expose } from 'class-transformer';
import { MoneyTransformer } from '@helpers';

export class SingleItemResponseDto {
  @Expose({ name: 'account_type' })
  accountType: string;

  @Expose()
  @MoneyTransformer()
  amount: string;

  @Expose({ name: 'RunningStatement@AvailableBalance@amt' })
  @MoneyTransformer()
  availableBalanceAmt: string;

  @Expose({ name: 'RunningStatement@AvailableBalance@value_date' })
  availableBalanceValueDate: string;

  @Expose()
  description: string;

  @Expose({ name: 'principal_amount' })
  @MoneyTransformer()
  principalAmount: string;

  @Expose({ name: 'account_no' })
  accountNo: string;

  @Expose({ name: 'cur_code' })
  curCode: string;

  @Expose({ name: 'account_id' })
  accountId: string;

  @Expose({ name: 'RunningStatement@LedgerBalance@amt' })
  @MoneyTransformer()
  statementLedgerBalanceAmt: string;

  @Expose({ name: 'RunningStatement@LedgerBalance@convertedAmt' })
  @MoneyTransformer()
  statementLedgerBalanceConvertedAmt: string;

  @Expose({ name: 'RunningStatement@AvailableBalance@convertedAmt' })
  @MoneyTransformer()
  availableBalanceConvertedAmt: string;

  @Expose({ name: 'acct_name' })
  acctName: string;

  @Expose({ name: 'owner_type' })
  ownerType: string;
}

@Injectable()
@Request(FccRequestsEnum.ACCOUNTS_HOME_SUMMARY, RequestModuleEnum.FCC)
export class AccountSummaryRequest extends FccListDataRestRequest {
  singleItemResponseDto: ClassConstructor<any> = SingleItemResponseDto;
  requestMethod: RequestMethod = RequestMethod.GET;

  getRequestPayload() {
    return {
      Name: 'core/listdef/customer/homeAccountSummary',
    };
  }
}
