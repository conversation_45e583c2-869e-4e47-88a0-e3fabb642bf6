import { Injectable, RequestMethod } from '@nestjs/common';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';
import { Request } from '@/adapter/decorators/request.decorator';
import {
  DetailedAccountTypeWithBalanceQueryEnum,
  FccRequestsEnum,
  RequestModuleEnum,
} from '@enums';
import { ClassConstructor } from 'class-transformer';
import { FccAccountListDto } from '@dtos';
import { IsEnum, IsOptional } from 'class-validator';

export class DetailedAccountListQueryDto {
  @IsEnum(DetailedAccountTypeWithBalanceQueryEnum)
  accountTypeForBalance: DetailedAccountTypeWithBalanceQueryEnum;

  @IsOptional()
  sort?: string;
}

@Injectable()
@Request(FccRequestsEnum.DETAILED_ACCOUNTS_LIST, RequestModuleEnum.FCC, 'v1')
export class DetailedAccountsListRequest extends FccRestRequest {
  requestMethod: RequestMethod = RequestMethod.GET;
  responseDto: ClassConstructor<any> = FccAccountListDto;

  ApiUrl(): string {
    return `dxp/accounts/balances-by-account-type`;
  }

  getRequestPayload(queryDto: DetailedAccountListQueryDto) {
    return {
      limit: 9999,
      accountTypeForBalance: queryDto.accountTypeForBalance,
      sort: queryDto.sort,
    };
  }
}
