import { FccRequestsEnum, RequestModuleEnum } from '@enums';
import { Request } from '@/adapter/decorators/request.decorator';
import { Injectable, RequestMethod } from '@nestjs/common';
import { ClassConstructor } from 'class-transformer';
import { MovementsListRequest } from '../common/movments-list.request';
import { AccountMovementsItemResponseDto } from '@dtos';

@Injectable()
@Request(FccRequestsEnum.ACCOUNT_MOVEMENTS_LIST, RequestModuleEnum.FCC, 'v1')
export class AccountMovementsListRequest extends MovementsListRequest {
  listName: string = '/cash/listdef/customer/AB/accountStatement';
  requestMethod: RequestMethod = RequestMethod.GET;
  singleItemResponseDto: ClassConstructor<AccountMovementsItemResponseDto> =
    AccountMovementsItemResponseDto;
}
