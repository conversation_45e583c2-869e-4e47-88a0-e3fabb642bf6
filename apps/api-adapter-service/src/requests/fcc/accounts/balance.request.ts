import { Request } from '@/adapter/decorators/request.decorator';
import { Injectable, RequestMethod } from '@nestjs/common';
import { RequestModuleEnum } from '@enums';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';
import { ClassConstructor, Expose } from 'class-transformer';
import { IsNotEmpty } from 'class-validator';

export class AccountBalanceRequestDTO {
  @Expose()
  @IsNotEmpty()
  accountId: number;
}

@Injectable()
@Request('account-balance', RequestModuleEnum.FCC)
export class AccountBalanceRequest extends FccRestRequest {
  ApiUrl(payload: AccountBalanceRequestDTO): string {
    return `accounts/${payload.accountId}/balances`;
  }
  payloadDto: ClassConstructor<any> = AccountBalanceRequestDTO;
  requestMethod: RequestMethod = RequestMethod.GET;
}
