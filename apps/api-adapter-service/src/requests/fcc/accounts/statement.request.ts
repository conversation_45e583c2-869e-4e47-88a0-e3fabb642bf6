import { Request } from '@/adapter/decorators/request.decorator';
import { Injectable, RequestMethod } from '@nestjs/common';
import { RequestModuleEnum } from '@enums';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';
import { Expose } from 'class-transformer';
import { FccRequestsEnum } from '@enums';
import { IsNotEmpty, IsString } from 'class-validator';

export class AccountStatementRequestDto {
  @Expose()
  @IsString()
  @IsNotEmpty()
  accountId: string;
}

@Injectable()
@Request(FccRequestsEnum.ACCOUNT_STATEMENT, RequestModuleEnum.FCC)
export class AccountStatementRequest extends FccRestRequest {
  requestMethod: RequestMethod = RequestMethod.GET;

  ApiUrl(): string {
    return `dxp/account-estatement`;
  }

  getRequestPayload(payload: AccountStatementRequestDto) {
    return {
      account: payload.accountId,
    };
  }
}
