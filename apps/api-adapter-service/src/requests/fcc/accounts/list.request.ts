import { Injectable, RequestMethod } from '@nestjs/common';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';
import { Request } from '@/adapter/decorators/request.decorator';
import {
  AccountsListContextEnum,
  CacheTTL,
  FccRequestsEnum,
  RequestModuleEnum,
} from '@enums';
import { ClassConstructor, Expose } from 'class-transformer';
import { IsEnum } from 'class-validator';
import { AccountsListContextMap } from '@constants';

export class TransferListRequestDto {
  @Expose()
  @IsEnum(AccountsListContextEnum)
  context: AccountsListContextEnum;
}
@Injectable()
@Request(FccRequestsEnum.ACCOUNTS_LIST, RequestModuleEnum.FCC, 'v1')
export class AccountsListRequest extends FccRestRequest {
  payloadDto: ClassConstructor<any> = TransferListRequestDto;
  requestMethod: RequestMethod = RequestMethod.GET;

  cacheEnabled: boolean = true;
  cacheTtl: number = CacheTTL.TenMinutes;

  ApiUrl(): string {
    return `dxp/accounts`;
  }

  getRequestPayload(payload: TransferListRequestDto) {
    return {
      limit: 9999,
      accountContext: AccountsListContextMap[payload.context],
    };
  }
}
