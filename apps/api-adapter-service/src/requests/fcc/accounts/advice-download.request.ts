import { Injectable, RequestMethod } from '@nestjs/common';
import { FccPortalRequest } from '@/adapter/requests/fcc-portal.request';
import {
  FccRequestsEnum,
  MovementAdviceTypeEnum,
  RequestModuleEnum,
  ResponseTypeEnum,
} from '@enums';
import { Request } from '@/adapter/decorators/request.decorator';
import { IsEnum, IsNotEmpty, IsString } from 'class-validator';
import { FccMovementAdviceTypesMap } from '@constants';

export class AdviceDownloadRequestDto {
  @IsString()
  @IsNotEmpty()
  @IsEnum(MovementAdviceTypeEnum)
  type: MovementAdviceTypeEnum;

  @IsString()
  @IsNotEmpty()
  boReferenceId: string;
}

@Injectable()
@Request(FccRequestsEnum.MOVEMENT_ADVICE_DOWNLOAD, RequestModuleEnum.FCC)
export class AdviceDownloadRequest extends FccPortalRequest {
  requestMethod: RequestMethod = RequestMethod.GET;
  responseType = ResponseTypeEnum.ArrayBuffer;

  getRequestPayload(payload: AdviceDownloadRequestDto): any {
    const tnxid = FccMovementAdviceTypesMap?.[payload.type];
    return {
      option: 'EXPORT_PDF_FULL',
      productcode: 'FT',
      referenceid: encodeURIComponent(payload.boReferenceId),
      tnxid: encodeURIComponent(tnxid),
    };
  }

  ApiUrl(): string {
    return `screen/ReportingPopup`;
  }

  format(data: any) {
    const buffer = Buffer.isBuffer(data) ? data : Buffer.from(data);
    return {
      response: 'success',
      data: buffer.toString('base64'),
    };
  }
}
