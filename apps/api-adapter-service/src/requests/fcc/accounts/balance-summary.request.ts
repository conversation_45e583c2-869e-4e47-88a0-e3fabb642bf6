import { Injectable, RequestMethod } from '@nestjs/common';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';
import { Request } from '@/adapter/decorators/request.decorator';
import { CacheTTL, FccRequestsEnum, RequestModuleEnum } from '@enums';
import { ClassConstructor, Expose, Type } from 'class-transformer';
import { FloatNumberTransformer, MoneyTransformer } from '@helpers';

class CurrencyAmount {
  @Expose()
  currencyCode: string;

  @Expose()
  @FloatNumberTransformer()
  amount: string;
}

class IndividualAccountBalance {
  @Expose()
  accountId: string;

  @Expose()
  accountNumber: string;

  @Expose()
  accountName: string | null;

  @Expose()
  currencyCode: string;

  @Expose()
  @FloatNumberTransformer()
  amount: string;

  @Expose()
  accountType: string;

  @Expose()
  principalAmount: string;

  @Expose()
  @MoneyTransformer()
  availableAmount: string | null;

  @Expose()
  @MoneyTransformer()
  ledgerBalance: string;

  @Expose()
  @MoneyTransformer()
  availableBalance: string;

  @Expose()
  @MoneyTransformer()
  ledgerBalanceConverted: string;

  @Expose()
  @MoneyTransformer()
  availableBalanceConverted: string;
}

class AccountsBalanceSummaryResponseDto {
  @Expose()
  @Type(() => CurrencyAmount)
  totalAccountBalanceOnBaseCurrency: CurrencyAmount;

  @Expose()
  @Type(() => CurrencyAmount)
  totalAccountBalanceBasedOnCurrency: CurrencyAmount[];

  @Expose()
  @Type(() => IndividualAccountBalance)
  individualAccountBalances: IndividualAccountBalance[];
}

@Injectable()
@Request(FccRequestsEnum.ACCOUNTS_BALANCE_SUMMARY, RequestModuleEnum.FCC, 'v1')
export class AccountsBalanceSummaryRequest extends FccRestRequest {
  requestMethod: RequestMethod = RequestMethod.POST;
  responseDto: ClassConstructor<any> = AccountsBalanceSummaryResponseDto;

  cacheEnabled: boolean = true;
  cacheTtl: number = CacheTTL.FiveMinutes;

  ApiUrl(): string {
    return `dxp/accountBalanceInfo/`;
  }

  getRequestPayload() {
    return {};
  }
}
