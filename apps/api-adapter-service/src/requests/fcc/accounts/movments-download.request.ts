import { Request } from '@/adapter/decorators/request.decorator';
import { Injectable } from '@nestjs/common';
import { RequestModuleEnum } from '@enums';
import { FccRequestsEnum } from '@enums';
import { MovementsDownloadRequest } from '../common/movments-download.request';

@Injectable()
@Request(FccRequestsEnum.ACCOUNT_MOVEMENTS_DOWNLOAD, RequestModuleEnum.FCC)
export class AccountMovementsDownloadRequest extends MovementsDownloadRequest {}
