import { FccRequestsEnum, RequestModuleEnum } from '@enums';
import { Request } from '@/adapter/decorators/request.decorator';
import { Injectable, RequestMethod } from '@nestjs/common';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';
import { ClassConstructor, Expose } from 'class-transformer';
import { IsNotEmpty } from 'class-validator';
import { FCCAccountDetailDto } from '@dtos';

export class AccountDetailsRequestDTO {
  @Expose()
  @IsNotEmpty()
  accountId: number;
}

@Injectable()
@Request(FccRequestsEnum.ACCOUNT_DETAILS, RequestModuleEnum.FCC)
export class AccountDetailsRequest extends FccRestRequest {
  ApiUrl(payload: AccountDetailsRequestDTO): string {
    return `dxp/accounts/${payload.accountId}`;
  }

  responseDto: ClassConstructor<any> = FCCAccountDetailDto;
  requestMethod: RequestMethod = RequestMethod.GET;
}
