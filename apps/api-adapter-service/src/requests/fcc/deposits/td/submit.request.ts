import { Injectable, RequestMethod } from '@nestjs/common';
import { Request } from '@/adapter/decorators/request.decorator';
import { FccRequestsEnum, RequestModuleEnum } from '@enums';
import * as snakecaseKeys from 'snakecase-keys';
import { DateTime } from 'luxon';
import { FccGenericTnxResponseDto, TermDepositSubmitRequestDto } from '@dtos';
import { FccGenricTnxRequest } from '../../common/generic-tnx.request';

@Injectable()
@Request(FccRequestsEnum.TERM_DEPOSIT_SUBMIT, RequestModuleEnum.FCC, 'v1')
export class TermDepositSubmitRequest extends FccGenricTnxRequest {
  requestMethod: RequestMethod = RequestMethod.POST;
  responseDto = FccGenericTnxResponseDto;

  ApiUrl(): string {
    return `genericsave`;
  }

  getRequestPayload(
    payload: TermDepositSubmitRequestDto & Record<string, any>,
  ) {
    const {
      attachments = [],
      refId,
      tnxId,

      startDate,

      autoForward,

      fccCode,
      tenorValue,
      tenorUnit,

      ...restPayload
    } = payload;

    const snakeCasePayload = snakecaseKeys(restPayload);

    const todayDate = DateTime.now().toFormat('dd/MM/yyyy');
    const operation = autoForward ? 'AUTO_FORWARD_SUBMIT' : 'SUBMIT';

    const valueDate = startDate || todayDate;

    return {
      common: {
        screen: 'TermDepositScreen',
        operation,
        option: '',
        mode: 'INITIATE',
        referenceid: refId,
        tnxid: tnxId,
        tnxtype: '01',
      },
      transaction: {
        //reauth_perform: 'Y',
        brch_code: '00001',

        //company_id: 112169,
        //company_name: 'TESTMATRIX',
        //tnx_id: 25062912658350,
        tnx_amt: payload.tdAmount,
        old_ctl_dttm: '',
        old_inp_dttm: '',
        //old_inp_dttm: '29/06/2025 11:55:18',

        //token: 'Wx2fO51cK2znqXGaTWatB87rTXKu/34tuZI90ESd/aA=',
        applicant_act_name1: payload.applicantActName,
        applicant_act_nickname: '',
        applicant_act_name: payload.applicantActName,
        applicant_act_cur_code: payload.applicantActCurCode,

        applicant_act_no: payload.applicantActNo,
        applicant_act_pab: 'Y',

        applicant_act_description: payload.applicantActDescription,
        AccountBalance: '',

        cust_ref_id: '',

        product_code: 'TD',
        sub_product_code: 'CSTD',

        value_date_unsigned: valueDate,
        appl_date: todayDate,

        display_entity_unsigned: '',
        sub_product_code_unsigned: 'CSTD',
        td_cur_code_unsigned: payload.currencyCode,

        td_amt_unsigned: payload.tdAmount,

        //ref_id: '****************',

        //tnx_type_code: 1,

        currency_res: false,
        placement_account_enabled: 'N',
        service_enabled: 'N',

        issuing_bank_name: 'CIB',
        issuing_bank_ref: '',
        issuing_bank_abbv_name: 'CIB',

        //applicant_reference: 'tTR4eCl9CovSqpG/h3a+Lw==',

        applicant_name: payload.applicantActName,
        applicant_address_line_1: '',
        applicant_address_line_2: '',
        applicant_dom: '',

        td_type: fccCode,
        selected_td_type: fccCode,
        selected_tenor_type: `${tenorValue}_${tenorUnit}`,

        //selected_maturity_type: payload.maturityType,

        maturity_mandatory: '',
        selected_td_cur: payload.currencyCode,

        //tnxId: 1,
        DueWtdrwDt: '',
        HoldCode: '',
        PledgeBranchCode: '',
        FundValDate: '',
        ReplCost: '',
        SibidSibor: '',
        WaiveIndicator: '',
        TranchNum: '',
        MinAmt: '',
        SgdDepAmt: '',
        SgdXchgRate: '',
        ConvertedAmt: '',
        CtrRate: '',
        FcfdAmt: '',
        bank_value_date: '',

        placement_act_name: '',
        placement_act_cur_code: '',
        placement_act_no: '',
        placement_act_description: '',
        placement_act_pab: '',

        value_date: valueDate,

        // maturity_date: payload.maturityDate,

        td_cur_code: payload.currencyCode,
        td_amt: payload.tdAmount,

        fx_master_currency: '',
        credit_act_name: payload.creditActName,
        credit_act_cur_code: payload.creditActCurCode,
        credit_act_no: payload.creditActNo,

        //revise
        credit_act_pab: 'Y',

        fx_rates_type: '01',

        fx_rates_type_temp: '',
        fx_exchange_rate: '',

        fx_exchange_rate_cur_code: '',
        fx_exchange_rate_amt: '',

        fx_tolerance_rate: '',
        fx_tolerance_rate_cur_code: '',
        fx_tolerance_rate_amt: '',
        fxBuyOrSell: '',

        fxTnxAmt: '',
        fx_rate_custom: '',
        fx_dealer_name: '',
        fx_nbr_contracts: '',

        value_date_term_number: tenorValue,
        value_date_term_code: tenorUnit,

        ...snakeCasePayload,
        attachments: {
          docId: attachments,
        },
      },
    };
  }
}
