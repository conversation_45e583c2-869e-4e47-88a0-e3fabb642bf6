import { FccRequestsEnum, RequestModuleEnum } from '@enums';
import { Request } from '@/adapter/decorators/request.decorator';
import { Injectable, RequestMethod } from '@nestjs/common';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';
import { ClassConstructor, Expose } from 'class-transformer';
import { IsNotEmpty } from 'class-validator';
import { FccTermDepositDetailsDto } from '@dtos';

export class AccountDetailsRequestDTO {
  @Expose()
  @IsNotEmpty()
  accountId: number;
}

@Injectable()
@Request(FccRequestsEnum.TERM_DEPOSIT_DETAILS, RequestModuleEnum.FCC)
export class TermDepositDetailsRequest extends FccRestRequest {
  ApiUrl(payload: AccountDetailsRequestDTO): string {
    return `accounts/${payload.accountId}`;
  }

  responseDto: ClassConstructor<any> = FccTermDepositDetailsDto;
  requestMethod: RequestMethod = RequestMethod.GET;
}
