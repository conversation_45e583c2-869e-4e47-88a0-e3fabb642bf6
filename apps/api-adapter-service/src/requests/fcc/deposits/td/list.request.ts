import { Injectable, RequestMethod } from '@nestjs/common';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';
import { Request } from '@/adapter/decorators/request.decorator';
import {
  DetailedAccountTypeWithBalanceQueryEnum,
  FccRequestsEnum,
  RequestModuleEnum,
} from '@enums';
import { ClassConstructor } from 'class-transformer';
import { DepositListQueryDto, FccAccountListDto } from '@dtos';
import { mapSortField, toJoinedString } from '@helpers';

@Injectable()
@Request(FccRequestsEnum.TERM_DEPOSIT_LIST, RequestModuleEnum.FCC, 'v1')
export class TermDepositListRequest extends FccRestRequest {
  requestMethod: RequestMethod = RequestMethod.GET;
  responseDto: ClassConstructor<any> = FccAccountListDto;

  ApiUrl(): string {
    return `dxp/accounts/balances-by-account-type`;
  }

  getRequestPayload(query: DepositListQueryDto) {
    const payload = {
      limit: 9999,
      accountTypeForBalance:
        DetailedAccountTypeWithBalanceQueryEnum.TERM_DEPOSIT,
      cur_code: query.curCode && toJoinedString(query.curCode),
      AmountRange: query.fromAmount || undefined,
      AmountRange2: query.toAmount || undefined,
      create_date: query.valueDate || undefined,
      create_date2: query.maturityDate || undefined,
    } as any;

    if (query.sortField && query.sortOrder) {
      payload.sort = mapSortField(query.sortField, query.sortOrder, {
        amount: 'principal_amount',
        maturity_date: 'end_date',
        value_date: 'start_date',
      });
    }

    return payload;
  }
}
