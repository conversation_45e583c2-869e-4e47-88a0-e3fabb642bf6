import { FccRequestsEnum, RequestModuleEnum } from '@enums';
import { Request } from '@/adapter/decorators/request.decorator';
import { Injectable, RequestMethod } from '@nestjs/common';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';

@Injectable()
@Request(FccRequestsEnum.DEPOSITS_MAP, RequestModuleEnum.FCC)
export class FccDepositsMapRequest extends FccRestRequest {
  requestMethod: RequestMethod = RequestMethod.GET;

  ApiUrl(): string {
    return `dxp/deposits/map`;
  }
}
