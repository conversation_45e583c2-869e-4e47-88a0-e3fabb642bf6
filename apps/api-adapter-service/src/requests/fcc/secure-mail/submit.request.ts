import { Injectable, RequestMethod } from '@nestjs/common';
import { Request } from '@/adapter/decorators/request.decorator';
import { FccRequestsEnum, RequestModuleEnum } from '@enums';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';
import * as snakecaseKeys from 'snakecase-keys';
import { DateTime } from 'luxon';
import { FccSEGenericTnxResponseDto } from '@dtos';
import { FccGenricTnxRequest } from '../common/generic-tnx.request';

@Injectable()
@Request(FccRequestsEnum.SECURE_MAIL_SUBMIT, RequestModuleEnum.FCC, 'v1')
export class SecureMailSubmitRequest extends FccGenricTnxRequest {
  requestMethod: RequestMethod = RequestMethod.POST;
  responseDto = FccSEGenericTnxResponseDto;

  ApiUrl(): string {
    return `genericsave`;
  }

  getRequestPayload(payload: any) {
    const { attachments = [], refId, tnxId, type, ...restPayload } = payload;
    const snakeCasePayload = snakecaseKeys(restPayload);

    const todayDate = DateTime.now().toFormat('dd/MM/yyyy');

    return {
      common: {
        screen: 'SecureEmailScreen',
        operation: 'SUBMIT',
        option: 'SE_GENERIC_FILE_UPLOAD',
        mode: 'INITIATE',
        referenceid: refId,
        tnxid: tnxId,
        tnxtype: '01',
        templateid: '',
      },
      transaction: {
        product_code: 'SE',
        sub_product_code: '',

        brch_code: '00001',

        bank_abbv_name: 'CIB',
        appl_date: todayDate,
        upload_file_type: type,

        margin_act_nickname: '',
        old_ctl_dttm: '',
        old_inp_dttm: '',
        prod_stat_code: '',
        date_time: '',
        se_type: '',
        margin_act_name: '',
        margin_act_cur_code: '',
        margin_act_no: '',
        margin_act_description: '',
        margin_act_pab: '',

        product: '',
        bank_code: '',
        branch_code: '',
        bank_name: '',
        branch_name: '',

        file_type_name: type,

        issuing_bank_name: 'CIB',
        issuing_bank_abbv_name: 'CIB',

        issuing_bank_customer_reference_temp: '',

        ...snakeCasePayload,
        attachments: {
          docId: attachments,
        },
      },
    };
  }
}
