import { Injectable, RequestMethod } from '@nestjs/common';
import { Request } from '@/adapter/decorators/request.decorator';
import { FccRequestsEnum, RequestModuleEnum } from '@enums';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';
import { FccSEGenericTnxResponseDto } from '@dtos';

@Injectable()
@Request(FccRequestsEnum.SECURE_MAIL_INITIATE, RequestModuleEnum.FCC, 'v1')
export class SecureMailInitiateRequest extends FccRestRequest {
  requestMethod: RequestMethod = RequestMethod.POST;
  responseDto = FccSEGenericTnxResponseDto;

  ApiUrl(): string {
    return `genericsave`;
  }

  getRequestPayload(): any {
    return {
      common: {
        screen: 'SecureEmailScreen',
        operation: 'SAVE',
        mode: 'DRAFT',
        tnxtype: '01',
      },
      transaction: {
        product_code: 'SE',
      },
    };
  }
}
