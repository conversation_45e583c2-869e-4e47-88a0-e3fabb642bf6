import { FccRequestsEnum, RequestModuleEnum } from '@enums';
import { Request } from '@/adapter/decorators/request.decorator';
import { Injectable, RequestMethod } from '@nestjs/common';
import { ClassConstructor } from 'class-transformer';
import { TransactionsListRequest } from '../transactions/list.request';
import { TransactionListItemDto } from '@dtos';

@Injectable()
@Request(FccRequestsEnum.SECURE_MAIL_REJECTED_LIST, RequestModuleEnum.FCC)
export class SecureMailRejectedListRequest extends TransactionsListRequest {
  listName: string = 'core/listdef/customer/dxpSERejectedList';
  singleItemResponseDto: ClassConstructor<any> = TransactionListItemDto;

  requestMethod: RequestMethod = RequestMethod.GET;
}
