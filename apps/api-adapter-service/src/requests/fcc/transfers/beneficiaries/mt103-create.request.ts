import { Injectable, RequestMethod } from '@nestjs/common';
import { Request } from '@/adapter/decorators/request.decorator';
import { FccRequestsEnum, RequestModuleEnum } from '@enums';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';
import { ClassConstructor, Expose } from 'class-transformer';
import { IsString, IsNotEmpty, IsOptional } from 'class-validator';

/**
 * Adapter DTO for creating an MT103 beneficiary.
 * Includes all fields sent to FCC, including defaults injected by the service.
 */
export class Mt103BeneficiaryCreateRequestDto {
  @Expose()
  @IsString()
  @IsNotEmpty()
  name!: string;

  @Expose()
  @IsString()
  @IsNotEmpty()
  accountNumber!: string;

  @Expose()
  @IsString()
  @IsNotEmpty()
  currency!: string;

  @Expose()
  @IsString()
  @IsNotEmpty()
  bicCode!: string;

  @Expose()
  @IsString()
  @IsNotEmpty()
  beneficiaryAddress!: string;

  @Expose()
  @IsString()
  @IsOptional()
  country?: string;

  @Expose()
  @IsString()
  @IsOptional()
  nickname?: string;

  @Expose()
  @IsString()
  @IsOptional()
  intermediaryBankBicCode?: string;

  @Expose()
  @IsString()
  @IsOptional()
  bankName?: string;

  @Expose()
  @IsString()
  @IsOptional()
  branchAddress?: string;

  @Expose()
  @IsString()
  @IsOptional()
  clearingSystem?: string;

  // injected by the service
  @Expose()
  entityShortName!: string;
  @Expose()
  preApproved!: boolean;
}

@Injectable()
@Request(
  FccRequestsEnum.TRANSFERS_BENEFICIARIES_CREATE_MT103,
  RequestModuleEnum.FCC,
  'v1',
)
export class Mt103BeneficiaryCreateRequest extends FccRestRequest {
  readonly payloadDto: ClassConstructor<any> = Mt103BeneficiaryCreateRequestDto;
  requestMethod: RequestMethod = RequestMethod.POST;

  ApiUrl(): string {
    return 'dxp/beneficiaries/mt103';
  }
}
