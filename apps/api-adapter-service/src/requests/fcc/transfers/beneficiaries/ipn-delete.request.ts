import { Injectable, RequestMethod } from '@nestjs/common';
import { Request } from '@/adapter/decorators/request.decorator';
import { FccRequestsEnum, RequestModuleEnum } from '@enums';
import { ClassConstructor, Expose } from 'class-transformer';
import { IsNotEmpty, IsString } from 'class-validator';
import { FccIpnRestRequest } from '../ipn.request';

export class IpnBeneficiaryDeleteRequestDto {
  @Expose()
  @IsString()
  @IsNotEmpty()
  beneficiaryId!: string;
}

@Injectable()
@Request(
  FccRequestsEnum.TRANSFERS_BENEFICIARIES_DELETE_IPN,
  RequestModuleEnum.FCC,
  'v1',
)
export class IpnBeneficiaryDeleteRequest extends FccIpnRestRequest {
  readonly payloadDto: ClassConstructor<any> = IpnBeneficiaryDeleteRequestDto;
  requestMethod: RequestMethod = RequestMethod.DELETE;

  ApiUrl(payload: IpnBeneficiaryDeleteRequestDto): string {
    return `ipn/beneficiaries/${encodeURIComponent(payload.beneficiaryId)}`;
  }
}
