import { Injectable, RequestMethod } from '@nestjs/common';
import { Request } from '@/adapter/decorators/request.decorator';
import { FccRequestsEnum, RequestModuleEnum } from '@enums';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';
import { ClassConstructor, Expose } from 'class-transformer';
import { IsString, IsOptional, IsNotEmpty } from 'class-validator';

export class Mt103BeneficiaryUpdateRequestDto {
  @Expose()
  @IsString()
  @IsNotEmpty()
  beneficiaryId!: string;

  @Expose()
  @IsString()
  @IsNotEmpty()
  name!: string;

  @Expose()
  @IsString()
  @IsNotEmpty()
  accountNumber!: string;

  @Expose()
  @IsString()
  @IsNotEmpty()
  currency!: string;

  @Expose()
  @IsString()
  @IsNotEmpty()
  bicCode!: string;

  @Expose()
  @IsString()
  @IsNotEmpty()
  beneficiaryAddress!: string;

  @Expose()
  @IsString()
  @IsOptional()
  country?: string;

  @Expose()
  @IsString()
  @IsOptional()
  nickname?: string;

  @Expose()
  @IsString()
  @IsOptional()
  intermediaryBankBicCode?: string;

  @Expose()
  @IsString()
  @IsOptional()
  bankName?: string;

  @Expose()
  @IsString()
  @IsOptional()
  branchAddress?: string;

  // injected by the service
  @Expose()
  entityShortName!: string;
  @Expose()
  preApproved!: boolean;
}

@Injectable()
@Request(
  FccRequestsEnum.TRANSFERS_BENEFICIARIES_UPDATE_MT103,
  RequestModuleEnum.FCC,
  'v1',
)
export class Mt103BeneficiaryUpdateRequest extends FccRestRequest {
  readonly payloadDto: ClassConstructor<any> = Mt103BeneficiaryUpdateRequestDto;
  requestMethod: RequestMethod = RequestMethod.PUT;

  ApiUrl(payload: Mt103BeneficiaryUpdateRequestDto): string {
    return `dxp/beneficiaries/mt103/${encodeURIComponent(payload.beneficiaryId)}`;
  }
}
