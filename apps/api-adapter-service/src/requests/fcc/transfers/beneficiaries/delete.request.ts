import { Injectable, RequestMethod } from '@nestjs/common';
import { Request } from '@/adapter/decorators/request.decorator';
import { FccRequestsEnum, RequestModuleEnum } from '@enums';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';
import { ClassConstructor, Expose } from 'class-transformer';
import { IsNotEmpty, IsString } from 'class-validator';

export class BeneficiaryDeleteRequestDto {
  @Expose()
  @IsString()
  @IsNotEmpty()
  beneficiaryId!: string;
}

@Injectable()
@Request(
  FccRequestsEnum.TRANSFERS_BENEFICIARIES_DELETE,
  RequestModuleEnum.FCC,
  'v1',
)
export class BeneficiaryDeleteRequest extends FccRestRequest {
  readonly payloadDto: ClassConstructor<any> = BeneficiaryDeleteRequestDto;
  requestMethod: RequestMethod = RequestMethod.DELETE;

  ApiUrl(payload: BeneficiaryDeleteRequestDto): string {
    return `dxp/beneficiaries/${encodeURIComponent(payload.beneficiaryId)}`;
  }
}
