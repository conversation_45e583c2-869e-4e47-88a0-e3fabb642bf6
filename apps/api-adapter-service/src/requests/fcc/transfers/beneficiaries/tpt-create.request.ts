import { Injectable, RequestMethod } from '@nestjs/common';
import { Request } from '@/adapter/decorators/request.decorator';
import { FccRequestsEnum, RequestModuleEnum } from '@enums';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';
import { ClassConstructor, Expose } from 'class-transformer';
import { IsString, IsNotEmpty, IsOptional } from 'class-validator';

/**
 * Adapter DTO for creating a TPT beneficiary.
 * Includes all fields sent to FCC, including defaults injected by the service.
 */
export class TptBeneficiaryCreateRequestDto {
  @Expose()
  @IsString()
  @IsNotEmpty()
  name!: string;

  @Expose()
  @IsString()
  @IsNotEmpty()
  accountNumber!: string;

  @Expose()
  @IsString()
  @IsNotEmpty()
  currency!: string;

  @Expose()
  @IsString()
  @IsOptional()
  nickname?: string;

  // injected by the service
  @Expose()
  entityShortName!: string;
  @Expose()
  preApproved!: boolean;
}

@Injectable()
@Request(
  FccRequestsEnum.TRANSFERS_BENEFICIARIES_CREATE_TPT,
  RequestModuleEnum.FCC,
  'v1',
)
export class TptBeneficiaryCreateRequest extends FccRestRequest {
  readonly payloadDto: ClassConstructor<any> = TptBeneficiaryCreateRequestDto;
  requestMethod: RequestMethod = RequestMethod.POST;

  ApiUrl(): string {
    return `dxp/beneficiaries/third-party-transfer`;
  }
}
