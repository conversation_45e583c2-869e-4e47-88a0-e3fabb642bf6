import { Injectable, RequestMethod } from '@nestjs/common';
import { Request } from '@/adapter/decorators/request.decorator';
import { FccRequestsEnum, RequestModuleEnum } from '@enums';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';
import { ClassConstructor, Expose, Transform, Type } from 'class-transformer';
import { IsNumber, IsOptional, Min, Max } from 'class-validator';

/**
 * DTO for GET beneficiaries list query params.
 */
export class BeneficiariesListRequestDto {
  @Expose()
  @IsOptional()
  subProductCode?: string;

  @Expose()
  @IsOptional()
  @Transform(({ value }) => (value === undefined ? 9999 : parseInt(value, 10)))
  @IsNumber()
  @Min(1)
  @Max(9999)
  limit?: number;

  @Expose()
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  @Max(1000)
  offset?: number;
}

@Injectable()
@Request(
  FccRequestsEnum.TRANSFERS_BENEFICIARIES_LIST,
  RequestModuleEnum.FCC,
  'v1',
)
export class BeneficiariesListRequest extends FccRestRequest {
  readonly payloadDto: ClassConstructor<any> = BeneficiariesListRequestDto;
  requestMethod: RequestMethod = RequestMethod.GET;

  ApiUrl(): string {
    return 'beneficiaries';
  }
}
