import { Injectable, RequestMethod } from '@nestjs/common';
import { Request } from '@/adapter/decorators/request.decorator';
import { FccRequestsEnum, RequestModuleEnum } from '@enums';
import { ClassConstructor, Expose } from 'class-transformer';
import { IsString, IsNotEmpty, IsOptional, IsBoolean } from 'class-validator';
import { FccIpnRestRequest } from '../ipn.request';

export class IpnBeneficiaryCreateRequestDto {
  @Expose()
  @IsString()
  @IsOptional()
  name?: string;

  @Expose()
  @IsString()
  @IsNotEmpty()
  accountNumber!: string;

  @Expose()
  @IsString()
  @IsOptional()
  nickname?: string;

  @Expose()
  @IsString()
  @IsOptional()
  description?: string;

  @Expose()
  @IsString()
  @IsNotEmpty()
  clearingSystem!: string;

  // Clearing system specific fields
  @Expose()
  @IsString()
  @IsOptional()
  beneficiaryBank?: string;

  @Expose()
  @IsString()
  @IsOptional()
  cardHolderName?: string;

  // Injected by service
  @Expose()
  @IsString()
  @IsNotEmpty()
  limitAmount!: string;

  @Expose()
  @IsString()
  @IsNotEmpty()
  limitCurrency!: string;

  @Expose()
  @IsString()
  @IsNotEmpty()
  currency!: string;

  @Expose()
  @IsString()
  entityShortName!: string;

  @Expose()
  @IsBoolean()
  preApproved!: boolean;

  @Expose()
  @IsBoolean()
  active!: boolean;

  @Expose()
  @IsString()
  @IsOptional()
  country?: string;
}

@Injectable()
@Request(
  FccRequestsEnum.TRANSFERS_BENEFICIARIES_CREATE_IPN,
  RequestModuleEnum.FCC,
  'v1',
)
export class IpnBeneficiaryCreateRequest extends FccIpnRestRequest {
  readonly payloadDto: ClassConstructor<any> = IpnBeneficiaryCreateRequestDto;
  requestMethod: RequestMethod = RequestMethod.POST;

  ApiUrl(): string {
    return 'ipn/beneficiaries/ipn';
  }
}
