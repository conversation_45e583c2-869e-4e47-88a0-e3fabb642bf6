import { Injectable, RequestMethod } from '@nestjs/common';
import { Request } from '@/adapter/decorators/request.decorator';
import { FccRequestsEnum, RequestModuleEnum } from '@enums';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';
import { ClassConstructor, Expose } from 'class-transformer';

export class IpnBeneficiariesListRequestDto {}

/**
 * Response DTO matching FCC response
 */
export class IpnBeneficiariesListResponseDto {
  @Expose()
  data!: any[]; // Array of beneficiaries

  @Expose()
  status?: string;

  @Expose()
  code?: string;

  @Expose()
  message?: string;

  @Expose()
  timestamp?: string;
}

@Injectable()
@Request(
  FccRequestsEnum.TRANSFERS_BENEFICIARIES_LIST_IPN,
  RequestModuleEnum.FCC,
  'v1',
)
export class IpnBeneficiariesListRequest extends FccRestRequest {
  readonly responseDto: ClassConstructor<any> = IpnBeneficiariesListResponseDto;
  requestMethod: RequestMethod = RequestMethod.GET;

  ApiUrl(): string {
    return 'ipn/beneficiaries/list';
  }
}
