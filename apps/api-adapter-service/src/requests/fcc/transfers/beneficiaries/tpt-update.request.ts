import { Injectable, RequestMethod } from '@nestjs/common';
import { Request } from '@/adapter/decorators/request.decorator';
import { FccRequestsEnum, RequestModuleEnum } from '@enums';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';
import { ClassConstructor, Expose } from 'class-transformer';
import { IsString, IsNotEmpty, IsOptional } from 'class-validator';

export class TptBeneficiaryUpdateRequestDto {
  @Expose()
  @IsString()
  @IsNotEmpty()
  beneficiaryId!: string;

  @Expose()
  @IsString()
  @IsNotEmpty()
  name!: string;

  @Expose()
  @IsString()
  @IsNotEmpty()
  accountNumber!: string;

  @Expose()
  @IsString()
  @IsNotEmpty()
  currency!: string;

  @Expose()
  @IsString()
  @IsOptional()
  nickname?: string;

  // injected by the service
  @Expose()
  entityShortName!: string;
  @Expose()
  preApproved!: boolean;
}

@Injectable()
@Request(
  FccRequestsEnum.TRANSFERS_BENEFICIARIES_UPDATE_TPT,
  RequestModuleEnum.FCC,
  'v1',
)
export class TptBeneficiaryUpdateRequest extends FccRestRequest {
  readonly payloadDto: ClassConstructor<any> = TptBeneficiaryUpdateRequestDto;
  requestMethod: RequestMethod = RequestMethod.PUT;

  ApiUrl(payload: TptBeneficiaryUpdateRequestDto): string {
    return `dxp/beneficiaries/third-party-transfer/${encodeURIComponent(payload.beneficiaryId)}`;
  }
}
