import { Injectable, RequestMethod } from '@nestjs/common';
import { Request } from '@/adapter/decorators/request.decorator';
import { FccRequestsEnum, RequestModuleEnum } from '@enums';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';
import * as snakecaseKeys from 'snakecase-keys';
import { DateTime } from 'luxon';
import { FccFtGenericTnxResponseDto } from '@dtos';

@Injectable()
@Request(FccRequestsEnum.INTERNAL_TRANSFER_SUBMIT, RequestModuleEnum.FCC, 'v1')
export class InternalTransferSubmitRequest extends FccRestRequest {
  requestMethod: RequestMethod = RequestMethod.POST;
  responseDto = FccFtGenericTnxResponseDto;

  ApiUrl(): string {
    return `genericsave`;
  }

  getRequestPayload(payload: any) {
    const {
      attachments = [],
      refId,
      tnxId,
      transferDate,
      ...restPayload
    } = payload;
    const snakeCasePayload = snakecaseKeys(restPayload);
    const todayDate = DateTime.now().toFormat('dd/MM/yyyy');

    return {
      common: {
        screen: 'FundTransferScreen',
        operation: 'SUBMIT',
        option: '',
        mode: 'INITIATE',
        referenceid: refId,
        tnxid: tnxId,
        tnxtype: '01',
      },
      transaction: {
        swiftBicCodeRegexValue:
          '^[A-Z]{6,6}[A-Z2-9][A-NP-Z0-9]([A-Z0-9]{3,3}){0,1}$',

        old_ctl_dttm: '',
        old_inp_dttm: '',
        ft_type: '01',

        bulk_template_id: '',
        bulk_ref_id: '',
        bulk_tnx_id: '',

        issuing_bank_name: 'CIB',
        issuing_bank_abbv_name: 'CIB',
        issuing_bank_iso_code: '',

        counterparty_id: '',
        beneficiary_mode: '',

        product_code: 'FT',
        sub_product_code: 'INT',

        modifiedBeneficiary: '',

        pre_approved: '',
        swiftregexValue: "^[a-zA-Z0-9 :,/'?.+()\r\n-]*$",

        pre_approved_status: '',
        fx_tolerance_rate_value: '',
        applicant_act_nickname: '',
        beneficiary_nickname: '',
        beneficiary_act_nickname: '',
        applicant_act_name1: '',
        beneficiary_account_name1: '',
        fx_tolerance_rate_amt_value: '',
        intermediary_flag: '',
        fx_rates_type_temp: '',
        fx_master_currency: '',
        reauth_perform: 'Y',

        AccountBalance: '0',
        recurring_flag: 'N',

        appl_date: todayDate,
        template_id: '',
        recurring_start_date: '',
        recurring_frequency: '',
        recurring_on: '',
        recurring_end_date: '',
        appl_date_hidden: todayDate,
        allow_both_fields: 'N',
        recurring_payment_enabled: 'N',

        bk_sub_product_code: '',

        iss_date: transferDate || todayDate,
        sub_product_code_custom: '',
        file_upload_require: '',
        iss_date_unsigned: '',
        sub_product_code_unsigned: 'INT',
        display_entity_unsigned: '',
        ft_cur_code_unsigned: '',
        applicant_act_cur_code_unsigned: '',
        ft_amt_unsigned: '',
        recurring_start_date_unsigned: '',
        recurring_end_date_unsigned: '',

        base_cur_code: payload.beneficiaryActCurCode,

        regexValue: '^[A-Za-z][A-Za-z0-9 _]*$',
        allowedProducts: 'TRSRY,DD,PICO,PIDD,INT,TPT',
        product_type: 'INT',
        cust_ref_id: '',
        beneficiary_reference: '',

        fx_rates_type: '01',
        fx_exchange_rate: '',
        fx_exchange_rate_cur_code: '',
        fx_exchange_rate_amt: '',
        fx_tolerance_rate: '',
        fx_tolerance_rate_cur_code: '',
        fx_tolerance_rate_amt: '',

        fxBuyOrSell: '',
        fxTnxAmt: '',
        fx_rate_custom: '',
        fx_dealer_name: '',

        fx_nbr_contracts: '',
        notify_beneficiary: 'N',
        notify_beneficiary_choice: '',
        notify_beneficiary_email: '',
        free_format_text: '',

        applicant_act_pab: 'Y',

        tnx_amt: payload.ftAmt,
        tnx_cur_code: payload.ftCurCode,

        ...snakeCasePayload,
        attachments: {
          docId: attachments,
        },
      },
    };
  }
}
