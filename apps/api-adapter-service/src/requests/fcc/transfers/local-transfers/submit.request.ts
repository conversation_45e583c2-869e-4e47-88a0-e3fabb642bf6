import { Injectable, RequestMethod } from '@nestjs/common';
import { Request } from '@/adapter/decorators/request.decorator';
import { FccRequestsEnum, RequestModuleEnum } from '@enums';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';
import * as snakecaseKeys from 'snakecase-keys';
import { DateTime } from 'luxon';
import { FccFtGenericTnxResponseDto } from '@dtos';

/**
 * Handles local transfer (TPT) submission requests to FCC
 */
@Injectable()
@Request(FccRequestsEnum.LOCAL_TRANSFER_SUBMIT, RequestModuleEnum.FCC, 'v1')
export class LocalTransferSubmitRequest extends FccRestRequest {
  requestMethod: RequestMethod = RequestMethod.POST;
  responseDto = FccFtGenericTnxResponseDto;

  ApiUrl(): string {
    return `genericsave`;
  }

  /**
   * Transforms the payload for FCC API format
   * @param payload Request payload from service
   * @returns Formatted payload for FCC API
   */
  getRequestPayload(payload: any) {
    const {
      attachments = [],
      refId,
      tnxId,
      transferDate,
      ...restPayload
    } = payload;
    const snakeCasePayload = snakecaseKeys(restPayload);

    const todayDate = DateTime.now().toFormat('dd/MM/yyyy');

    return {
      common: {
        screen: 'FundTransferScreen',
        operation: 'SUBMIT',
        option: '',
        mode: 'INITIATE',
        referenceid: refId,
        tnxid: tnxId,
        tnxtype: '01',
      },
      transaction: {
        // Standard validation patterns
        swiftBicCodeRegexValue:
          '^[A-Z]{6,6}[A-Z2-9][A-NP-Z0-9]([A-Z0-9]{3,3}){0,1}$',
        swiftregexValue: "^[a-zA-Z0-9 :,/'?.+()\r\n-]*$",
        regexValue: '^[A-Za-z][A-Za-z0-9 _]*$',

        // Transaction metadata
        old_ctl_dttm: '',
        old_inp_dttm: '',
        ft_type: '01',

        // Bulk transaction fields (empty for individual transfers)
        bulk_template_id: '',
        bulk_ref_id: '',
        bulk_tnx_id: '',

        // Bank information
        issuing_bank_name: 'CIB',
        issuing_bank_abbv_name: 'CIB',
        issuing_bank_iso_code: '',

        // Counterparty information
        counterparty_id: '',
        beneficiary_mode: '02',

        // Product codes
        product_code: 'FT',
        sub_product_code: 'TPT',
        product_type: 'TPT',
        allowedProducts: 'TRSRY,DD,PICO,PIDD,INT,TPT',

        // Beneficiary modification tracking
        modifiedBeneficiary: '',

        // Pre-approval settings
        pre_approved_status: 'Y',

        // Account nicknames (empty for standard transfers)
        applicant_act_nickname: '',
        beneficiary_nickname: '',
        beneficiary_act_nickname: '',
        applicant_act_name1: '',
        beneficiary_account_name1: '',

        // FX rate settings
        fx_tolerance_rate_value: '',
        fx_tolerance_rate_amt_value: '',
        intermediary_flag: '',
        fx_rates_type_temp: '',
        fx_master_currency: '',

        // Authentication settings
        reauth_perform: 'Y',

        // Account balance placeholder
        AccountBalance: '0',

        // Recurring payment settings (disabled by default)
        recurring_flag: 'N',
        recurring_payment_enabled: 'N',
        appl_date: todayDate,
        template_id: '',
        recurring_start_date: '',
        recurring_frequency: '',
        recurring_on: '',
        recurring_end_date: '',
        appl_date_hidden: todayDate,
        allow_both_fields: 'N',

        // Product code backups
        bk_sub_product_code: '',
        sub_product_code_custom: '',

        // File upload settings
        file_upload_require: '',

        // Unsigned fields (used for validation)
        iss_date: transferDate || todayDate,
        iss_date_unsigned: '',
        sub_product_code_unsigned: 'TPT',
        display_entity_unsigned: '',
        ft_cur_code_unsigned: '',
        applicant_act_cur_code_unsigned: '',
        ft_amt_unsigned: '',
        recurring_start_date_unsigned: '',
        recurring_end_date_unsigned: '',

        // Currency settings
        base_cur_code: payload.ftCurCode,

        // Reference IDs
        cust_ref_id: '',
        beneficiary_reference: '',

        // FX rate configuration
        fx_rates_type: '01',
        fx_exchange_rate: '',
        fx_exchange_rate_cur_code: '',
        fx_exchange_rate_amt: '',
        fx_tolerance_rate: '',
        fx_tolerance_rate_cur_code: '',
        fx_tolerance_rate_amt: '',
        fxBuyOrSell: '',
        fxTnxAmt: '',
        fx_rate_custom: '',
        fx_dealer_name: '',
        fx_nbr_contracts: '',

        // Notification settings
        notify_beneficiary: 'N',
        notify_beneficiary_choice: '',
        notify_beneficiary_email: '',
        free_format_text: '',

        // Applicant account settings
        applicant_act_pab: 'Y',

        // Transaction amount details
        tnx_amt: payload.ftAmt,
        tnx_cur_code: payload.ftCurCode,

        // Beneficiary email fields
        bene_email_1: '',
        bene_email_2: '',

        // Beneficiary account details
        bo_account_id: '',
        bo_account_type: '',
        bo_account_currency: '',
        bo_branch_code: '',
        bo_product_type: '',

        // Merge remaining payload fields
        ...snakeCasePayload,
        attachments: {
          docId: attachments,
        },
      },
    };
  }
}
