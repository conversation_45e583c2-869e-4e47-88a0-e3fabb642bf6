import { Injectable, RequestMethod } from '@nestjs/common';
import { Request } from '@/adapter/decorators/request.decorator';
import { FccRequestsEnum, RequestModuleEnum } from '@enums';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';
import { FccFtGenericTnxResponseDto } from '@dtos';

/**
 * Handles local transfer (TPT) initiation requests to FCC
 */
@Injectable()
@Request(FccRequestsEnum.LOCAL_TRANSFER_INITIATE, RequestModuleEnum.FCC, 'v1')
export class LocalTransferInitiateRequest extends FccRestRequest {
  requestMethod: RequestMethod = RequestMethod.POST;
  responseDto = FccFtGenericTnxResponseDto;

  ApiUrl(): string {
    return `genericsave`;
  }

  /**
   * Transforms the payload for FCC API format
   * @returns Formatted payload for FCC API
   */
  getRequestPayload(): any {
    return {
      common: {
        screen: 'FundTransferScreen',
        operation: 'SAVE',
        mode: 'DRAFT',
        tnxtype: '01',
      },
      transaction: {
        product_code: 'FT',
        sub_product_code: 'TPT',
      },
    };
  }
}
