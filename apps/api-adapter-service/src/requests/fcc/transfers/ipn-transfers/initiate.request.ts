import { Injectable, RequestMethod } from '@nestjs/common';
import { Request } from '@/adapter/decorators/request.decorator';
import { FccRequestsEnum, RequestModuleEnum } from '@enums';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';
import { FccFtGenericTnxResponseDto } from '@dtos';

@Injectable()
@Request(FccRequestsEnum.IPN_TRANSFER_INITIATE, RequestModuleEnum.FCC, 'v1')
export class IpnTransferInitiateRequest extends FccRestRequest {
  requestMethod: RequestMethod = RequestMethod.POST;
  responseDto = FccFtGenericTnxResponseDto;

  ApiUrl(): string {
    return `ipn/ft/initiate`;
  }

  getRequestPayload(): any {
    return {
      common: {
        screen: 'FundTransferScreen',
        operation: 'SCRATCH_IPN',
        mode: 'DRAFT',
        tnxtype: '01',
      },
    };
  }

  format(data: any) {
    return super.format(data.ft_tnx_record);
  }
}
