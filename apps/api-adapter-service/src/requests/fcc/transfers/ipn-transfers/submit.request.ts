import { Injectable, RequestMethod } from '@nestjs/common';
import { Request } from '@/adapter/decorators/request.decorator';
import { FccRequestsEnum, RequestModuleEnum } from '@enums';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';
import { DateTime } from 'luxon';
import * as snakecaseKeys from 'snakecase-keys';
import { IpnConstants } from '@constants';

@Injectable()
@Request(FccRequestsEnum.IPN_TRANSFER_SUBMIT, RequestModuleEnum.FCC, 'v1')
export class IpnTransferSubmitRequest extends FccRestRequest {
  requestMethod: RequestMethod = RequestMethod.POST;

  ApiUrl(): string {
    return `ipn/ft/submit`;
  }

  /**
   * Transforms the payload to FCC format
   * @param payload Request payload from service
   * @returns Formatted payload for FCC API
   */
  getRequestPayload(payload: any) {
    const {
      attachments = [],
      refId,
      tnxId,
      transferDate,
      clearingSystem,
      ...restPayload
    } = payload;
    const snakeCasePayload = snakecaseKeys(restPayload);

    const todayDate = DateTime.now().toFormat('dd/MM/yyyy');

    return {
      requestData: {
        ref_id: refId,
        tnx_id: tnxId,

        // Product details
        product_code: IpnConstants.PRODUCT.CODE,
        sub_product_code: IpnConstants.PRODUCT.SUB_PRODUCT_CODE,
        product_type: IpnConstants.PRODUCT.PRODUCT_TYPE,
        tnxtype: IpnConstants.PRODUCT.TNX_TYPE,

        // Amount & currency
        ft_amt: payload.ftAmt,
        ft_cur_code: payload.ftCurCode,

        // Beneficiary details
        beneficiaryId: payload.beneficiaryId?.toString(),
        beneficiary_account: payload.beneficiaryAccount,
        beneficiary_name: payload.beneficiaryName,

        // Company details
        company_id: payload.companyId,
        company_name: payload.companyName,

        // Applicant details
        applicant_name: payload.applicantName,
        applicant_reference: payload.applicantReference,
        applicant_act_no: payload.applicantActNo,
        applicant_act_name: payload.applicantActName,
        applicant_act_cur_code: payload.applicantActCurCode,
        applicant_act_description: payload.applicantActDescription,
        applicant_act_pab: IpnConstants.APPROVAL.APPLICANT_ACT_PAB,

        // Branch & bank details
        brch_code: payload.brchCode,
        issuing_bank_name: IpnConstants.BANK.ISSUING_BANK_NAME,
        issuing_bank_abbv_name: IpnConstants.BANK.ISSUING_BANK_ABBV_NAME,

        // Commission
        commissionFor: payload.commissionFor,
        commissionType: payload.commissionType,

        orderingCustomerBank: payload.orderingCustomerBank,
        pmtMethod: payload.pmtMethod,
        chargeDebitAccount: payload.chargeDebitAccount,

        // IPN Clearing System
        clearing_system: clearingSystem,
        beneficiary_clearing_system_id:
          IpnConstants.DEFAULTS.BENEFICIARY_CLEARING_SYSTEM_ID,
        clearing_system_id: IpnConstants.DEFAULTS.CLEARING_SYSTEM_ID,

        transferReason: payload.transferReason,

        // Dates
        iss_date: transferDate || todayDate,
        appl_date: todayDate,

        // Reauth
        reauth_password: payload.reauthPassword,
        reauth_perform: IpnConstants.REAUTH.PERFORM,

        pre_approved_status: IpnConstants.APPROVAL.PRE_APPROVED_STATUS,
        pre_approved: IpnConstants.APPROVAL.PRE_APPROVED,

        token: payload.token,

        entity: payload.entity || IpnConstants.DEFAULTS.ENTITY,

        ...snakeCasePayload,
        attachments: {
          docId: attachments,
        },
      },
      userData: {
        userSelectedLanguage: payload.userLanguage || 'en',
      },
    };
  }
}
