import { Injectable, RequestMethod } from '@nestjs/common';
import { Request } from '@/adapter/decorators/request.decorator';
import { FccRequestsEnum, RequestModuleEnum } from '@enums';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';
import { ClassConstructor, Type, Expose } from 'class-transformer';
import { IsString, IsOptional, IsNumber, Max, Min } from 'class-validator';

export class IpnAccountsListQueryDto {
  @Expose()
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(999)
  limit?: number;

  @Expose()
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  @Max(999)
  offset?: number;

  @Expose()
  @IsString()
  @IsOptional()
  ccy_codes?: string;
}

@Injectable()
@Request(FccRequestsEnum.IPN_ACCOUNTS_LIST, RequestModuleEnum.FCC, 'v1')
export class IpnAccountsListRequest extends FccRestRequest {
  readonly payloadDto: ClassConstructor<any> = IpnAccountsListQueryDto;
  requestMethod: RequestMethod = RequestMethod.GET;

  ApiUrl(): string {
    return `ipn/ft/accounts`;
  }

  /**
   * Formats params to FCC
   * @param payload Query params from service
   * @returns Query params object for the request
   */
  getRequestPayload(payload: IpnAccountsListQueryDto) {
    return {
      ...(payload.limit && { limit: payload.limit.toString() }),
      ...(payload.offset && { offset: payload.offset.toString() }),
      ...(payload.ccy_codes && { ccy_codes: payload.ccy_codes }),
    };
  }
}
