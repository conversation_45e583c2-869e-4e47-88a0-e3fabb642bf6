import { Injectable, RequestMethod } from '@nestjs/common';
import { Request } from '@/adapter/decorators/request.decorator';
import { FccRequestsEnum, RequestModuleEnum } from '@enums';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';
import { ClassConstructor, Expose } from 'class-transformer';
import { IsString, IsNotEmpty, IsOptional } from 'class-validator';

export class IpnTransferSimulateRequestDto {
  @Expose()
  @IsString()
  @IsNotEmpty()
  fromAccount: string;

  @Expose()
  @IsString()
  @IsNotEmpty()
  amount: string;

  @Expose()
  @IsString()
  @IsNotEmpty()
  currency: string;

  @Expose()
  @IsString()
  @IsNotEmpty()
  reason: string;

  @Expose()
  @IsString()
  @IsOptional()
  transferType?: string;

  @Expose()
  @IsString()
  @IsOptional()
  commissionCode?: string;

  @Expose()
  @IsString()
  @IsOptional()
  commissionType?: string;

  @Expose()
  @IsString()
  @IsOptional()
  beneficiaryId?: string;
}

@Injectable()
@Request(FccRequestsEnum.IPN_TRANSFER_SIMULATE, RequestModuleEnum.FCC, 'v1')
export class IpnTransferSimulateRequest extends FccRestRequest {
  readonly payloadDto: ClassConstructor<any> = IpnTransferSimulateRequestDto;
  requestMethod: RequestMethod = RequestMethod.POST;

  ApiUrl(): string {
    return `ipn/ft/simulate`;
  }

  /**
   * Transforms the payload to FCC format
   * @param payload Request payload from service
   * @returns Formatted payload for FCC
   */
  getRequestPayload(payload: IpnTransferSimulateRequestDto) {
    return {
      fromAccount: payload.fromAccount,
      amount: payload.amount,
      reason: payload.reason,
      currency: payload.currency,
      transferType: payload.transferType,
      commissionCode: payload.commissionCode,
      commissionType: payload.commissionType,
      beneficiaryId: payload.beneficiaryId,
    };
  }
}
