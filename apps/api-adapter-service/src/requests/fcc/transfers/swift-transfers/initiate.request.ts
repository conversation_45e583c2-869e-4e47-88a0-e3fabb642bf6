import { Injectable, RequestMethod } from '@nestjs/common';
import { Request } from '@/adapter/decorators/request.decorator';
import { FccRequestsEnum, RequestModuleEnum } from '@enums';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';
import { FccFtGenericTnxResponseDto } from '@dtos';

@Injectable()
@Request(FccRequestsEnum.SWIFT_TRANSFER_INITIATE, RequestModuleEnum.FCC, 'v1')
export class SwiftTransferInitiateRequest extends FccRestRequest {
  requestMethod: RequestMethod = RequestMethod.POST;
  responseDto = FccFtGenericTnxResponseDto;

  ApiUrl(): string {
    return `genericsave`;
  }

  getRequestPayload(): any {
    return {
      common: {
        screen: 'FundTransferScreen',
        operation: 'SAVE',
        mode: 'DRAFT',
        tnxtype: '01',
      },
      transaction: {
        product_code: 'FT',
        sub_product_code: 'MT103',
      },
    };
  }
}
