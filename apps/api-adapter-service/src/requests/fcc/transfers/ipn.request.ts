import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';
import { FccErrorCodeMap } from '@constants';

export abstract class FccIpnRestRequest extends FccRestRequest {
  errorFormat(data: any, options: any) {
    const statusCode = 400;
    if (data?.status === 'ERROR' && data?.code) {
      if (FccErrorCodeMap?.[data?.code]) {
        return {
          statusCode,
          errorCode: FccErrorCodeMap[data?.code],
        };
      }

      return {
        statusCode,
        errorCode: data.code,
      };
    }
    return super.errorFormat(data, options);
  }
}
