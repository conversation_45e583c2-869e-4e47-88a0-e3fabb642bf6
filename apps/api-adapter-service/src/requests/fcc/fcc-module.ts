import { Modu<PERSON> } from '@nestjs/common';
import { AccountBalanceRequest } from './accounts/balance.request';
import { MyPendingTransactionsRequest } from './transactions/my-pending.request';
import { AllTransactionsRequest } from './transactions/all-transactions.request';
import { RejectTransactionRequest } from './transactions/reject.request';
import { TransactionDetailsRequest } from './transactions/details.request';
import { AccountsListRequest } from '@/requests/fcc/accounts/list.request';
import { FxRateRequest } from '@/requests/fcc/global/fx-rate.request';
import { InternalTransferInitiateRequest } from '@/requests/fcc/transfers/internal-transfers/initiate.request';
import { UploadAttachmentRequest } from '@/requests/fcc/attachments/upload.request';
import { AuthTypeRequest } from '@/requests/fcc/global/auth-type.request';
import { ApproveTransactionRequest } from './transactions/approve.request';
import { UserPermissionsRequest } from './users/user-permission.request';
import { BeneficiariesListRequest } from './transfers/beneficiaries/list.request';
import { CountryListRequest } from './static-data/country-list.request';
import { CurrencyListRequest } from './static-data/currency-list.request';
import { MutualFundSummaryRequest } from './mutual-fund/summary.request';
import { LoanAccountSummaryRequest } from './loans/summary.request';
import { AccountDetailsRequest } from './accounts/account-details';
import { BeneficiaryDeleteRequest } from './transfers/beneficiaries/delete.request';
import { TptBeneficiaryCreateRequest } from './transfers/beneficiaries/tpt-create.request';
import { TptBeneficiaryUpdateRequest } from './transfers/beneficiaries/tpt-update.request';
import { Mt103BeneficiaryCreateRequest } from './transfers/beneficiaries/mt103-create.request';
import { Mt103BeneficiaryUpdateRequest } from './transfers/beneficiaries/mt103-update.request';
import { SwiftDetailsRequest } from './banks/swift-details.request';
import { UserDetailsRequest } from './users/user-details.request';
import { InternalTransferSubmitRequest } from './transfers/internal-transfers/submit.request';
import { UserReauthenticationTypeRequest } from './users/reauthentication-type.request';
import { ClearingSystemRequestRequest } from './banks/clearing-system.request';
import { LocalTransferInitiateRequest } from './transfers/local-transfers/initiate.request';
import { LocalTransferSubmitRequest } from './transfers/local-transfers/submit.request';
import { SwiftTransferInitiateRequest } from './transfers/swift-transfers/initiate.request';
import { SwiftTransferSubmitRequest } from './transfers/swift-transfers/submit.request';
import { AccountSummaryRequest } from './accounts/home-summary.request';
import { TransactionJourneyRequest } from './transactions/journey.request';
import { DeleteAttachmentRequest } from './attachments/delete.request';
import { ListAttachmentRequest } from './attachments/list.request';
import { BanksListRequest } from './static-data/banks-list.request';
import { TransactionHistoryRequest } from './transactions/history.request';
import { ConfigurationDetailsRequest } from './global/config-details.request';
import { AccountsBalanceSummaryRequest } from './accounts/balance-summary.request';
import { FxConvertRequest } from './global/fx-convert.request';
import { SecureMailInitiateRequest } from '@/requests/fcc/secure-mail/initiate.request';
import { SecureMailSubmitRequest } from '@/requests/fcc/secure-mail/submit.request';
import { TermDepositInitiateRequest } from './deposits/td/initiate.request';
import { TermDepositSubmitRequest } from './deposits/td/submit.request';
import { CutOffTimeRequest } from './global/cut-off-time.request';
import { UsersListRequest } from './static-data/users-list.request';
import { CreditCardSummaryRequest } from './cards/credit/summary.request';
import { SecureMailRejectedListRequest } from './secure-mail/rejected-list.request';
import { DetailedAccountsListRequest } from '@/requests/fcc/accounts/detailed-list.request';
import { SecureMailApprovedListRequest } from './secure-mail/approved-list.request';
import { AccountStatementRequest } from './accounts/statement.request';
import { AccountMovementsListRequest } from './accounts/movments-list.request';
import { AccountMovementsDownloadRequest } from './accounts/movments-download.request';
import { ChangeLanguageRequest } from './users/change-language.request';
import { AdviceDownloadRequest } from './accounts/advice-download.request';
import { CreditCardListRequest } from './cards/credit/list.request';
import { CreditCardMovementsListRequest } from './cards/credit/movements-list.request';
import { ChangeCurrencyRequest } from './users/change-currency.request';
import { UploadDocumentRequest } from './attachments/upload-document.request';
import { DownloadAttachmentRequest } from './attachments/download.request';
import { DownloadTransactionRequest } from './transactions/download-transaction.request';
import { TermDepositDetailsRequest } from '@/requests/fcc/deposits/td/details.request';
import { IpnBeneficiaryCreateRequest } from './transfers/beneficiaries/ipn-create.request';
import { IpnBeneficiaryUpdateRequest } from './transfers/beneficiaries/ipn-update.request';
import { IpnBeneficiariesListRequest } from './transfers/beneficiaries/ipn-list.request';
import { IpnBeneficiaryDeleteRequest } from './transfers/beneficiaries/ipn-delete.request';
import { CreditCardTransferInitiateRequest } from './cards/credit/settlement/initiate.request';
import { CreditCardTransferSubmitRequest } from './cards/credit/settlement/submit.request';
import { TermDepositListRequest } from '@/requests/fcc/deposits/td/list.request';
import { FccDepositsMapRequest } from './deposits/map.request';
import { IpnTransferInitiateRequest } from './transfers/ipn-transfers/initiate.request';
import { IpnTransferSubmitRequest } from './transfers/ipn-transfers/submit.request';
import { IpnTransferSimulateRequest } from './transfers/ipn-transfers/simulate.request';
import { LoanListRequest } from '@/requests/fcc/loans/list.request';
import { LoanDetailsRequest } from '@/requests/fcc/loans/details.request';
import { ChangeDefaultAccountRequest } from './users/change-account.request';

@Module({
  imports: [],
  controllers: [],
  providers: [
    AccountsListRequest,
    AccountBalanceRequest,
    MyPendingTransactionsRequest,
    AllTransactionsRequest,
    ApproveTransactionRequest,
    RejectTransactionRequest,
    TransactionDetailsRequest,
    AccountsListRequest,
    FxRateRequest,
    InternalTransferInitiateRequest,
    UploadAttachmentRequest,
    UploadDocumentRequest,
    AuthTypeRequest,
    AccountSummaryRequest,
    UserPermissionsRequest,
    UserDetailsRequest,
    CountryListRequest,
    CurrencyListRequest,
    BeneficiariesListRequest,
    MutualFundSummaryRequest,
    BeneficiariesListRequest,
    BeneficiaryDeleteRequest,
    TptBeneficiaryCreateRequest,
    TptBeneficiaryUpdateRequest,
    Mt103BeneficiaryCreateRequest,
    Mt103BeneficiaryUpdateRequest,
    IpnBeneficiaryCreateRequest,
    IpnBeneficiaryUpdateRequest,
    IpnBeneficiariesListRequest,
    IpnBeneficiaryDeleteRequest,
    SwiftDetailsRequest,
    InternalTransferSubmitRequest,
    LocalTransferInitiateRequest,
    LocalTransferSubmitRequest,
    CreditCardTransferInitiateRequest,
    CreditCardTransferSubmitRequest,
    UserReauthenticationTypeRequest,
    LoanAccountSummaryRequest,
    LoanListRequest,
    LoanDetailsRequest,
    AccountDetailsRequest,
    ClearingSystemRequestRequest,
    SwiftTransferInitiateRequest,
    SwiftTransferSubmitRequest,
    TransactionJourneyRequest,
    DeleteAttachmentRequest,
    ListAttachmentRequest,
    DownloadAttachmentRequest,
    BanksListRequest,
    TransactionHistoryRequest,
    ConfigurationDetailsRequest,
    AccountsBalanceSummaryRequest,
    FxConvertRequest,
    SecureMailInitiateRequest,
    SecureMailSubmitRequest,

    TermDepositInitiateRequest,
    TermDepositSubmitRequest,
    TermDepositDetailsRequest,

    CutOffTimeRequest,

    UsersListRequest,
    CreditCardSummaryRequest,
    CreditCardListRequest,
    CreditCardMovementsListRequest,
    SecureMailRejectedListRequest,
    DetailedAccountsListRequest,
    SecureMailApprovedListRequest,

    AccountStatementRequest,

    AccountMovementsListRequest,
    AccountMovementsDownloadRequest,

    ChangeLanguageRequest,
    ChangeCurrencyRequest,
    ChangeDefaultAccountRequest,

    AdviceDownloadRequest,
    DownloadTransactionRequest,
    TermDepositListRequest,

    FccDepositsMapRequest,

    IpnTransferInitiateRequest,
    IpnTransferSubmitRequest,
    IpnTransferSimulateRequest,
  ],
})
export class FCCModule {}
