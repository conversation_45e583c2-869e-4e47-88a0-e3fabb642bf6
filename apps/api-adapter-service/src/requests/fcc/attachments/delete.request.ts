import { Injectable, RequestMethod } from '@nestjs/common';
import { Request } from '@/adapter/decorators/request.decorator';
import { FccRequestsEnum, RequestModuleEnum } from '@enums';
import { ClassConstructor, Expose } from 'class-transformer';
import { IsNotEmpty, IsString } from 'class-validator';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';

export class DeleteAttachmentRequestDto {
  @Expose()
  @IsString()
  @IsNotEmpty()
  documentId!: string;
}

@Injectable()
@Request(FccRequestsEnum.DELETE_ATTACHMENT, RequestModuleEnum.FCC, 'v1')
export class DeleteAttachmentRequest extends FccRestRequest {
  readonly payloadDto: ClassConstructor<any> = DeleteAttachmentRequestDto;
  requestMethod: RequestMethod = RequestMethod.DELETE;

  ApiUrl(payload: DeleteAttachmentRequestDto): string {
    return `documents/${encodeURIComponent(payload.documentId)}`;
  }
}
