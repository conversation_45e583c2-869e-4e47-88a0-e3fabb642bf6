import { Injectable, Logger, RequestMethod } from '@nestjs/common';
import { Request } from '@/adapter/decorators/request.decorator';
import { FccRequestsEnum, RequestModuleEnum } from '@enums';
import { FccMultiPartRequest } from '@/adapter/requests/fcc-multipart.request';
import * as FormData from 'form-data';
import { formatFileSize } from '@helpers';

@Injectable()
@Request(FccRequestsEnum.UPLOAD_ATTACHMENT, RequestModuleEnum.FCC, 'v1')
export class UploadAttachmentRequest extends FccMultiPartRequest {
  protected readonly logger = new Logger(UploadAttachmentRequest.name);

  requestMethod: RequestMethod = RequestMethod.POST;

  ApiUrl(): string {
    return `upload`;
  }

  getRequestPayload(payload?: any): any {
    const formData = new FormData();

    const buffer = Buffer.from(payload.file.base64, 'base64');

    this.logger.debug(
      `uploding ${payload.fileName} / ${formatFileSize(buffer.length)} / ${payload.refId} / ${payload.tnxId}`,
    );

    formData.append('file', buffer, {
      filename: payload.file.filename,
      contentType: payload.file.mimetype,
    });

    formData.append('fileName', payload.fileName);
    formData.append('fileTitle', payload.fileTitle);
    formData.append('identifier', payload.identifier);
    formData.append('refId', payload.refId);
    formData.append('tnxId', payload.tnxId);
    formData.append('entity', payload.entity || '');

    return formData;
  }
}
