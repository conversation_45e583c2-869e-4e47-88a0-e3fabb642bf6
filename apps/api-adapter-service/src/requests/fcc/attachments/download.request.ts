import { Injectable, RequestMethod } from '@nestjs/common';
import { Request } from '@/adapter/decorators/request.decorator';
import { FccRequestsEnum, RequestModuleEnum, ResponseTypeEnum } from '@enums';
import { ClassConstructor, Expose } from 'class-transformer';
import { IsNotEmpty, IsString } from 'class-validator';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';

export class DownloadAttachmentRequestDto {
  @Expose()
  @IsString()
  @IsNotEmpty()
  attId: string;
}

@Injectable()
@Request(FccRequestsEnum.DOWNLOAD_ATTACHMENT, RequestModuleEnum.FCC, 'v1')
export class DownloadAttachmentRequest extends FccRestRequest {
  readonly payloadDto: ClassConstructor<any> = DownloadAttachmentRequestDto;
  requestMethod: RequestMethod = RequestMethod.GET;
  responseType = ResponseTypeEnum.ArrayBuffer;

  ApiUrl(payload: DownloadAttachmentRequestDto): string {
    return `download/${payload.attId}`;
  }

  format(data: any) {
    const buffer = Buffer.isBuffer(data) ? data : Buffer.from(data);

    return {
      response: 'success',
      data: buffer.toString('base64'),
    };
  }
}
