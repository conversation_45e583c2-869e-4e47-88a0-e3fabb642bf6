import { Injectable, RequestMethod } from '@nestjs/common';
import { Request } from '@/adapter/decorators/request.decorator';
import { FccRequestsEnum, RequestModuleEnum } from '@enums';
import { ClassConstructor, Expose } from 'class-transformer';
import { IsNotEmpty, IsString } from 'class-validator';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';

export class ListRequestDto {
  @Expose()
  @IsString()
  @IsNotEmpty()
  eventId!: string;
}

@Injectable()
@Request(FccRequestsEnum.GET_ATTACHMENT_LIST, RequestModuleEnum.FCC, 'v1')
export class ListAttachmentRequest extends FccRestRequest {
  readonly payloadDto: ClassConstructor<any> = ListRequestDto;
  requestMethod: RequestMethod = RequestMethod.GET;

  ApiUrl(): string {
    return `documents`;
  }
}
