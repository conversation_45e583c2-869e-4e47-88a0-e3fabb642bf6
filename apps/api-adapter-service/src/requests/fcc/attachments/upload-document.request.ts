import { Injectable, Logger, RequestMethod } from '@nestjs/common';
import { Request } from '@/adapter/decorators/request.decorator';
import { FccRequestsEnum, RequestModuleEnum } from '@enums';
import { FccMultiPartRequest } from '@/adapter/requests/fcc-multipart.request';
import * as FormData from 'form-data';
import { formatFileSize } from '@helpers';
import { ClassConstructor, Expose } from 'class-transformer';

export class UploadDocumentResponseDto {
  @Expose({ name: 'docId' })
  id: string;

  @Expose()
  size: string;

  @Expose()
  type: string;
}

@Injectable()
@Request(FccRequestsEnum.UPLOAD_DOCUMENT, RequestModuleEnum.FCC, 'v1')
export class UploadDocumentRequest extends FccMultiPartRequest {
  protected readonly logger = new Logger(UploadDocumentRequest.name);

  requestMethod: RequestMethod = RequestMethod.POST;

  responseDto: ClassConstructor<UploadDocumentResponseDto> =
    UploadDocumentResponseDto;

  ApiUrl(): string {
    return `documents`;
  }

  getRequestPayload(payload?: any): any {
    const formData = new FormData();

    const buffer = Buffer.from(payload.file.base64, 'base64');

    this.logger.debug(
      `uploding document ${payload.fileName} / ${formatFileSize(buffer.length)} / ${payload.refId} / ${payload.tnxId}`,
    );

    formData.append('file', buffer, {
      filename: payload.file.filename,
      contentType: payload.file.mimetype,
    });

    formData.append('fileName', payload.fileName);
    formData.append('title', payload.fileTitle);

    formData.append('id', payload.refId);
    formData.append('eventId', payload.tnxId);
    formData.append('type', 'CUSTOMER');

    return formData;
  }
}
