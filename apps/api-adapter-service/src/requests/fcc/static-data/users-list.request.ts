import { Injectable, RequestMethod } from '@nestjs/common';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';
import { Request } from '@/adapter/decorators/request.decorator';
import {
  CacheScope,
  CacheTTL,
  FccRequestsEnum,
  RequestModuleEnum,
} from '@enums';

import { Type, Expose, ClassConstructor } from 'class-transformer';
import {
  IsArray,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';

export class UsersListItemDto {
  @Expose({ name: 'ACTV_FLAG' })
  @IsOptional()
  @IsString()
  actvFlag: string;

  @Expose({ name: 'LOGIN_ID' })
  @IsString()
  loginId: string;

  @Expose({ name: 'MOBILE_NUMBER' })
  @IsOptional()
  @IsString()
  mobileNumber: string;

  @Expose({ name: 'USER_ID' })
  @IsNumber()
  userId: number;

  @Expose({ name: 'EMAIL' })
  @IsOptional()
  @IsString()
  email: string;

  @Expose({ name: 'NAME' })
  @IsOptional()
  @IsString()
  name: string;
}

export class UsersListDto {
  @Expose({ name: 'items' })
  @IsArray()
  @Type(() => UsersListItemDto)
  items: UsersListItemDto[];
}

@Injectable()
@Request(FccRequestsEnum.USERS_LIST, RequestModuleEnum.FCC, 'v1')
export class UsersListRequest extends FccRestRequest {
  requestMethod: RequestMethod = RequestMethod.GET;
  responseDto: ClassConstructor<any> = UsersListDto;

  cacheEnabled: boolean = true;
  cacheTtl: number = CacheTTL.OneHour;

  ApiUrl(): string {
    return `dxp/users-List`;
  }

  format(data: any) {
    const fotmatted: UsersListDto = super.format(data);
    return fotmatted?.items ?? [];
  }
}
