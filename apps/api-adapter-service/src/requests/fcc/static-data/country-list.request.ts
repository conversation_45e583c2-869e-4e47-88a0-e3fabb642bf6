import { Request } from '@/adapter/decorators/request.decorator';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';
import {
  CacheScope,
  CacheTTL,
  FccRequestsEnum,
  RequestModuleEnum,
} from '@enums';
import { Injectable, RequestMethod } from '@nestjs/common';

/**
 * Handles the FCC "country-list" request.
 * Maps all validated payload fields to FCC query params.
 */
@Injectable()
@Request(FccRequestsEnum.COUNTRY_LIST, RequestModuleEnum.FCC)
export class CountryListRequest extends FccRestRequest {
  requestMethod: RequestMethod = RequestMethod.GET;

  //cacheEnabled: boolean = true;
  cacheScope: CacheScope = CacheScope.GLOBAL;
  cacheTtl: number = CacheTTL.OneDay;

  /**
   * Returns the FCC endpoint for the country listing.
   */
  ApiUrl(): string {
    return 'countries';
  }

  getRequestPayload() {
    return {
      limit: 999,
    };
  }

  format(data: any) {
    return data?.countries?.map(({ alpha2code, name }) => ({
      code: alpha2code,
      name,
    }));
  }
}
