import { Request } from '@/adapter/decorators/request.decorator';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';
import {
  CacheScope,
  CacheTTL,
  FccRequestsEnum,
  RequestModuleEnum,
} from '@enums';
import { Injectable, RequestMethod } from '@nestjs/common';

/**
 * Handles the FCC "currency-list" request.
 * Maps all validated payload fields to FCC request body.
 */
@Injectable()
@Request(FccRequestsEnum.CURRENCY_LIST, RequestModuleEnum.FCC)
export class CurrencyListRequest extends FccRestRequest {
  requestMethod: RequestMethod = RequestMethod.POST;

  cacheEnabled: boolean = true;
  cacheScope: CacheScope = CacheScope.GLOBAL;
  cacheTtl: number = CacheTTL.OneDay;

  /**
   * Returns the FCC endpoint for the currency listing.
   */
  ApiUrl(): string {
    return 'currencyList';
  }
}
