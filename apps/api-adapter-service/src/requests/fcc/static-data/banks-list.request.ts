import { Request } from '@/adapter/decorators/request.decorator';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';
import {
  CacheScope,
  CacheTTL,
  FccRequestsEnum,
  RequestModuleEnum,
} from '@enums';
import { Injectable, RequestMethod } from '@nestjs/common';

@Injectable()
@Request(FccRequestsEnum.BANKS_LIST, RequestModuleEnum.FCC)
export class BanksListRequest extends FccRestRequest {
  requestMethod: RequestMethod = RequestMethod.GET;
  cacheEnabled: boolean = true;
  cacheScope: CacheScope = CacheScope.GLOBAL;
  cacheTtl: number = CacheTTL.OneDay;

  ApiUrl(): string {
    return 'dxp/domestic-banks';
  }
}
