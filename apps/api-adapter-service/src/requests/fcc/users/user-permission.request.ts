import { Request } from '@/adapter/decorators/request.decorator';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';
import { CacheTTL, FccRequestsEnum, RequestModuleEnum } from '@enums';
import { Injectable, RequestMethod } from '@nestjs/common';

@Injectable()
@Request(FccRequestsEnum.USER_PERMISSIONS, RequestModuleEnum.FCC)
export class UserPermissionsRequest extends FccRestRequest {
  ApiUrl(): string {
    return 'access-permissions';
  }
  requestMethod: RequestMethod = RequestMethod.GET;

  cacheEnabled: boolean = true;
  cacheTtl: number = CacheTTL.TenMinutes;

  getRequestPayload() {
    return { limit: 10000, offset: 0 };
  }

  format(data: any) {
    if (data?.items?.length > 0) {
      return data?.items?.map(
        (item: Record<string, string>) => item?.actionAllowed,
      );
    }
    return [];
  }
}
