import { Request } from '@/adapter/decorators/request.decorator';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';
import { FccUserDetailsDto } from '@dtos';
import { CacheTTL, FccRequestsEnum, RequestModuleEnum } from '@enums';
import { Injectable, RequestMethod } from '@nestjs/common';
import { ClassConstructor } from 'class-transformer';

@Injectable()
@Request(FccRequestsEnum.USER_DETAILS, RequestModuleEnum.FCC)
export class UserDetailsRequest extends FccRestRequest {
  requestMethod: RequestMethod = RequestMethod.GET;
  responseDto: ClassConstructor<any> = FccUserDetailsDto;

  cacheEnabled: boolean = true;
  cacheTtl: number = CacheTTL.TenMinutes;

  ApiUrl(): string {
    return 'dxp/userDetails';
  }
}
