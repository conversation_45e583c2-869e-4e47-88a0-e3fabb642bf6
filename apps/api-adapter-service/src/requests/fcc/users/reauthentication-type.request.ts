import { Request } from '@/adapter/decorators/request.decorator';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';
import { FccRequestsEnum, RequestModuleEnum } from '@enums';
import { Injectable, RequestMethod } from '@nestjs/common';
import { ClassConstructor, Expose } from 'class-transformer';
import { IsNotEmpty, IsOptional } from 'class-validator';
import * as snakecaseKeys from 'snakecase-keys';
import { get } from 'lodash';

export class UserReauthenticationTypeRequestDto {
  @IsNotEmpty()
  @Expose()
  productCode: string;

  @IsNotEmpty()
  @IsOptional()
  @Expose()
  subProductCode: string;

  @IsOptional()
  @Expose()
  ftAmt: string;

  @IsOptional()
  @Expose()
  operation: string;

  @IsOptional()
  @Expose()
  refId: string;

  @IsOptional()
  @Expose()
  tnxId: string;
}

@Injectable()
@Request(FccRequestsEnum.USER_REAUTHENTICATION_TYPE, RequestModuleEnum.FCC)
export class UserReauthenticationTypeRequest extends FccRestRequest {
  payloadDto: ClassConstructor<any> = UserReauthenticationTypeRequestDto;

  ApiUrl(): string {
    return 'reauthentication-type';
  }

  requestMethod: RequestMethod = RequestMethod.POST;

  getRequestPayload(payload: UserReauthenticationTypeRequestDto) {
    return {
      transactionPayload: Object.entries(snakecaseKeys({ ...payload })).map(
        ([key, value]) => ({
          name: key,
          value: String(value),
        }),
      ),
    };
  }

  format(data: any) {
    const reauthenticationType = get(data, 'items[0]', null);
    if (reauthenticationType !== null) {
      return reauthenticationType;
    }
    return null;
  }
}
