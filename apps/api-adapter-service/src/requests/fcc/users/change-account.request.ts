import { Injectable, RequestMethod } from '@nestjs/common';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';
import { Request } from '@/adapter/decorators/request.decorator';
import { CurrencyEnum, FccRequestsEnum, RequestModuleEnum } from '@enums';
import { ClassConstructor, Expose } from 'class-transformer';
import { IsEnum, IsNotEmpty } from 'class-validator';

export class ChangeDefaultAccountRequestDto {
  @IsNotEmpty()
  @Expose()
  accountId: string;
}

export class ChangeDefaultAccountResponseDto {
  @Expose()
  response: string;
}

@Injectable()
@Request(FccRequestsEnum.CHANGE_DEFAULT_ACCOUNT, RequestModuleEnum.FCC, 'v1')
export class ChangeDefaultAccountRequest extends FccRestRequest {
  requestMethod: RequestMethod = RequestMethod.POST;
  payloadDto: ClassConstructor<any> = ChangeDefaultAccountRequestDto;
  responseDto: ClassConstructor<any> = ChangeDefaultAccountResponseDto;

  ApiUrl(): string {
    return `dxp/default-account/change`;
  }

  getRequestPayload({ accountId }: ChangeDefaultAccountRequestDto) {
    return {
      accountId,
    };
  }
}
