import { Injectable, RequestMethod } from '@nestjs/common';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';
import { Request } from '@/adapter/decorators/request.decorator';
import { FccRequestsEnum, RequestModuleEnum } from '@enums';
import { ClassConstructor, Expose } from 'class-transformer';
import { IsNotEmpty, IsString } from 'class-validator';

export class ChangeLanguageRequestDTO {
  @IsString()
  @IsNotEmpty()
  @Expose()
  language: string;
}

export class ChangeLangaugeResponseDto {
  @Expose()
  response: string;

  @Expose()
  userLanguage: string;

  @Expose()
  errorMessage: string;

  @Expose()
  message: string;
}

@Injectable()
@Request(FccRequestsEnum.CHANGE_LANGUAGE, RequestModuleEnum.FCC, 'v1')
export class ChangeLanguageRequest extends FccRestRequest {
  requestMethod: RequestMethod = RequestMethod.POST;
  payloadDto: ClassConstructor<any> = ChangeLanguageRequestDTO;
  responseDto: ClassConstructor<any> = ChangeLangaugeResponseDto;

  ApiUrl(): string {
    return `language/change/`;
  }

  getRequestPayload({ language }: ChangeLanguageRequestDTO) {
    return {
      requestData: {
        language,
      },
    };
  }
}
