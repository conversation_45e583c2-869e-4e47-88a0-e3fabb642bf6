import { Injectable, RequestMethod } from '@nestjs/common';
import { FccRestRequest } from '@/adapter/requests/fcc-rest.request';
import { Request } from '@/adapter/decorators/request.decorator';
import { CurrencyEnum, FccRequestsEnum, RequestModuleEnum } from '@enums';
import { ClassConstructor, Expose } from 'class-transformer';
import { IsEnum, IsNotEmpty } from 'class-validator';

export class ChangeCurrencyRequestDto {
  @IsEnum(CurrencyEnum)
  @IsNotEmpty()
  @Expose()
  currency: string;
}

export class ChangeCurrencyResponseDto {
  @Expose()
  response: string;
}

@Injectable()
@Request(FccRequestsEnum.CHANGE_CURRENCY, RequestModuleEnum.FCC, 'v1')
export class ChangeCurrencyRequest extends FccRestRequest {
  requestMethod: RequestMethod = RequestMethod.POST;
  payloadDto: ClassConstructor<any> = ChangeCurrencyRequestDto;
  responseDto: ClassConstructor<any> = ChangeCurrencyResponseDto;

  ApiUrl(): string {
    return `dxp/currency/change/`;
  }

  getRequestPayload({ currency }: ChangeCurrencyRequestDto) {
    return {
      curCode: currency,
    };
  }
}
