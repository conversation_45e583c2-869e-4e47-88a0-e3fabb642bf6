import { FormRequest } from '@/adapter/requests/form.request';
import { EstatementRequestsEnum, RequestModuleEnum } from '@enums';
import { Injectable, RequestMethod } from '@nestjs/common';
import { Request } from '@/adapter/decorators/request.decorator';
import { Expose, ClassConstructor } from 'class-transformer';
import { IsNotEmpty } from 'class-validator';
import { parseXMLtoJSON } from '@/adapter/helpers/xmlparser.helper';

export class EstatementEncryptionRequestDto {
  @Expose()
  @IsNotEmpty()
  account: string;

  @Expose()
  @IsNotEmpty()
  accountType: string;
}
@Injectable()
@Request(EstatementRequestsEnum.ENCRYPTION, RequestModuleEnum.ESTATEMENT)
export class EstatementEncryptionRequest extends FormRequest {
  requestMethod: RequestMethod = RequestMethod.POST;
  readonly payloadDto: ClassConstructor<any> = EstatementEncryptionRequestDto;

  getDestinationUrl(): string {
    return `${this.configService.get('estatement.encryptionURL')}`;
  }

  getRequestPayload(payload: EstatementEncryptionRequestDto) {
    return {
      strUserName: this.configService.get('estatement.username'),
      strPassword: this.configService.get('estatement.password'),
      strAccount: payload.account,
      strAccountType: payload.accountType,
    };
  }

  format(data: string) {
    const json = parseXMLtoJSON(data);
    const string = json.string;
    return {
      url: `${this.configService.get('estatement.publicURL')}${string}`,
    };
  }
}
