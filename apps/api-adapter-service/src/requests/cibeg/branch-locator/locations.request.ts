import { Request } from '@/adapter/decorators/request.decorator';
import { Injectable, RequestMethod } from '@nestjs/common';
import {
  CacheScope,
  CacheTTL,
  CibegRequestsEnum,
  RequestModuleEnum,
} from '@enums';
import { CibegApiRequest } from '@/adapter/requests/cibeg.request';
import { ClassConstructor } from 'class-transformer';
import { GetBranchsLocationsDto } from '@dtos';
import { ConfigService } from '@nestjs/config';

@Injectable()
@Request(CibegRequestsEnum.BRANCH_LOCATIONS, RequestModuleEnum.CIBEG)
export class BranchLocationsRequest extends CibegApiRequest {
  requestMethod: RequestMethod = RequestMethod.GET;
  payloadDto: ClassConstructor<any> = GetBranchsLocationsDto;

  cacheEnabled: boolean = true;
  cacheScope: CacheScope = CacheScope.GLOBAL;
  cacheTtl: number = CacheTTL.OneDay;

  constructor(protected configService: ConfigService) {
    super(configService);
  }

  ApiUrl(): string {
    return `map/getmapitems`;
  }

  getRequestPayload(payload: GetBranchsLocationsDto) {
    const params: Record<string, string | number> = {
      ds: this.configService.get('branchLocator.ds'),
    };

    if (payload.standAloneATMs) params.Tlt = 1;
    if (payload.branchesWithATMs) params.Tlt = 3;

    if (payload.deposit) params.Tse = 2;
    if (payload.withdrawal) params.Tse = 4;
    if (payload.chequeDeposit) params.Tse = 1;

    if (payload.forex) params.Tse = 3;
    if (payload.talkingATM) params.Tse = 5;

    if (payload.specialNeedsBathroom) params.Tse = 6;
    if (payload.specialNeedsRamp) params.Tse = 7;
    if (payload.specialNeedsTeller) params.Tse = 8;

    if (payload.businessBanking) params.Tsg = 1;

    if (payload.plus) params.Tsg = 4;
    if (payload.wealth) params.Tsg = 9;
    if (payload.prime) params.Tsg = 5;
    if (payload.corporate) params.Tsg = 2;
    if (payload.private) params.Tsg = 6;

    if (payload.query) params.q = payload.query;

    return params;
  }
}
