import { Request } from '@/adapter/decorators/request.decorator';
import { Injectable, RequestMethod } from '@nestjs/common';
import {
  CacheScope,
  CacheTTL,
  CibegRequestsEnum,
  RequestModuleEnum,
} from '@enums';
import { CibegApiRequest } from '@/adapter/requests/cibeg.request';
import { ClassConstructor, Expose } from 'class-transformer';
import { ConfigService } from '@nestjs/config';
import { IsNotEmpty } from 'class-validator';

export class BranchLocationDetailsRequestDto {
  @Expose()
  @IsNotEmpty()
  mapid: string;
}

@Injectable()
@Request(CibegRequestsEnum.BRANCH_LOCATION_DETAILS, RequestModuleEnum.CIBEG)
export class BranchLocationDetailsRequest extends CibegApiRequest {
  requestMethod: RequestMethod = RequestMethod.GET;
  payloadDto: ClassConstructor<any> = BranchLocationDetailsRequestDto;

  cacheEnabled: boolean = true;
  cacheScope: CacheScope = CacheScope.GLOBAL;
  cacheTtl: number = CacheTTL.OneDay;

  constructor(protected configService: ConfigService) {
    super(configService);
  }

  ApiUrl(): string {
    return `map/getmapitems`;
  }
}
