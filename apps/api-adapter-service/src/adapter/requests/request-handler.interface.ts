import { ApiAdapterRequestOptionsDto } from '@dtos';
import { CacheScope, ResponseTypeEnum } from '@enums';
import { RequestMethod } from '@nestjs/common';
import { CurrentUser, ProxyConfig } from '@types';
import { ClassConstructor } from 'class-transformer';

export interface HeadersConfig {
  authConfig: Record<string, any>;
  requestOptions?: ApiAdapterRequestOptionsDto;
}

export interface RequestHandler {
  readonly payloadDto: ClassConstructor<any>;
  readonly requestMethod: RequestMethod;

  readonly cacheEnabled?: boolean;
  readonly cacheScope?: CacheScope;
  readonly cacheTtl?: number;

  responseType?: ResponseTypeEnum;

  format(data: any): any;
  errorFormat(data: any, options: ApiAdapterRequestOptionsDto): any;
  getDestinationUrl(payload?: any): string;
  getHeaders?(config?: HeadersConfig): Record<string, string>;
  getRequestPayload?(payload?: any): any;
  getProxy?(payload?: any): ProxyConfig | false;
  authenticateUser?(user?: CurrentUser): any;
  validateResponse?(body: any, headers: any, user?: CurrentUser): any;
}
