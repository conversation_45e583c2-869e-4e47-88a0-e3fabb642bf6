import { Injectable } from '@nestjs/common';
import { FccRestRequest } from './fcc-rest.request';
import { keyBy, mapValues } from 'lodash';
import { ClassConstructor, plainToClass } from 'class-transformer';

@Injectable()
export abstract class FccListDataRestRequest extends FccRestRequest {
  readonly singleItemResponseDto: ClassConstructor<any> = null;

  ApiUrl(): string {
    return `listdata`;
  }

  format(data: any) {
    const transformed = {
      count: data?.count,
      rowDetails: data?.rowDetails?.map((row) => {
        const item = mapValues(keyBy(row.index, 'name'), 'value');
        if (this.singleItemResponseDto) {
          return plainToClass(this.singleItemResponseDto, item, {
            excludeExtraneousValues: true,
          });
        }
        return item;
      }),
    };

    return transformed;
  }
}
