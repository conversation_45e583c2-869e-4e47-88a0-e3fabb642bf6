import { ConfigService } from '@nestjs/config';
import { HeadersConfig, RequestHandler } from './request-handler.interface';
import { Injectable, RequestMethod } from '@nestjs/common';
import { ClassConstructor, plainToClass } from 'class-transformer';
import { CacheScope, CacheTTL, UserLanguage } from '@enums';
import { ProxyConfig } from '@types';

@Injectable()
export abstract class CibegApiRequest implements RequestHandler {
  readonly payloadDto: ClassConstructor<any> = null;
  readonly responseDto: ClassConstructor<any> = null;
  abstract readonly requestMethod: RequestMethod;

  readonly cacheEnabled?: boolean = false;
  readonly cacheScope?: CacheScope = CacheScope.USER;
  readonly cacheTtl?: number = CacheTTL.OneHour;

  abstract ApiUrl(payload?: any): string;

  constructor(protected configService: ConfigService) {}

  getProxy(): ProxyConfig | false {
    return {
      protocol: 'http',
      host: this.configService.get('publicProxy.host'),
      port: this.configService.get('publicProxy.port'),
    };
  }

  getDestinationUrl(payload: any): string {
    return `${this.configService.get('cibegURL')}/api/${this.ApiUrl(payload)}`;
  }

  getHeaders(config: HeadersConfig): Record<string, string> {
    const language =
      config?.requestOptions?.language === UserLanguage['ar-EG'] ? 'ar' : 'en';
    return {
      'Content-Type': 'application/json',
      Cookie: `www.cibeg.com#lang=${language};`,
    };
  }

  getRequestPayload?(payload?: any) {
    return payload;
  }

  /**
   * Formats the response data.
   * Override in child classes if transformation is needed.
   */
  format(data: any) {
    if (this.responseDto) {
      return plainToClass(this.responseDto, data, {
        excludeExtraneousValues: true,
      });
    }
    return data;
  }

  /**
   * Formats error responses.
   * Override for custom error handling.
   */
  errorFormat(data: string) {
    return data;
  }
}
