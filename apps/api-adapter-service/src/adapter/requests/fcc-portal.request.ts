import { HeadersConfig } from './request-handler.interface';
import { Injectable, RequestMethod } from '@nestjs/common';
import { v4 as UUIDV4 } from 'uuid';
import { FccRequest } from './fcc.request';

/**
 * Abstract base class for all FCC REST requests.
 * Enforces structure and centralizes URL/header/query logic.
 */
@Injectable()
export abstract class FccPortalRequest extends FccRequest {
  abstract readonly requestMethod: RequestMethod;

  /**
   * Returns the API endpoint path for the request
   * @param payload - Optional payload for dynamic path generation
   * @returns API endpoint path string
   */
  abstract ApiUrl(payload?: any): string;

  /**
   * Formats the response data.
   * Override in child classes if transformation is needed.
   */
  format(data: any) {
    return data;
  }

  /**
   * Formats error responses.
   * Override for custom error handling.
   */
  errorFormat(data: string) {
    return data;
  }

  /**
   * Constructs the full destination URL for the FCC API.
   * Appends query params for GET requests.
   * @param payload - Optional request payload
   * @returns Full API endpoint URL with query params if applicable
   */
  getDestinationUrl(payload?: any): string {
    return `${this.configService.get('fccURL')}/portal/${this.ApiUrl(payload)}`;
  }

  getHeaders(config: HeadersConfig): Record<string, string> {
    const idempotencyKey = config?.requestOptions?.idempotencyKey || UUIDV4();
    return {
      Cookie: config?.authConfig?.cookies,
      'idempotency-key': idempotencyKey,
      'REQUEST-FROM': 'INTERNAL',
      'Content-Type': 'application/x-www-form-urlencoded',
    };
  }

  /**
   * Processes request payload before sending
   * @param payload - Raw payload data
   * Returns the request payload for POST/PUT requests.
   */
  getRequestPayload(payload: any) {
    return payload;
  }
}
