import { HeadersConfig } from './request-handler.interface';
import { Injectable, RequestMethod } from '@nestjs/common';
import { v4 as UUIDV4 } from 'uuid';
import { plainToClass } from 'class-transformer';
import { FccErrorResponse } from '@types';
import { ErrorCodeEnum } from '@enums';
import { ApiAdapterRequestOptionsDto } from '@dtos';
import { FccErrorCodeMap } from '@constants';
import { FccRequest } from './fcc.request';

/**
 * Abstract base class for all FCC REST requests.
 * Enforces structure and centralizes URL/header/query logic.
 */
@Injectable()
export abstract class FccRestRequest extends FccRequest {
  abstract readonly requestMethod: RequestMethod;

  /**
   * Formats the response data.
   * Override in child classes if transformation is needed.
   */
  format(data: any) {
    if (this.responseDto) {
      return plainToClass(this.responseDto, data, {
        excludeExtraneousValues: true,
      });
    }
    return data;
  }

  /**
   * Formats error responses.
   * Override for custom error handling.
   */
  errorFormat(data: FccErrorResponse, options: ApiAdapterRequestOptionsDto) {
    //dont forget to add the request name on i18n as fallback
    this.logger.debug(data);
    console.log(data);

    const statusCode = data?.status ?? 500;

    if (data?.keyMessage) {
      return {
        statusCode,
        errorCode: FccErrorCodeMap[data.keyMessage] ?? data.keyMessage,
      };
    }

    if (FccErrorCodeMap?.[data?.titleKey]) {
      return {
        statusCode,
        errorCode: FccErrorCodeMap[data.titleKey],
      };
    }

    if (data?.causes && data?.causes.length > 0) {
      const { messageKey = undefined } =
        data.causes.find((cause) => cause.messageKey) ?? {};

      if (messageKey) {
        return {
          statusCode,
          errorCode: FccErrorCodeMap?.[messageKey] ?? messageKey,
        };
      }
    }
    return {
      statusCode,
      errorCode: data?.titleKey ?? ErrorCodeEnum.UnknownError,
    };
  }

  /**
   * Constructs the full destination URL for the FCC API.
   * Appends query params for GET requests.
   * @param payload - Optional request payload
   * @returns Full API endpoint URL with query params if applicable
   */
  getDestinationUrl(payload?: any): string {
    return `${this.configService.get('fccURL')}/restportal/${this.ApiUrl(payload)}`;
  }

  getHeaders(config: HeadersConfig): Record<string, string> {
    const idempotencyKey = config?.requestOptions?.idempotencyKey || UUIDV4();
    return {
      'Content-Type': 'application/json',
      Cookie: config?.authConfig?.cookies,
      'idempotency-key': idempotencyKey,
      'REQUEST-FROM': 'INTERNAL',
    };
  }
}
