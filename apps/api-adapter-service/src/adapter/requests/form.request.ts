import { ConfigService } from '@nestjs/config';
import { HeadersConfig, RequestHandler } from './request-handler.interface';
import { Injectable, RequestMethod } from '@nestjs/common';
import { ClassConstructor } from 'class-transformer';
import { UserLanguage } from '@enums';

@Injectable()
export abstract class FormRequest implements RequestHandler {
  readonly payloadDto: ClassConstructor<any> = null;
  readonly responseDto: ClassConstructor<any> = null;
  abstract readonly requestMethod: RequestMethod;

  constructor(protected configService: ConfigService) {}

  abstract getDestinationUrl(payload: any): string;

  getHeaders(config: HeadersConfig): Record<string, string> {
    const language =
      config?.requestOptions?.language === UserLanguage['ar-EG'] ? 'ar' : 'en';
    return {
      'Content-Type': 'application/x-www-form-urlencoded',
    };
  }

  getRequestPayload?(payload?: any) {
    return payload;
  }

  format(data: any) {
    return data;
  }

  errorFormat(data: string) {
    return data;
  }
}
