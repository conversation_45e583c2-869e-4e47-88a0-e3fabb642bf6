import { ConfigService } from '@nestjs/config';
import { HeadersConfig, RequestHandler } from './request-handler.interface';
import {
  Injectable,
  Logger,
  RequestMethod,
  ServiceUnavailableException,
  UnauthorizedException,
} from '@nestjs/common';
import { ClassConstructor } from 'class-transformer';
import { CurrentUser } from '@types';
import { RedisCacheService } from '@modules';
import { CacheScope, CacheTTL, ErrorCodeEnum } from '@enums';
import { ApiAdapterRequestOptionsDto } from '@dtos';

/**
 * Abstract base class for all FCC REST requests.
 * Enforces structure and centralizes URL/header/query logic.
 */
@Injectable()
export abstract class FccRequest implements RequestHandler {
  protected readonly logger = new Logger(FccRequest.name);

  readonly payloadDto: ClassConstructor<any> = null;
  readonly responseDto: ClassConstructor<any> = null;
  abstract readonly requestMethod: RequestMethod;

  readonly cacheEnabled?: boolean = false;
  readonly cacheScope?: CacheScope = CacheScope.USER;
  readonly cacheTtl?: number = CacheTTL.OneHour;

  abstract ApiUrl(payload?: any): string;
  abstract errorFormat(data: any, options: ApiAdapterRequestOptionsDto): any;
  abstract format(data: any): any;
  abstract getDestinationUrl(payload?: any): string;
  abstract getHeaders?(config?: HeadersConfig): Record<string, string>;

  constructor(
    protected readonly configService: ConfigService,
    protected readonly redisService: RedisCacheService,
  ) {}

  /**
   * Processes request payload before sending
   * @param payload - Raw payload data
   * Returns the request payload for POST/PUT requests.
   */
  getRequestPayload(payload: any) {
    return payload;
  }

  async authenticateUser(user: CurrentUser) {
    if (!user) {
      throw new ServiceUnavailableException('User info is required');
    }

    if (user.cookies) {
      return { cookies: user.cookies };
    }

    const userData = await this.redisService.get(user.jti);
    if (!userData) {
      throw new ServiceUnavailableException(
        'User is not found, please reauthenticate',
      );
    }
    return userData;
  }

  async validateResponse(body: any, headers: any, user: CurrentUser) {
    if (
      headers?.['content-type'] === 'text/html' &&
      body?.includes('Login_Form')
    ) {
      try {
        await this.redisService.del(user.jti);
      } catch (error) {}
      throw new UnauthorizedException({
        statusCode: 401,
        errorCode: ErrorCodeEnum.SessionTerminated,
      });
    }
  }
}
