import { ConfigService } from '@nestjs/config';
import { v4 as UUIDV4 } from 'uuid';
import { DateTime } from 'luxon';
import { RequestHandler } from './request-handler.interface';
import { Injectable, RequestMethod } from '@nestjs/common';
import { parseXMLtoJSON } from '../helpers/xmlparser.helper';
import { ClassConstructor, plainToClass } from 'class-transformer';
import { get } from 'lodash';
import { CacheScope, CacheTTL } from '@enums';

@Injectable()
export abstract class EsbRequest implements RequestHandler {
  abstract readonly payloadDto: ClassConstructor<any>;
  abstract readonly SOAPAction: string;
  abstract soapTemplate(payload: any, header: string): string;
  readonly responseDto: ClassConstructor<any> = null;
  requestMethod: RequestMethod = RequestMethod.POST;
  serviceName: string = '19999999';
  xmlRootPath: string[] = [];

  opMode: string;

  readonly cacheEnabled?: boolean = false;
  readonly cacheScope?: CacheScope = CacheScope.GLOBAL;
  readonly cacheTtl?: number = CacheTTL.OneHour;

  constructor(private configService: ConfigService) {}

  format(data: string) {
    const json = parseXMLtoJSON(data);
    const body = get(json, ['Envelope', 'Body', ...this.xmlRootPath]);

    if (this.responseDto) {
      return plainToClass(this.responseDto, body, {
        excludeExtraneousValues: true,
      });
    }
    return body;
  }

  errorFormat(data: string) {
    const json = parseXMLtoJSON(data);
    return json?.Envelope?.Body?.Fault;
  }

  getDestinationUrl(): string {
    return `${this.configService.get('esbURL')}`;
  }

  getHeaders(): Record<string, string> {
    return {
      SOAPAction: `"${this.SOAPAction}"`,
      Connection: 'Keep-Alive',
      'Content-Type': 'text/xml;charset=UTF-8',
      'Accept-Encoding': 'gzip,deflate',
      'User-Agent': 'Apache-HttpClient/4.1.1 (java 1.5)',
    };
  }

  soapHeader() {
    const uuid = UUIDV4();
    const timestamp = DateTime.now().toFormat("yyyy-MM-dd'T'HH:mm:ss");
    const opModeTag = this.opMode ? `<OpMode>${this.opMode}</OpMode>` : '';

    return `<soapenv:Header>
        <head:MsgHdr>
            <RqUID>${uuid}</RqUID>
            <RqDtTm>${timestamp}</RqDtTm>
            <MsgRqHdr>
                <AsyncRqUID>${uuid}</AsyncRqUID>
                <SvcIdent>
                    <SvcName>${this.serviceName}</SvcName>
                </SvcIdent>
                <ConsumerId>DDCIB</ConsumerId>
                <ContextRqHdr>
                    <RqMode>SYNC</RqMode>
                    ${opModeTag}
                </ContextRqHdr>
            </MsgRqHdr>
        </head:MsgHdr>
    </soapenv:Header>`;
  }

  getRequestPayload(payload: any) {
    return this.soapTemplate(payload, this.soapHeader())
      .replace(/\n/g, '')
      .replace(/\s+/g, ' ')
      .replace(/> </g, '><');
  }
}
