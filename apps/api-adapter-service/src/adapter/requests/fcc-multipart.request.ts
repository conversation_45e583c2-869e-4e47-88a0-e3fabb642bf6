import { Injectable } from '@nestjs/common';
import { FccRestRequest } from './fcc-rest.request';
import { HeadersConfig } from './request-handler.interface';

@Injectable()
export abstract class FccMultiPartRequest extends FccRestRequest {
  getHeaders(config: HeadersConfig): Record<string, string> {
    return {
      'Content-Type': 'multipart/form-data',
      Cookie: config?.authConfig?.cookies,
      'REQUEST-FROM': 'INTERNAL',
    };
  }
}
