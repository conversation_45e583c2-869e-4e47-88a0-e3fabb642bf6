import { XMLParser } from 'fast-xml-parser';

export const parseXMLtoJSON = (data: any, options?: any) => {
  try {
    const parser = new XMLParser({
      ignoreAttributes: false,
      attributeNamePrefix: '',
      trimValues: true,
      parseTagValue: true,
      parseAttributeValue: true,
      textNodeName: '$t',
      processEntities: true,
      removeNSPrefix: true,
      ...options,
    });

    if (typeof data === 'object') return data;
    return parser.parse(data);
  } catch (err) {
    return data;
  }
};
