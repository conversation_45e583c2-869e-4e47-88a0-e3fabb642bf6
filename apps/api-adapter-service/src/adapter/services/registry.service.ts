import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { DiscoveryService, Reflector } from '@nestjs/core';
import { REQUEST_KEY } from '../decorators/request.decorator';
import { RequestHandler } from '../requests/request-handler.interface';
import { ApiAdapterRequestDto } from '@dtos';
import { RequestModuleEnum } from '@enums';

@Injectable()
export class Registry implements OnModuleInit {
  private readonly logger = new Logger(Registry.name);

  private readonly handlers = new Map<string, RequestHandler>();

  constructor(
    private readonly discoveryService: DiscoveryService,
    private readonly reflector: Reflector,
  ) {}

  async onModuleInit() {
    const providers = this.discoveryService.getProviders();

    for (const provider of providers) {
      const instance = provider.instance;
      if (!instance) continue;

      const requestMeta = this.reflector.get<{
        requestId: string;
        module: RequestModuleEnum;
        version: number;
      }>(REQUEST_KEY, instance.constructor);

      if (requestMeta) {
        this.logger.debug(
          `Request ${requestMeta.module.toUpperCase()} ${requestMeta.requestId} ${requestMeta.version} initialized`,
        );
        this.handlers.set(
          `${requestMeta.module}-${requestMeta.requestId}-${requestMeta.version}`,
          instance as RequestHandler,
        );
      }
    }
  }

  getHandler(request: ApiAdapterRequestDto): RequestHandler | undefined {
    return this.handlers.get(
      `${request.module}-${request.requestId}-${request.version}`,
    );
  }
}
