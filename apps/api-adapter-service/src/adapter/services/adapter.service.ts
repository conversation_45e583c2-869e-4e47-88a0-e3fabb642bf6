import {
  BadRequestException,
  HttpException,
  Injectable,
  Logger,
  NotFoundException,
  RequestMethod,
  UnauthorizedException,
} from '@nestjs/common';
import * as http from 'node:http';

import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { Registry } from './registry.service';
import { plainToInstance } from 'class-transformer';
import { validate } from 'class-validator';
import { ApiAdapterRequestDto } from '@dtos';
import { I18nLookupService, RedisApiAdapterService } from '@modules';
import { ResponseTypeEnum, UserLanguage } from '@enums';
import { AxiosRequestConfig } from 'axios';
import { createHttpsAgent } from '@helpers';

@Injectable()
export class AdapterService {
  private readonly logger = new Logger(AdapterService.name);

  constructor(
    private readonly requestRegistry: Registry,
    private readonly httpService: HttpService,
    private readonly cacheService: RedisApiAdapterService,
    private readonly i18nService: I18nLookupService,
  ) {}

  async processRequest(request: ApiAdapterRequestDto) {
    const handler = this.requestRegistry.getHandler(request);

    if (!handler) {
      throw new NotFoundException(`No handler found for ${request.requestId}`);
    }

    const authConfig = await handler.authenticateUser?.(request.user);

    let payload = request?.payload;

    if (handler.payloadDto) {
      const classDTO = plainToInstance(
        handler.payloadDto,
        request.payload ?? {},
        {
          excludeExtraneousValues: true,
        },
      );

      payload = classDTO;

      const errors = await validate(classDTO);
      if (errors.length > 0) {
        throw new BadRequestException(errors);
      }
    }

    const url = handler.getDestinationUrl(payload);
    const data = handler.getRequestPayload(payload);
    const headers =
      handler.getHeaders?.({
        authConfig,
        requestOptions: request?.options,
      }) ?? {};

    const cacheEnabled =
      request?.options?.cacheEnabled ?? handler?.cacheEnabled ?? false;

    const proxy = handler.getProxy?.() ?? false;

    try {
      let log = `${RequestMethod[handler.requestMethod]} ${request.module?.toUpperCase()} ${request.requestId} ${request.version} cache: ${cacheEnabled} cacheTtl: ${handler?.cacheTtl} URL: ${url} Headers: ${JSON.stringify(headers)} `;
      if (headers?.['Content-Type'] !== 'multipart/form-data') {
        log += `Payload: ${data instanceof Object ? JSON.stringify(data) : data}`;
      }
      this.logger.debug(log);

      const requestConfig: AxiosRequestConfig = {
        method: RequestMethod[handler.requestMethod],
        url,
        headers,
        httpAgent: new http.Agent({ keepAlive: true }),
        httpsAgent: createHttpsAgent(proxy),
        responseType: handler.responseType ?? ResponseTypeEnum.Json,
      };

      if (handler.requestMethod === RequestMethod.GET) {
        requestConfig.params = data;
      } else {
        requestConfig.data = data;
      }

      const response = await this.cacheService.getOrSet(
        request,
        {
          enabled: cacheEnabled,
          scope: handler.cacheScope,
          ttl: handler.cacheTtl,
        },
        async () => {
          const response = await firstValueFrom(
            this.httpService.request(requestConfig),
          );
          await handler.validateResponse?.(
            response.data,
            response.headers,
            request.user,
          );

          return handler.format(response.data);
        },
      );
      return response;
    } catch (error) {
      this.logger.error({ errorMessage: error.stack });

      //don't throw any other exceptions, the adaptor client will handle the rest
      if (error instanceof UnauthorizedException) {
        throw error;
      }

      throw new HttpException(
        error.response?.data
          ? handler.errorFormat(error.response.data, request?.options)
          : this.i18nService.translate(
              'exception.HttpException',
              UserLanguage['en-US'],
            ),
        500,
      );
    }
  }
}
