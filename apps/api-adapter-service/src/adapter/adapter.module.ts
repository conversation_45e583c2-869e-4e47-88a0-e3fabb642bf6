import { Module } from '@nestjs/common';
import { DiscoveryModule } from '@nestjs/core';
import { HttpModule } from '@nestjs/axios';

import { AdapterService } from './services/adapter.service';
import { AdapterController } from './adapter.controller';
import { Registry } from './services/registry.service';

@Module({
  imports: [DiscoveryModule, HttpModule],
  controllers: [AdapterController],
  providers: [AdapterService, Registry],
})
export class AdapterModule {}
