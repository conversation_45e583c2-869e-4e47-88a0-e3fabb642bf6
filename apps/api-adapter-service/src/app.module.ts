import { Module } from '@nestjs/common';
import { ESBModule } from './requests/esb/esb-module';
import { AdapterModule } from './adapter/adapter.module';
import {
  GlobalConfigModule,
  LookupEntity,
  LookupsModule,
  RedisModule,
} from '@modules';
import defaults from './config/defaults';
import { FCCModule } from './requests/fcc/fcc-module';
import { CibegModule } from './requests/cibeg/cibeg-module';
import { EstatementModule } from './requests/estatement/estatement-module';

@Module({
  imports: [
    GlobalConfigModule.forRoot(defaults),
    RedisModule.forRootAsync(),
    LookupsModule.forRoot([
      {
        name: LookupEntity.I18N_SERVER,
      },
    ]),
    ESBModule,
    FCCModule,
    CibegModule,
    EstatementModule,
    AdapterModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
