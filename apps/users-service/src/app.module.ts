import { Module } from '@nestjs/common';
import {
  ApiAdapterClient,
  CommonModule,
  GlobalConfigModule,
  JwtAuthModule,
  LookupEntity,
  LookupsModule,
  RedisModule,
  UserEligibilityModule,
} from '@modules';
import defaults from './config/defaults';
import { TransactionsModule } from './modules/transactions/transactions.module';
import { UsersModule } from './modules/users/users.module';
import { SecureMailDynamicListEnum } from '@enums';
import { EligibilityModule } from './modules/eligibility/eligibility.module';
import { UserPreferencesModule } from './modules/preferences/preferences.module';
@Module({
  imports: [
    GlobalConfigModule.forRoot(defaults),
    RedisModule.forRootAsync(),
    JwtAuthModule.forRoot(),
    ApiAdapterClient,
    TransactionsModule,
    UsersModule,
    LookupsModule.forRoot([
      {
        name: LookupEntity.I18N_SERVER,
      },
      {
        name: LookupEntity.CONTENT_LIST,
        filter: [
          SecureMailDynamicListEnum.PORTAL_TYPES,
          SecureMailDynamicListEnum.USER_PRIVILEGES,
          SecureMailDynamicListEnum.SECURE_MAIL_TYPES,
          SecureMailDynamicListEnum.ACCESS_PERMISSIONS,
          SecureMailDynamicListEnum.TOKEN_TYPES,
          SecureMailDynamicListEnum.PRODUCT_TYPES,
        ],
      },
      {
        name: LookupEntity.BANK_BRANCH,
      },
      {
        name: LookupEntity.BANK_BRANCH_GOVERNATE,
      },
      {
        name: LookupEntity.ACCOUNT_PRODUCT,
      },
    ]),
    CommonModule,
    UserEligibilityModule,
    EligibilityModule,
    UserPreferencesModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
