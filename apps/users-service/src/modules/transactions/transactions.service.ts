import { Injectable, Logger } from '@nestjs/common';
import {
  ApproveTransactionDto,
  RejectTransactionDto,
} from './transactions.dtos';
import {
  ApiAdapterClientService,
  GlobalConfigService,
  I18nLookupService,
  TransactionDetailsFormatterService,
  TransactionListFormatterService,
} from '@modules';
import {
  CurrentUser,
  TransactionApproveRejectResponse,
  TransactionHistory,
  TransactionsListResponse,
} from '@types';
import {
  FccRequestsEnum,
  ProductCodeEnum,
  TransactionApprovalStatusEnum,
  UserLanguage,
} from '@enums';
import {
  DownloadTransactionRequestDto,
  FccGenericTnxResponseDto,
  TransactionsListQueryDto,
} from '@dtos';
import { groupBy } from 'lodash';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class TransactionsService {
  constructor(
    readonly apiAdapterService: ApiAdapterClientService,
    readonly detailsFormatterService: TransactionDetailsFormatterService,
    readonly listFormatterService: TransactionListFormatterService,
    readonly i18n: I18nLookupService,
    readonly configService: GlobalConfigService,
  ) {}

  private readonly logger = new Logger(TransactionsService.name);

  private logListOutput(
    method: string,
    userId: string | undefined,
    filters: string[],
    count: number,
  ) {
    const payload = {
      msg: `${method}: ${count ? 'success' : 'empty'}`,
      userId,
      filters,
      count,
    };
    if (count === 0) this.logger.warn(payload);
    else this.logger.log(payload);
  }

  private logError(
    method: string,
    meta: Record<string, unknown>,
    error: unknown,
  ) {
    this.logger.error(
      { msg: `${method}: error`, ...meta },
      (error as Error)?.stack,
    );
  }

  /**
   * Retrieves the list of pending transactions using listdata.
   * @param params - Validated query params DTO.
   */
  async getMyPendingList(
    params: TransactionsListQueryDto,
    user: CurrentUser,
    userLanguage: UserLanguage,
  ) {
    const filters = Object.keys(params || {});
    try {
      const { rowDetails = [] } =
        await this.apiAdapterService.fccRequest<TransactionsListResponse>({
          requestId: FccRequestsEnum.MY_PENDING_TRANSACTIONS,
          payload: params,
          user,
        });
      this.logListOutput(
        'getMyPendingList',
        user?.userId,
        filters,
        rowDetails.length,
      );
      return this.listFormatterService.formatMany(rowDetails, userLanguage);
    } catch (error) {
      this.logError(
        'getMyPendingList',
        { userId: user?.userId, filters },
        error,
      );
      throw error;
    }
  }

  /**
   * Retrieves all transactions for activity log using listdata.
   * @param params - Validated query params DTO.
   */
  async getAllTransactionsList(
    params: TransactionsListQueryDto,
    user: CurrentUser,
    userLanguage: UserLanguage,
  ) {
    const filters = Object.keys(params || {});
    try {
      const { rowDetails = [] } =
        await this.apiAdapterService.fccRequest<TransactionsListResponse>({
          requestId: FccRequestsEnum.ALL_TRANSACTIONS,
          payload: params,
          user,
        });
      this.logListOutput(
        'getAllTransactionsList',
        user?.userId,
        filters,
        rowDetails.length,
      );
      return this.listFormatterService.formatMany(rowDetails, userLanguage);
    } catch (error) {
      this.logError(
        'getAllTransactionsList',
        { userId: user?.userId, filters },
        error,
      );
      throw error;
    }
  }

  /**
   * Retrieves all transactions initiated by current logged in user.
   * @param params - Validated query params DTO.
   */
  async getMyTransactionsList(
    params: TransactionsListQueryDto,
    user: CurrentUser,
    userLanguage: UserLanguage,
  ) {
    const payload = {
      ...params,
      inputterUserId: user.userId,
    };

    const filters = Object.keys(payload || {});
    try {
      const { rowDetails = [] } =
        await this.apiAdapterService.fccRequest<TransactionsListResponse>({
          requestId: FccRequestsEnum.ALL_TRANSACTIONS,
          payload,
          user,
        });
      this.logListOutput(
        'getMyTransactionsList',
        user?.userId,
        filters,
        rowDetails.length,
      );
      return this.listFormatterService.formatMany(rowDetails, userLanguage);
    } catch (error) {
      this.logError(
        'getMyTransactionsList',
        { userId: user?.userId, filters },
        error,
      );
      throw error;
    }
  }

  /**
   * Retrieves all transactions NOT initiated by current logged in user.
   * @param params - Validated query params DTO.
   */
  async getOthersTransactionsList(
    params: TransactionsListQueryDto,
    user: CurrentUser,
    userLanguage: UserLanguage,
  ) {
    const payload = {
      ...params,

      // If inputterUserId is provided, use it for filtering by specific user
      // Otherwise, exclude current logged in user's transactions
      ...(params.inputterUserId
        ? { inputterUserId: params.inputterUserId }
        : { excludeInputterUserId: user.userId }),
    };

    const filters = Object.keys(payload || {});
    try {
      const { rowDetails = [] } =
        await this.apiAdapterService.fccRequest<TransactionsListResponse>({
          requestId: FccRequestsEnum.ALL_TRANSACTIONS,
          payload,
          user,
        });
      this.logListOutput(
        'getOthersTransactionsList',
        user?.userId,
        filters,
        rowDetails.length,
      );
      return this.listFormatterService.formatMany(rowDetails, userLanguage);
    } catch (error) {
      this.logError(
        'getOthersTransactionsList',
        { userId: user?.userId, filters },
        error,
      );
      throw error;
    }
  }

  async approve(
    productCode: ProductCodeEnum,
    transactionId: string,
    body: ApproveTransactionDto,
    user: CurrentUser,
    userLanguage: UserLanguage,
  ) {
    const payload = {
      transactionId,
      referenceKey: body.referenceKey,
      ...(body.reauthPassword && { reauthSecret: body.reauthPassword }),
    };

    const { approvalStatus = TransactionApprovalStatusEnum.Unknown } =
      await this.apiAdapterService.fccRequest<TransactionApproveRejectResponse>(
        {
          requestId: FccRequestsEnum.APPROVE_TRANSACTION,
          payload,
          user,
        },
      );
    this.logger.log({
      msg: 'approve',
      userId: user?.userId,
      productCode,
      transactionId,
      approvalStatus,
      hasReauthPassword: Boolean(body?.reauthPassword),
      hasReferenceKey: Boolean(body?.referenceKey),
    });
    return this.approveRejectResponse(
      productCode,
      transactionId,
      approvalStatus,
      user,
      userLanguage,
    );
  }

  async reject(
    productCode: ProductCodeEnum,
    transactionId: string,
    body: RejectTransactionDto,
    user: CurrentUser,
    userLanguage: UserLanguage,
  ) {
    // Map reauthPassword from FE to reauthSecret for FCC API
    const payload = {
      transactionId,
      comments: body.comments,
      referenceKey: body.referenceKey,
      ...(body.reauthPassword && { reauthSecret: body.reauthPassword }),
    };

    const { approvalStatus = TransactionApprovalStatusEnum.Unknown } =
      await this.apiAdapterService.fccRequest<TransactionApproveRejectResponse>(
        {
          requestId: FccRequestsEnum.REJECT_TRANSACTION,
          payload,
          user,
        },
      );
    this.logger.log({
      msg: 'reject',
      userId: user?.userId,
      productCode,
      transactionId,
      approvalStatus,
      hasReauthPassword: Boolean(body?.reauthPassword),
      hasReferenceKey: Boolean(body?.referenceKey),
      hasComments: Boolean(body?.comments),
    });
    return this.approveRejectResponse(
      productCode,
      transactionId,
      approvalStatus,
      user,
      userLanguage,
    );
  }

  async getDetails(
    eventId: string,
    productCode: ProductCodeEnum,
    user: CurrentUser,
    userLanguage: UserLanguage,
  ) {
    const history = await this.getHistory(eventId, user, true);

    const { rawDetails, details } = await this.getFormattedDetails(
      eventId,
      productCode,
      user,
      userLanguage,
    );

    this.logger.log({
      msg: 'getDetails',
      userId: user?.userId,
      productCode,
      eventId,
      hasDetails: Boolean(details),
    });

    return {
      rawDetails: this.configService.isLocalEnvironment() ? rawDetails : {},
      details,
      history,
    };
  }

  async getHistory(id: string, user: CurrentUser, grouped: boolean = false) {
    const results = await this.apiAdapterService.fccRequest<TransactionHistory>(
      {
        requestId: FccRequestsEnum.TRANSACTION_HISTORY,
        payload: {
          transactionId: id,
        },
        user,
      },
    );

    if (results?.items && grouped) {
      const groupedResults = groupBy(results.items, (item) =>
        item?.role?.toLowerCase(),
      );
      this.logger.log({
        msg: 'getHistory',
        userId: user?.userId,
        transactionId: id,
        grouped,
        count: Object.keys(groupedResults).length,
      });
      return groupedResults;
    }

    this.logger.log({
      msg: 'getHistory',
      userId: user?.userId,
      transactionId: id,
      grouped,
      count: results?.items?.length ?? 0,
    });
    return results?.items;
  }

  async getFormattedDetails(
    id: string,
    productCode: ProductCodeEnum,
    user: CurrentUser,
    userLanguage: UserLanguage,
  ) {
    const details =
      await this.apiAdapterService.fccRequest<FccGenericTnxResponseDto>({
        requestId: FccRequestsEnum.TRANSACTION_DETAILS,
        payload: {
          eventId: id,
          productCode,
        },
        user,
      });

    return {
      rawDetails: details,
      details: this.detailsFormatterService.format(
        details,
        productCode,
        userLanguage,
      ),
    };
  }

  private async approveRejectResponse(
    productCode: ProductCodeEnum,
    transactionId: string,
    approvalStatus: TransactionApprovalStatusEnum,
    user: CurrentUser,
    userLanguage: UserLanguage,
  ) {
    const { details } = await this.getFormattedDetails(
      transactionId,
      productCode,
      user,
      userLanguage,
    );

    return {
      approvalStatus,
      message: this.i18n.translate(
        `messages.transactions.${approvalStatus}`,
        userLanguage,
      ),
      details,
    };
  }

  async downloadTransaction(
    payload: DownloadTransactionRequestDto,
    user: CurrentUser,
  ) {
    const response = await this.apiAdapterService.fccRequest({
      requestId: FccRequestsEnum.DOWNLOAD_TRANSACTION,
      payload,
      user,
    });

    this.logger.debug(
      `Downloading transaction with RefID: ${payload.referenceId}`,
    );

    return response;
  }
}
