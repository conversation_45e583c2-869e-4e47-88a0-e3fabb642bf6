import {
  Controller,
  Body,
  Post,
  Param,
  Get,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApproveTransactionDto,
  RejectTransactionDto,
  TransactionDetailsDto,
} from './transactions.dtos';
import { TransactionsService } from './transactions.service';
import { CurrentUser } from '@types';
import { getCurrentUser, getUserLanguage, JwtAuthGuard } from '@modules';
import { ProductCodeEnum, UserLanguage } from '@enums';
import { TransactionsListQueryDto, DownloadTransactionRequestDto } from '@dtos';

@Controller('transactions')
@UseGuards(JwtAuthGuard)
export class TransactionsController {
  constructor(readonly transactionsService: TransactionsService) {}

  @Get('my-pending')
  async getMyPendingTransactions(
    @Query() query: TransactionsListQueryDto,
    @getCurrentUser() currentUser: CurrentUser,
    @getUserLanguage() userLanguage: UserLanguage,
  ) {
    return this.transactionsService.getMyPendingList(
      query,
      currentUser,
      userLanguage,
    );
  }

  @Get('my-transactions')
  async getMyTransactions(
    @Query() query: TransactionsListQueryDto,
    @getCurrentUser() currentUser: CurrentUser,
    @getUserLanguage() userLanguage: UserLanguage,
  ) {
    return this.transactionsService.getMyTransactionsList(
      query,
      currentUser,
      userLanguage,
    );
  }

  @Get('others-transactions')
  async getOthersTransactions(
    @Query() query: TransactionsListQueryDto,
    @getCurrentUser() currentUser: CurrentUser,
    @getUserLanguage() userLanguage: UserLanguage,
  ) {
    return this.transactionsService.getOthersTransactionsList(
      query,
      currentUser,
      userLanguage,
    );
  }

  @Get('all')
  async getAllTransactions(
    @Query() query: TransactionsListQueryDto,
    @getCurrentUser() currentUser: CurrentUser,
    @getUserLanguage() userLanguage: UserLanguage,
  ) {
    return this.transactionsService.getAllTransactionsList(
      query,
      currentUser,
      userLanguage,
    );
  }

  @Post('/:productCode/:id/approve')
  async approve(
    @Param('productCode') productCode: ProductCodeEnum,
    @Param('id') id: string,
    @Body() body: ApproveTransactionDto,
    @getCurrentUser() currentUser: CurrentUser,
    @getUserLanguage() userLanguage: UserLanguage,
  ) {
    return this.transactionsService.approve(
      productCode,
      id,
      body,
      currentUser,
      userLanguage,
    );
  }

  @Post('/:productCode/:id/reject')
  async reject(
    @Param('productCode') productCode: ProductCodeEnum,
    @Param('id') id: string,
    @Body() body: RejectTransactionDto,
    @getCurrentUser() currentUser: CurrentUser,
    @getUserLanguage() userLanguage: UserLanguage,
  ) {
    return this.transactionsService.reject(
      productCode,
      id,
      body,
      currentUser,
      userLanguage,
    );
  }

  @Get('/:productCode/:eventId')
  async getDetails(
    @Param() { eventId, productCode }: TransactionDetailsDto,
    @getCurrentUser() currentUser: CurrentUser,
    @getUserLanguage() userLanguage: UserLanguage,
  ) {
    return this.transactionsService.getDetails(
      eventId,
      productCode,
      currentUser,
      userLanguage,
    );
  }

  @Get('/:id/history')
  async getHistory(
    @Param('id') id: string,
    @getCurrentUser() currentUser: CurrentUser,
  ) {
    return this.transactionsService.getHistory(id, currentUser);
  }

  @Get('download')
  async downloadTransaction(
    @Query() query: DownloadTransactionRequestDto,
    @getCurrentUser() currentUser: CurrentUser,
  ) {
    return await this.transactionsService.downloadTransaction(
      query,
      currentUser,
    );
  }
}
