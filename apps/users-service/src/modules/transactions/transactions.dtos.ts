import { ProductCodeEnum } from '@enums';
import { IsString, IsNotEmpty, IsOptional, IsEnum } from 'class-validator';

export enum TransactionStatusEnum {
  DRAFT = 'DRAFT',
  PENDING_APPROVAL = 'PENDING-APPROVAL',
  SUBMITTED = 'SUBMITTED',
  ACKNOWLEDGED = 'ACKNOWLEDGED',
  ERROR = 'ERROR',
  CANCELLED = 'CANCELLED',
  BANK_PENDING = 'BANK-PENDING',
  FAILED = 'FAILED',
  REVISE = 'REVISE',
  REJECTED = 'REJECTED',
}

export class ReauthDto {
  @IsOptional()
  referenceKey: string;

  @IsOptional()
  reauthPassword: string;
}

export class RejectTransactionDto extends ReauthDto {
  @IsString()
  @IsNotEmpty()
  comments: string;
}

export class ApproveTransactionDto extends ReauthDto {}

export class TransactionDetailsDto {
  @IsNotEmpty()
  @IsString()
  @IsEnum(ProductCodeEnum)
  productCode: ProductCodeEnum;

  @IsNotEmpty()
  eventId: string;
}
