import { Body, Controller, Get, Post, UseGuards } from '@nestjs/common';
import { UsersService } from './users.service';
import { getCurrentUser, JwtAuthGuard } from '@modules';
import { CurrentUser } from '@types';
import { UserReauthenticationDto } from '@dtos';

@Controller('users')
@UseGuards(JwtAuthGuard)
export class UsersController {
  constructor(private readonly userService: UsersService) {}

  @Get('permissions')
  getUserPermissions(@getCurrentUser() currentUser: CurrentUser) {
    return this.userService.getUserPermissions(currentUser);
  }

  @Get('details')
  getUserDetails(@getCurrentUser() currentUser: CurrentUser) {
    return this.userService.getUserDetails(currentUser);
  }

  @Post('/reauthenticate')
  async reAuthenticate(
    @Body() body: UserReauthenticationDto,
    @getCurrentUser() currentUser: CurrentUser,
  ) {
    return this.userService.reAuthenticate(body, currentUser);
  }
}
