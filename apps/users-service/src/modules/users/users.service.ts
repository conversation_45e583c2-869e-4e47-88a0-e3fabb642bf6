import { FccRequestsEnum, FccTransfersTypesEnum } from '@enums';
import { Injectable } from '@nestjs/common';
import { FccClientService, UsersService as GlobalUsersService } from '@modules';
import { CurrentUser } from '@types';
import { UserReauthenticationDto } from '@dtos';

@Injectable()
export class UsersService {
  constructor(
    readonly apiAdapterService: FccClientService,
    readonly usersService: GlobalUsersService,
  ) {}

  async getUserPermissions(user: CurrentUser) {
    return this.apiAdapterService.call({
      requestId: FccRequestsEnum.USER_PERMISSIONS,
      user,
    });
  }

  async getUserDetails(user: CurrentUser) {
    const { fccProfile, ...esbDetails } =
      await this.usersService.getCustomerDetails(user);

    const { customerReferences, ...profile } = fccProfile;

    return {
      ...profile,
      relationshipManager: esbDetails.relationshipManager,
      customerId: esbDetails.userId,
    };
  }

  async reAuthenticate(payload: UserReauthenticationDto, user: CurrentUser) {
    //CRD is mapped to credentials in fcc
    if (
      payload.subProductCode &&
      payload.subProductCode === FccTransfersTypesEnum.CRD
    ) {
      delete payload.subProductCode;
    }

    const reAuth = await this.apiAdapterService.call<Record<string, string>>({
      requestId: FccRequestsEnum.USER_REAUTHENTICATION_TYPE,
      payload,
      user,
    });

    let config: Record<string, string>;

    if (reAuth?.reauthenticationType === 'OTP') {
      config = await this.apiAdapterService.call({
        requestId: FccRequestsEnum.CONFIGURATION_DETAILS,
        payload: {
          keys: ['RESEND_OTP_MAX_COUNT', 'OTP_MAX_LENGTH'],
        },
        user,
      });
    }

    return { ...reAuth, config };
  }
}
