import { eligibilityPolicyMap, UserEligibilityService } from '@modules';
import { Injectable, NotFoundException } from '@nestjs/common';
import { CurrentUser } from '@types';
import { UsersService as GlobalUsersService } from '@modules';
import { CheckEligibilityDto } from './eligibility.dtos';
import { UserLanguage } from '@enums';

@Injectable()
export class EligibilityService {
  constructor(
    private readonly eligibilityService: UserEligibilityService,
    readonly usersService: GlobalUsersService,
  ) {}

  async checkEligibility(
    body: CheckEligibilityDto,
    user: CurrentUser,
    userLanguage: UserLanguage,
  ) {
    if (eligibilityPolicyMap[body.operation]) {
      const customerDetails = await this.usersService.getCustomerDetails(user);
      return this.eligibilityService.evaluate(
        body.operation,
        customerDetails,
        userLanguage,
      );
    }
    throw new NotFoundException();
  }
}
