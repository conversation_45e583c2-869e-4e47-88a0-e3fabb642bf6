import { Body, Controller, Get, Post, UseGuards } from '@nestjs/common';
import { EligibilityService } from './eligibility.service';
import { getCurrentUser, getUserLanguage, JwtAuthGuard } from '@modules';
import { CurrentUser } from '@types';
import { CheckEligibilityDto } from './eligibility.dtos';
import { UserLanguage } from '@enums';

@Controller('eligibility')
@UseGuards(JwtAuthGuard)
export class EligibilityController {
  constructor(private readonly eligibilityService: EligibilityService) {}

  @Post('check')
  checkEligibility(
    @Body() body: CheckEligibilityDto,
    @getCurrentUser() currentUser: CurrentUser,
    @getUserLanguage() userLanguage: UserLanguage,
  ) {
    return this.eligibilityService.checkEligibility(
      body,
      currentUser,
      userLanguage,
    );
  }
}
