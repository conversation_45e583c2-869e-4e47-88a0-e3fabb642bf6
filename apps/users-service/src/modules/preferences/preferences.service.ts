import { FccRequestsEnum } from '@enums';
import { Injectable } from '@nestjs/common';
import {
  FccClientService,
  RedisCacheService,
  RedisUsersService,
} from '@modules';
import { CurrentUser } from '@types';
import {
  ChangeCurrencyRequestDto,
  ChangeDefaultAccountRequestDto,
  ChangeLanguageRequestDto,
} from './preferences.dto';

@Injectable()
export class UserPreferencesService {
  constructor(
    readonly clientService: FccClientService,
    readonly redisUsersService: RedisUsersService,
  ) {}
  async changeLanguage(body: ChangeLanguageRequestDto, user: CurrentUser) {
    await this.clientService.fccRequest({
      requestId: FccRequestsEnum.CHANGE_LANGUAGE,
      payload: body,
      user,
    });
    this.redisUsersService.invalidateUserData(user);

    return { response: 'success' };
  }

  async changeCurrency(body: ChangeCurrencyRequestDto, user: CurrentUser) {
    await this.clientService.fccRequest({
      requestId: FccRequestsEnum.CHANGE_CURRENCY,
      payload: body,
      user,
    });
    // do not use await, run it at the backround
    this.redisUsersService.invalidateUserData(user);

    return { response: 'success' };
  }

  async changeDefaultAccount(
    body: ChangeDefaultAccountRequestDto,
    user: CurrentUser,
  ) {
    await this.clientService.fccRequest({
      requestId: FccRequestsEnum.CHANGE_DEFAULT_ACCOUNT,
      payload: body,
      user,
    });
    // do not use await, run it at the backround
    this.redisUsersService.invalidateUserData(user);

    return { response: 'success' };
  }
}
