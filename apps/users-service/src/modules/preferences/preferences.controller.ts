import {
  Body,
  Controller,
  HttpCode,
  HttpStatus,
  Post,
  UseGuards,
} from '@nestjs/common';
import { UserPreferencesService } from './preferences.service';
import { getCurrentUser, JwtAuthGuard } from '@modules';
import { CurrentUser } from '@types';
import {
  ChangeCurrencyRequestDto,
  ChangeDefaultAccountRequestDto,
  ChangeLanguageRequestDto,
} from './preferences.dto';

@Controller('preferences')
@UseGuards(JwtAuthGuard)
export class UserPreferencesController {
  constructor(private readonly preferencesService: UserPreferencesService) {}

  @HttpCode(HttpStatus.OK)
  @Post('change-language')
  async changeLanguage(
    @Body() body: ChangeLanguageRequestDto,
    @getCurrentUser() currentUser: CurrentUser,
  ) {
    return this.preferencesService.changeLanguage(body, currentUser);
  }

  @HttpCode(HttpStatus.OK)
  @Post('change-currency')
  async changeCurrency(
    @Body() body: ChangeCurrencyRequestDto,
    @getCurrentUser() currentUser: CurrentUser,
  ) {
    return this.preferencesService.changeCurrency(body, currentUser);
  }

  @HttpCode(HttpStatus.OK)
  @Post('change-default-account')
  async changeDefaultAccount(
    @Body() body: ChangeDefaultAccountRequestDto,
    @getCurrentUser() currentUser: CurrentUser,
  ) {
    return this.preferencesService.changeDefaultAccount(body, currentUser);
  }
}
