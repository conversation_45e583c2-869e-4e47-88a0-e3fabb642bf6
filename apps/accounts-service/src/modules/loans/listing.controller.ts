import { Controller, Get, Param, Query, UseGuards } from '@nestjs/common';
import { getCurrentUser, getUserLanguage, JwtAuthGuard } from '@modules';
import { CurrentUser } from '@types';
import { UserLanguage } from '@enums';
import { OwnListService } from '@/modules/loans/services/own-list.service';
import { DetailsService } from '@/modules/loans/services/detail.service';
import { LoanListQueryDto } from '@dtos';

@Controller('loans')
@UseGuards(JwtAuthGuard)
export class ListingController {
  constructor(
    private readonly ownListService: OwnListService,
    private readonly detailService: DetailsService,
  ) {}

  @Get()
  async getAccountByTypeList(
    @Query() query: LoanListQueryDto,
    @getCurrentUser() currentUser: CurrentUser,
    @getUserLanguage() userLanguage: UserLanguage,
  ) {
    return this.ownListService.getList(query, currentUser, userLanguage);
  }

  @Get(':id')
  async getDepositDetails(
    @Param('id') id: string,
    @getCurrentUser() currentUser: CurrentUser,
    @getUserLanguage() userLanguage: UserLanguage,
  ) {
    return this.detailService.getDetails(id, currentUser, userLanguage);
  }
}
