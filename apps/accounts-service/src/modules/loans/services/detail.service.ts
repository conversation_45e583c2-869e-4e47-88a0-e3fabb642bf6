import { Injectable } from '@nestjs/common';
import { CurrentUser, LoanId } from '@types';
import { UserLanguage } from '@enums';
import { decryptUserJson } from '@helpers';
import { ConfigService } from '@nestjs/config';
import { LoanDetailsService } from '@modules';

@Injectable()
export class DetailsService {
  constructor(
    private readonly configService: ConfigService,
    private readonly detailsService: LoanDetailsService,
  ) {}

  async getDetails(
    encryptedId: string,
    currentUser: CurrentUser,
    userLanguage: UserLanguage,
  ) {
    const { accountId } = decryptUserJson<LoanId>(
      encryptedId,
      this.configService.get('aesSecret'),
      currentUser.userId,
    );

    return this.detailsService.getDetails(accountId, currentUser, {
      userLanguage,
    });
  }
}
