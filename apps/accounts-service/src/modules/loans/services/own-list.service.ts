import { Injectable } from '@nestjs/common';
import { CurrentUser } from '@types';
import { UserLanguage } from '@enums';
import { LoansListService, UsersService } from '@modules';
import { LoanListQueryDto } from '@dtos';

@Injectable()
export class OwnListService {
  constructor(
    private loanListService: LoansListService,
    private readonly usersService: UsersService,
  ) {}

  async getList(
    query: LoanListQueryDto,
    currentUser: CurrentUser,
    userLanguage: UserLanguage = UserLanguage['en-US'],
  ) {
    const defaultCurrency =
      await this.usersService.getDefaultCurrency(currentUser);

    return this.loanListService.getOwnList(
      currentUser,
      {
        userLanguage,
        defaultCurrency,
      },
      query,
    );
  }
}
