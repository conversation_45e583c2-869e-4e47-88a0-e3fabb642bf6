import { Body, Controller, Get, Param, Query, UseGuards } from '@nestjs/common';
import { getCurrentUser, getUserLanguage, JwtAuthGuard } from '@modules';
import { CurrentUser } from '@types';
import { AccountMovementsListService } from './services/list.service';
import {
  AccountMovementsDownloadRequestDto,
  AccountMovementsListRequestDto,
} from '@dtos';
import { UserLanguage } from '@enums';
import { AdviceDownloadRequestDto } from './movements.dtos';
import { AdviseService } from './services/advise.service';

@UseGuards(JwtAuthGuard)
@Controller('movements')
export class AccountMovementsController {
  constructor(
    private readonly movementsList: AccountMovementsListService,
    private readonly adviceService: AdviseService,
  ) {}

  @Get(':accountId')
  async getMovementsList(
    @Param('accountId') accountId: string,
    @Query() query: AccountMovementsListRequestDto,
    @getCurrentUser() currentUser: CurrentUser,
    @getUserLanguage() userLanguage: UserLanguage,
  ) {
    return this.movementsList.getList(
      accountId,
      query,
      currentUser,
      userLanguage,
    );
  }

  @Get(':accountId/download')
  async downloadMovementsList(
    @Param('accountId') accountId: string,
    @Query() query: AccountMovementsDownloadRequestDto,
    @getCurrentUser() currentUser: CurrentUser,
    @getUserLanguage() userLanguage: UserLanguage,
  ) {
    return this.movementsList.downloadList(
      accountId,
      query,
      currentUser,
      userLanguage,
    );
  }

  @Get('advice/:refId')
  async downloadAdvice(
    @Param('refId') refId: string,
    @Query() query: AdviceDownloadRequestDto,
    @getCurrentUser() currentUser: CurrentUser,
  ) {
    return this.adviceService.downloadAdvise(refId, query, currentUser);
  }
}
