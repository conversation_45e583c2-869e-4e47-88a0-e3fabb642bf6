import { ApiAdapterClientService, FccClientService } from '@modules';
import { CurrentUser } from '@types';
import {
  ESBAdviceTypeEnum,
  EsbRequestsEnum,
  FccRequestsEnum,
  MovementAdviceTypeEnum,
} from '@enums';
import { Injectable, ServiceUnavailableException } from '@nestjs/common';
import { AdviceDownloadRequestDto } from '../movements.dtos';

@Injectable()
export class AdviseService {
  constructor(
    private readonly clientService: FccClientService,
    private readonly apiClientService: ApiAdapterClientService,
  ) {}

  //to be moved into internal pdf generator system
  async downloadAdvise(
    refId: string,
    query: AdviceDownloadRequestDto,
    user: CurrentUser,
  ): Promise<any> {
    if (query.type == MovementAdviceTypeEnum.Swift) {
      await this.validateSwift(refId);
    }

    const response = await this.clientService.call<any>({
      requestId: FccRequestsEnum.MOVEMENT_ADVICE_DOWNLOAD,
      payload: {
        boReferenceId: refId,
        ...query,
      },
      user,
    });

    return response;
  }

  async validateSwift(refId: string) {
    const { swiftMessage } = await this.apiClientService.esbRequest<any>({
      requestId: EsbRequestsEnum.SWIFT_CONFIRMATION,
      payload: {
        referenceId: refId.replace(/^BG/, ''),
        adviceType: ESBAdviceTypeEnum.SwiftConfirmation,
      },
    });

    if (swiftMessage.trim().length !== 0) {
      return true;
    }

    throw new ServiceUnavailableException();
  }
}
