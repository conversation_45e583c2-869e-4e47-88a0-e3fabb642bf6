import { FccClientService } from '@modules';
import { CurrentUser } from '@types';
import { FccRequestsEnum, UserLanguage } from '@enums';
import { Injectable } from '@nestjs/common';
import {
  AccountMovementsDownloadRequestDto,
  AccountMovementsListRequestDto,
} from '@dtos';

@Injectable()
export class AccountMovementsListService {
  constructor(private readonly clientService: FccClientService) {}

  async getList(
    accountId: string,
    query: AccountMovementsListRequestDto,
    user: CurrentUser,
    userLanguage: UserLanguage,
  ): Promise<any> {
    const results = await this.clientService.call<any>({
      requestId: FccRequestsEnum.ACCOUNT_MOVEMENTS_LIST,
      payload: {
        ...query,
        accountId,
      },
      user,
      options: { language: userLanguage },
    });
    return results;
  }

  async downloadList(
    accountId: string,
    query: AccountMovementsDownloadRequestDto,
    user: CurrentUser,
    userLanguage: UserLanguage,
  ): Promise<any> {
    const statements = await this.clientService.call<any>({
      requestId: FccRequestsEnum.ACCOUNT_MOVEMENTS_DOWNLOAD,
      payload: {
        ...query,
        accountId,
      },
      user,
      options: { language: userLanguage },
    });
    return statements;
  }
}
