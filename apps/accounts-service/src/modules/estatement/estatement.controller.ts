import { Controller, Get, Param, UseGuards } from '@nestjs/common';
import { getCurrentUser, getUserLanguage, JwtAuthGuard } from '@modules';
import { CurrentUser } from '@types';
import { AccountStatementService } from './estatement.service';
import { UserLanguage } from '@enums';

@UseGuards(JwtAuthGuard)
@Controller('statement')
export class AccountStatementController {
  constructor(
    private readonly accountStatementService: AccountStatementService,
  ) {}

  @Get(':accountId')
  async getAccountStatementURL(
    @Param('accountId') accountId: string,
    @getCurrentUser() currentUser: CurrentUser,
    @getUserLanguage() userLanguage: UserLanguage,
  ) {
    return this.accountStatementService.getStatementURL(
      accountId,
      currentUser,
      userLanguage,
    );
  }
}
