import { ApiAdapterClientService, FccClientService } from '@modules';
import { CurrentUser } from '@types';
import {
  EstatementRequestsEnum,
  FccRequestsEnum,
  RequestModuleEnum,
  UserLanguage,
} from '@enums';
import { Injectable } from '@nestjs/common';
import { FCCAccountDetailDto } from '@dtos';

@Injectable()
export class AccountStatementService {
  constructor(private readonly clientService: ApiAdapterClientService) {}

  async getStatementURL(
    accountId: string,
    user: CurrentUser,
    userLanguage: UserLanguage,
  ): Promise<any> {
    const { number } = await this.clientService.fccRequest<FCCAccountDetailDto>(
      {
        requestId: FccRequestsEnum.ACCOUNT_DETAILS,
        payload: { accountId },
        user: user,
        options: { language: userLanguage },
      },
    );

    return this.clientService.call<any>({
      module: RequestModuleEnum.ESTATEMENT,
      requestId: EstatementRequestsEnum.ENCRYPTION,
      user,
      payload: {
        account: number,
        accountType: 1,
      },
      options: { language: userLanguage },
    });
  }
}
