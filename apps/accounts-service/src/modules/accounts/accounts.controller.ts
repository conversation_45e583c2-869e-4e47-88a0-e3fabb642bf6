import {
  Body,
  Controller,
  Get,
  HttpCode,
  Param,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { AccountsValidationService } from './services/validation.service';
import {
  AccountsListQueryDto,
  ValidateAccountRequestDTO,
} from './accounts.dtos';
import { AccountsTransferListService } from './services/transfer-list.service';
import { getCurrentUser, getUserLanguage, JwtAuthGuard } from '@modules';
import { CurrentUser } from '@types';
import { AccountsDetailedListService } from './services/detailed-list.service';
import { AccountsDetailsService } from './services/details.service';
import { UserLanguage } from '@enums';

@Controller('accounts')
@UseGuards(JwtAuthGuard)
export class AccountsController {
  constructor(
    private readonly validationService: AccountsValidationService,
    private readonly transferListService: AccountsTransferListService,
    private readonly detailedListService: AccountsDetailedListService,
    private readonly detailsService: AccountsDetailsService,
  ) {}

  @Get('list')
  async getTransferList(
    @Query() query: AccountsListQueryDto,
    @getCurrentUser() currentUser: CurrentUser,
    @getUserLanguage() userLanguage: UserLanguage,
  ) {
    return this.transferListService.getList(query, currentUser, userLanguage);
  }

  @Get('detailed-list')
  async getAccountList(
    @getCurrentUser() currentUser: CurrentUser,
    @getUserLanguage() userLanguage: UserLanguage,
  ) {
    const res = await this.detailedListService.getList(
      currentUser,
      userLanguage,
    );
    return res;
  }

  @Get('/:id/details')
  async getAccount(
    @Param('id') id: number,
    @getCurrentUser() currentUser: CurrentUser,
    @getUserLanguage() userLanguage: UserLanguage,
  ) {
    return this.detailsService.getDetails(id, currentUser, userLanguage);
  }

  @Post('validate')
  @HttpCode(200)
  async validateAccount(
    @Body() body: ValidateAccountRequestDTO,
    @getCurrentUser() currentUser: CurrentUser,
    @getUserLanguage() userLanguage: UserLanguage,
  ) {
    return this.validationService.validateAccount(
      body,
      currentUser,
      userLanguage,
    );
  }
}
