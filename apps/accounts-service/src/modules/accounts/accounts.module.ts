import { Module } from '@nestjs/common';
import { AccountsController } from './accounts.controller';
import { AccountsValidationService } from './services/validation.service';
import { AccountsTransferListService } from './services/transfer-list.service';
import { AccountsDetailedListService } from './services/detailed-list.service';
import { AccountsDetailsService } from './services/details.service';

@Module({
  controllers: [AccountsController],
  providers: [
    AccountsValidationService,
    AccountsTransferListService,
    AccountsDetailsService,
    AccountsDetailedListService,
  ],
})
export class AccountsModule {}
