import { Injectable } from '@nestjs/common';
import { ApiAdapterClientService, UsersService } from '@modules';
import { CurrentUser } from '@types';
import { FccRequestsEnum, UserLanguage } from '@enums';
import { AccountsListQueryDto } from '../accounts.dtos';
import { FccAccountListDto, FccAccountListItemDto } from '@dtos';

@Injectable()
export class AccountsTransferListService {
  constructor(
    private readonly apiAdapterService: ApiAdapterClientService,
    private readonly usersService: UsersService,
  ) {}

  async getList(
    { context }: AccountsListQueryDto,
    user: CurrentUser,
    userLanguage: UserLanguage,
  ) {
    const list: any = await this.apiAdapterService.fccRequest({
      requestId: FccRequestsEnum.ACCOUNTS_LIST,
      payload: {
        context,
      },
      user,
      options: { language: userLanguage },
    });

    const { defaultAccount } = await this.usersService.getUserData(user);

    if (defaultAccount) {
      const defaultIndex = list.items.findIndex(
        (account) => account.id === defaultAccount,
      );

      if (defaultIndex) {
        const [defaultAcc] = list.items.splice(defaultIndex, 1);
        list.items.unshift(defaultAcc);
      }
    }

    return list;
  }
}
