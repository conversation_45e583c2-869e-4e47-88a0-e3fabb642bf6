import { Injectable } from '@nestjs/common';
import { ApiAdapterClientService } from '@modules';
import { CurrentUser } from '@types';
import {
  DetailedAccountTypeWithBalanceQueryEnum,
  FccRequestsEnum,
  UserLanguage,
} from '@enums';
import { FccAccountListDto } from '@dtos';

@Injectable()
export class AccountsDetailedListService {
  constructor(private readonly apiClientService: ApiAdapterClientService) {}

  async getList(currentUser: CurrentUser, userLanguage: UserLanguage) {
    const [currentResults, otherResult] = await Promise.all([
      this.apiClientService.fccRequest<FccAccountListDto>({
        requestId: FccRequestsEnum.DETAILED_ACCOUNTS_LIST,
        user: currentUser,
        payload: {
          accountTypeForBalance:
            DetailedAccountTypeWithBalanceQueryEnum.CURRENT,
        },
      }),
      this.apiClientService.fccRequest<FccAccountListDto>({
        requestId: FccRequestsEnum.DETAILED_ACCOUNTS_LIST,
        user: currentUser,
        payload: {
          accountTypeForBalance: DetailedAccountTypeWithBalanceQueryEnum.OTHER,
        },
        options: { language: userLanguage },
      }),
    ]);

    return {
      currentAccountList: this.constructResponse(currentResults),
      otherAccountList: this.constructResponse(otherResult, true),
    };
  }

  //segregate current and overdraft accounts and calculate the total balances
  private constructResponse(list: FccAccountListDto, isOther?: boolean) {
    let equivalentAvailableBalanceTotal: number = 0;
    let equivalentTotalBalance: number = 0;

    let overdraftAvailableBalanceTotal: number = 0;
    let equivalentOverdraftTotalBalance: number = 0;
    const accountList = [];
    const overdraftAccountList = [];

    for (const account of list.items) {
      if (account.overdraftLimit > 0 && !isOther) {
        overdraftAvailableBalanceTotal += Number(
          account.availableBalanceEquivalent,
        );
        equivalentOverdraftTotalBalance += Number(
          account.overdraftLimitEquivalent,
        );
        overdraftAccountList.push(account);
      } else {
        equivalentAvailableBalanceTotal += Number(
          account.availableBalanceEquivalent,
        );
        equivalentTotalBalance += Number(account.ledgerBalanceEquivalent);
        accountList.push(account);
      }
    }

    if (isOther) {
      return {
        equivalentAvailableBalanceTotal,
        equivalentTotalBalance,
        accountList,
      };
    }

    return {
      equivalentAvailableBalanceTotal,
      equivalentTotalBalance,
      overdraftAvailableBalanceTotal,
      equivalentOverdraftTotalBalance,
      overdraftAccountList,
      accountList,
    };
  }
}
