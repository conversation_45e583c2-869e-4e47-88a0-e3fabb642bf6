import {
  ApiAdapterClientService,
  AccountsValidationService as GlobalValidationService,
} from '@modules';
import { Injectable, Logger } from '@nestjs/common';
import { ValidateAccountRequestDTO } from '../accounts.dtos';
import { CurrentUser } from '@types';
import { UserLanguage } from '@enums';

@Injectable()
export class AccountsValidationService {
  private readonly logger = new Logger(AccountsValidationService.name);

  constructor(
    private readonly apiClientService: ApiAdapterClientService,
    private readonly validationService: GlobalValidationService,
  ) {}

  async validateAccount(
    data: ValidateAccountRequestDTO,
    currentUser: CurrentUser,
    userLanguage: UserLanguage,
  ) {
    const results = await this.validationService.validateAccount(
      data.accountNumber,
      currentUser,
      userLanguage,
    );

    if (results?.ownAccount) {
      return {
        isValid: false,
        ownAccount: true,
      };
    }

    return results;
  }
}
