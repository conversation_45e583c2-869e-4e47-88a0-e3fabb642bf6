import { Injectable } from '@nestjs/common';
import { ApiAdapterClientService } from '@modules';
import { CurrentUser } from '@types';
import { EsbRequestsEnum, FccRequestsEnum, UserLanguage } from '@enums';
import {
  EsbAccountDetailsDto,
  EsbDebitCardListItemDto,
  FCCAccountDetailDto,
} from '@dtos';

@Injectable()
export class AccountsDetailsService {
  constructor(private readonly apiClientService: ApiAdapterClientService) {}

  async getDetails(
    id: number,
    currentUser: CurrentUser,
    userLanguage: UserLanguage,
  ) {
    const fccAccountDetails =
      await this.apiClientService.fccRequest<FCCAccountDetailDto>({
        requestId: FccRequestsEnum.ACCOUNT_DETAILS,
        payload: { accountId: id },
        user: currentUser,
        options: { language: userLanguage },
      });
    const [esbAccountDetails, esbDebitCards] = await Promise.all([
      this.apiClientService.esbRequest<EsbAccountDetailsDto>({
        requestId: EsbRequestsEnum.ACCOUNT_DETAILS,
        payload: {
          accountNumber: fccAccountDetails.number,
        },
        options: { language: userLanguage },
      }),
      this.apiClientService
        .esbRequest<EsbDebitCardListItemDto[]>({
          requestId: EsbRequestsEnum.LINKED_DEBIT_CARDS,
          payload: {
            accountNumber: fccAccountDetails.number,
          },
          options: { language: userLanguage },
        })
        .catch(() => {
          return [];
        }),
    ]);
    return this.constructDetailResponse(
      fccAccountDetails,
      esbAccountDetails,
      esbDebitCards,
    );
  }

  private constructDetailResponse(
    fccDetails: FCCAccountDetailDto,
    esbDetails: EsbAccountDetailsDto,
    esbDebitCards: EsbDebitCardListItemDto[],
  ) {
    return {
      accountNumber: fccDetails.number,
      accountIban: esbDetails.accountIban,
      customerNumber: fccDetails.customerReference,

      accountNickName: fccDetails.balances.nickname,

      accountType: fccDetails.type,
      productType: esbDetails.accountType,
      productName: esbDetails.productInfo.productName,
      productCode: esbDetails.productInfo.productCode,

      categoryCode: esbDetails.categoryCode,
      accountDescription: esbDetails.accountTypeDesc,

      isOverdraft: !!(
        esbDetails?.accountTrnLimit?.overdraft?.currentAmount !== 0
      ),
      overdraftExpiryDate:
        esbDetails?.accountTrnLimit?.overdraft?.expiryDate ?? '',
      overdraftLimit: esbDetails?.accountTrnLimit?.overdraft?.currentAmount,

      openingDate: esbDetails.openDate,

      ledgerBalance:
        fccDetails.balances.ledgerBalance || fccDetails.balances.overdraftLimit,
      ledgerBalanceEquivalent:
        fccDetails.balances.ledgerBalanceEquivalent ||
        fccDetails.balances.overdraftLimitEquivalent,
      availableBalance: fccDetails.balances.availableBalance,
      availableBalanceEquivalent:
        fccDetails.balances.availableBalanceEquivalent,
      equivalentCurrency: fccDetails.balances.equivalentCurrency,
      heldAmount: fccDetails.balances.heldAmount,
      heldAmountEquivalent: fccDetails.balances.heldAmountEquivalent,

      accountCurrency: fccDetails.currency,
      status: fccDetails.status,
      linkedDebitCards: esbDebitCards?.map(({ maskedCardNumber, status }) => ({
        maskedCardNumber,
        status,
      })),
    };
  }
}
