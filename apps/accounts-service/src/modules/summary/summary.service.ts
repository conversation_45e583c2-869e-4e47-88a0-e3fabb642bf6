import { Injectable } from '@nestjs/common';
import { CurrentUser, IndividualAccountBalance } from '@types';
import { AccountsSummaryService } from './services/accounts.service';
import { AccountType, CurrencyEnum, UserLanguage } from '@enums';
import { MutualFundsSummaryService } from './services/mutual-funds.service';
import { FxService } from '@modules';
import { LoansSummaryService } from './services/loans.service';
import { CreditCardsSummaryService } from './services/credit-cards.service';

@Injectable()
export class SummaryService {
  constructor(
    private readonly accountsService: AccountsSummaryService,
    private readonly mutualFundsService: MutualFundsSummaryService,
    private readonly loansService: LoansSummaryService,
    private readonly fxService: FxService,
    private readonly creditCardsService: CreditCardsSummaryService,
  ) {}

  async getSummary(currentUser: CurrentUser, userLanguage: UserLanguage) {
    const { totalAccountBalanceOnBaseCurrency, individualAccountBalances } =
      await this.accountsService.getSummary(currentUser, userLanguage);

    const { currencyCode } = totalAccountBalanceOnBaseCurrency;

    const loansTotal = await this.loansService.getTotal(
      currentUser,
      userLanguage,
    );

    const totalLoansBalanceOnBaseCurrency = {
      amount: loansTotal.total,
      count: loansTotal.count,
      currencyCode,
    };

    const depositsBalanceOnBaseCurrency = {
      amount: this.calculateTotalDeposits(individualAccountBalances).total,
      count: this.calculateTotalDeposits(individualAccountBalances).count,
      currencyCode,
    };

    const toEGP = await this.fxService.convert(
      CurrencyEnum.EGP,
      CurrencyEnum[currencyCode],
      currentUser,
    );

    //credit cards & mutual funds are always EGP
    const creditCardsTotalInEGP = await this.creditCardsService.getTotal(
      currentUser,
      userLanguage,
    );

    const mutualFundsTotalInEGP = await this.mutualFundsService.getTotal(
      currentUser,
      userLanguage,
    );

    const totalMutualFundsBalanceOnBaseCurrency = {
      amount: toEGP(mutualFundsTotalInEGP.total),
      count: mutualFundsTotalInEGP.count,
      currencyCode,
    };

    const totalCreditCardsBalanceOnBaseCurrency = {
      amount: toEGP(creditCardsTotalInEGP.total),
      count: creditCardsTotalInEGP.count,
      currencyCode,
    };

    totalAccountBalanceOnBaseCurrency.amount +=
      totalMutualFundsBalanceOnBaseCurrency.amount;

    return {
      totalAccountBalanceOnBaseCurrency,
      individualAccountBalances: this.filterUserAccounts(
        individualAccountBalances,
      ),
      totalLoansBalanceOnBaseCurrency,
      totalMutualFundsBalanceOnBaseCurrency,
      depositsBalanceOnBaseCurrency,
      totalCreditCardsBalanceOnBaseCurrency,
    };
  }

  filterUserAccounts(accounts: IndividualAccountBalance[]) {
    return accounts?.filter(
      (account) =>
        account.accountType === AccountType.CURRENT ||
        account.accountType === AccountType.SAVINGS ||
        account.accountType === AccountType.INQUIRY,
    );
  }

  calculateTotalDeposits(accounts: IndividualAccountBalance[]) {
    const termDeposits =
      accounts?.filter(
        (account) => account.accountType === AccountType.TERMDEPOSIT,
      ) || [];

    const total = termDeposits.reduce((sum, item) => {
      return sum + item.availableBalanceConverted;
    }, 0);

    const count = termDeposits.length;

    return { total, count };
  }
}
