import { AccountType, FccRequestsEnum, UserLanguage } from '@enums';
import { ApiAdapterClientService } from '@modules';
import { Injectable } from '@nestjs/common';
import { CurrentUser } from '@types';

@Injectable()
export class MutualFundsSummaryService {
  constructor(
    private readonly apiAdapterClientService: ApiAdapterClientService,
  ) {}

  async getTotal(currentUser: CurrentUser, userLanguage: UserLanguage) {
    const funds = await this.apiAdapterClientService.fccRequest<
      Record<string, number>[]
    >({
      requestId: FccRequestsEnum.MUTUAL_FUND_SUMMARY,
      user: currentUser,
      options: { language: userLanguage },
    });

    let total = 0;

    if (funds.length > 0) {
      total = funds.reduce((sum, item) => {
        return sum + item.total;
      }, 0);
    }

    return {
      total,
      count: funds.length,
    };
  }
}
