import { FccRequestsEnum, UserLanguage } from '@enums';
import { ApiAdapterClientService } from '@modules';
import { Injectable } from '@nestjs/common';
import { AccountBalanceSummary, CurrentUser } from '@types';

@Injectable()
export class AccountsSummaryService {
  constructor(
    private readonly apiAdapterClientService: ApiAdapterClientService,
  ) {}

  async getSummary(currentUser: CurrentUser, userLanguage: UserLanguage) {
    const response =
      await this.apiAdapterClientService.fccRequest<AccountBalanceSummary>({
        requestId: FccRequestsEnum.ACCOUNTS_BALANCE_SUMMARY,
        user: currentUser,
        options: { language: userLanguage },
      });
    return response;
  }
}
