import { Controller, Get, UseGuards } from '@nestjs/common';
import { getCurrentUser, JwtAuthGuard, getUserLanguage } from '@modules';
import { CurrentUser } from '@types';
import { SummaryService } from './summary.service';
import { MutualFundsSummaryService } from './services/mutual-funds.service';
import { UserLanguage } from '@enums';

@Controller('summary')
@UseGuards(JwtAuthGuard)
export class SummaryController {
  constructor(
    private readonly summaryService: SummaryService,
    private readonly mutualFunds: MutualFundsSummaryService,
  ) {}

  @Get('/all')
  async getAccountSummary(
    @getCurrentUser() currentUser: CurrentUser,
    @getUserLanguage() userLanguage: UserLanguage,
  ) {
    return this.summaryService.getSummary(currentUser, userLanguage);
  }
}
