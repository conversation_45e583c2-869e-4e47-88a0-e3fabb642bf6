import { Module } from '@nestjs/common';
import {
  ApiAdapterClient,
  CommonModule,
  FxModule,
  GlobalConfigModule,
  JwtAuthModule,
  LookupEntity,
  LookupsModule,
  RedisModule,
} from '@modules';
import defaults from './config/defaults';
import { SummaryModule } from './modules/summary/summary.module';
import { AccountsModule } from './modules/accounts/accounts.module';
import { AccountStatementModule } from './modules/estatement/estatement.module';
import { AccountMovementsModule } from './modules/movements/movements.module';
import { LoanListingModule } from '@/modules/loans/listing.module';

@Module({
  imports: [
    GlobalConfigModule.forRoot(defaults),
    RedisModule.forRootAsync(),
    LookupsModule.forRoot([
      {
        name: LookupEntity.I18N_SERVER,
      },
      {
        name: LookupEntity.ACCOUNT_PRODUCT,
      },
    ]),
    ApiAdapterClient,
    JwtAuthModule.forRoot(),
    FxModule,
    CommonModule,
    AccountsModule,
    SummaryModule,
    AccountStatementModule,
    AccountMovementsModule,
    LoanListingModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
