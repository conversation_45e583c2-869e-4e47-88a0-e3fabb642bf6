import { Injectable, Logger } from '@nestjs/common';
import { ApiAdapterClientService, DepositsLookupService } from '@modules';
import { CurrentUser } from '@types';
import {
  AccountsListContextEnum,
  CurrencyEnum,
  FccRequestsEnum,
  UserLanguage,
} from '@enums';

@Injectable()
export class ProductsService {
  private readonly logger = new Logger(ProductsService.name);

  constructor(
    private readonly depositsService: DepositsLookupService,
    private readonly apiAdapterService: ApiAdapterClientService,
  ) {}

  async getEligibleList(user: CurrentUser, userLanguage: UserLanguage) {
    const { items = [] }: { items: { currency: string }[] } =
      await this.apiAdapterService.fccRequest({
        requestId: FccRequestsEnum.ACCOUNTS_LIST,
        payload: {
          context: AccountsListContextEnum.LOCAL_TRANSFERS,
        },
        user,
        options: { language: userLanguage },
      });

    const currencies = items.map((account) => account.currency as CurrencyEnum);

    return this.depositsService.getGroupedEligibleProductList(
      user.segment,
      currencies,
      userLanguage,
    );
  }
}
