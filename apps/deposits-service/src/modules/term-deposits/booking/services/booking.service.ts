import { Injectable } from '@nestjs/common';
import {
  ApiAdapterClientService,
  DepositsLookupService,
  I18nLookupService,
  RawDepositProduct,
  RedisTnxService,
  TransactionDetailsFormatterService,
} from '@modules';
import { CurrentUser, DepositsMap } from '@types';
import {
  EsbLoanListItemDto,
  FccGenericTnxResponseDto,
  TermDepositSubmitRequestDto,
} from '@dtos';
import {
  ErrorCodeEnum,
  EsbProductTypeEnum,
  EsbRequestsEnum,
  FccRequestsEnum,
  ProductCodeEnum,
  TdRenewalInstructionsEnum,
  UserLanguage,
} from '@enums';
import { v4 as UUIDV4 } from 'uuid';
import {
  getTransactionStatusByCode,
  getTransactionSubStatusByCode,
  getTransfersReauthInputs,
} from '@helpers';
import { BusinessValidationException } from '@exceptions';
import { FccTdRenewalInstructionsMap } from '@constants';

@Injectable()
export class BookingService {
  constructor(
    private readonly clientService: ApiAdapterClientService,
    private readonly tnxService: RedisTnxService,
    private readonly i18n: I18nLookupService,
    private readonly depositsLookupService: DepositsLookupService,

    private readonly detailsFormatterService: TransactionDetailsFormatterService,
  ) {}

  async initiate(user: CurrentUser) {
    const idempotencyKey = UUIDV4();
    const ftxRecord =
      await this.clientService.fccRequest<FccGenericTnxResponseDto>({
        requestId: FccRequestsEnum.TERM_DEPOSIT_INITIATE,
        user,
        options: {
          idempotencyKey,
        },
      });

    await this.tnxService.setTnx(user, 'TD:CSTD', {
      ...ftxRecord,
      idempotencyKey,
    });

    return {
      refId: ftxRecord.refId,
      tnxId: ftxRecord.tnxId,
      reauth: getTransfersReauthInputs(ftxRecord),
      renewalInstructions: Object.values(TdRenewalInstructionsEnum).map(
        (key) => ({
          key,
          value: this.i18n.translate(`deposits.renewalInstructions.${key}`),
        }),
      ),
    };
  }

  async validate(
    body: TermDepositSubmitRequestDto,
    user: CurrentUser,
    userLanguage: UserLanguage,
  ) {
    await this.checkFCYLoan(user, userLanguage);
    await this.checkMinimumAmount(body, userLanguage);
    return await this.fetchInterestRateOrDefault(body, userLanguage);
  }

  private async fetchInterestRateOrDefault(
    body: TermDepositSubmitRequestDto,
    userLanguage: UserLanguage,
  ) {
    const interestRate = await this.clientService
      .esbRequest({
        requestId: EsbRequestsEnum.DEPOSIT_INTEREST_RATE,
        payload: {
          amount: 1000,
          productCode: body.productId,
        },
      })
      .catch(
        () =>
          this.depositsLookupService.getProduct(body.productId, userLanguage)
            .interestRate,
      );
    return {
      interestRate,
    };
  }

  private async checkFCYLoan(user: CurrentUser, userLanguage: UserLanguage) {
    const userLoanList = await this.clientService
      .esbRequest<EsbLoanListItemDto[]>({
        requestId: EsbRequestsEnum.LOANS_LIST,
        payload: {
          userId: user.cif,
          productType: EsbProductTypeEnum.LOAN,
        },
        user,
      })
      .catch(() => []);
    const hasFCYLoan =
      userLoanList.filter((loan) => loan.amount.curCode != 'EGP').length > 0;
    if (hasFCYLoan) {
      throw new BusinessValidationException(
        ErrorCodeEnum.NotEligible,
        userLanguage,
      );
    }
  }

  private async checkMinimumAmount(
    body: TermDepositSubmitRequestDto,
    userLanguage: UserLanguage,
  ) {
    const depositProduct: RawDepositProduct =
      this.depositsLookupService.getProduct(body.productId, userLanguage);
    if (Number(body.tdAmount) < depositProduct?.minimumBookingAmount) {
      throw new BusinessValidationException(
        ErrorCodeEnum.LessThanMinimumBookingAmount,
        userLanguage,
      );
    }
  }

  async getFccProductDetails(
    fccCode: string,
    user: CurrentUser,
    userLanguage: UserLanguage,
  ) {
    const map = await this.clientService.fccRequest<DepositsMap>({
      requestId: FccRequestsEnum.DEPOSITS_MAP,
      user,
    });

    if (map && map[fccCode]) {
      return map[fccCode];
    }

    throw new BusinessValidationException(ErrorCodeEnum.NotFound, userLanguage);
  }

  async submit(
    body: TermDepositSubmitRequestDto,
    user: CurrentUser,
    userLanguage: UserLanguage,
  ) {
    await this.checkFCYLoan(user, userLanguage);
    await this.checkMinimumAmount(body, userLanguage);

    const { tnxId, refId, idempotencyKey } =
      await this.tnxService.getTnx<FccGenericTnxResponseDto>(user, 'TD:CSTD');

    const { productId, renewalInstruction, ...bodyRest } = body;

    const product = this.depositsLookupService.getProduct(productId);

    const maturityInstructionName =
      FccTdRenewalInstructionsMap?.[body.renewalInstruction];

    const payload = {
      tnxId,
      refId,

      fccCode: product.fccCode,
      tenorValue: 1,
      tenorUnit: 'W',

      ...bodyRest,
    } as any;

    const { instructions = {} } = await this.getFccProductDetails(
      product.fccCode,
      user,
      userLanguage,
    );

    if (instructions[maturityInstructionName]) {
      payload.maturityInstructionName = maturityInstructionName;
      payload.maturityInstruction = instructions[maturityInstructionName];
    }

    const transaction =
      await this.clientService.fccRequest<FccGenericTnxResponseDto>({
        requestId: FccRequestsEnum.TERM_DEPOSIT_SUBMIT,
        payload,
        user,
        options: {
          idempotencyKey,
        },
      });

    const status = getTransactionStatusByCode(transaction.tnxStatCode);
    const subStatus = getTransactionSubStatusByCode(transaction.subTnxStatCode);

    return {
      refId: transaction.refId,
      status,
      subStatus,
      message: this.i18n.translate(
        `deposits.submit.${subStatus}`,
        userLanguage,
      ),
      details: this.detailsFormatterService.format(
        transaction,
        ProductCodeEnum.TermDeposit,
        userLanguage,
      ),
    };
  }
}
