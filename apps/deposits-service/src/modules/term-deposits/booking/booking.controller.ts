import { Body, Controller, Get, Post, UseGuards } from '@nestjs/common';
import { getCurrentUser, getUserLanguage, JwtAuthGuard } from '@modules';
import { CurrentUser } from '@types';
import { UserLanguage } from '@enums';
import { ProductsService } from './services/products.service';
import { BookingService } from './services/booking.service';
import { TermDepositSubmitRequestDto } from '@dtos';

@Controller('td/booking')
@UseGuards(JwtAuthGuard)
export class TermDepositsBookingController {
  constructor(
    readonly bookService: BookingService,
    readonly productsService: ProductsService,
  ) {}

  @Get('/products')
  async productsList(
    @getCurrentUser() currentUser: CurrentUser,
    @getUserLanguage() userLanguage: UserLanguage,
  ) {
    return this.productsService.getEligibleList(currentUser, userLanguage);
  }

  @Post('/initiate')
  async initiate(@getCurrentUser() currentUser: CurrentUser) {
    return this.bookService.initiate(currentUser);
  }

  @Post('/validate')
  async validate(
    @Body() body: TermDepositSubmitRequestDto,
    @getCurrentUser() currentUser: CurrentUser,
    @getUserLanguage() userLanguage: UserLanguage,
  ) {
    return this.bookService.validate(body, currentUser, userLanguage);
  }

  @Post('/submit')
  async submit(
    @Body() body: TermDepositSubmitRequestDto,
    @getCurrentUser() currentUser: CurrentUser,
    @getUserLanguage() userLanguage: UserLanguage,
  ) {
    return this.bookService.submit(body, currentUser, userLanguage);
  }
}
