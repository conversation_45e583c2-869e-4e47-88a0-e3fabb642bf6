import { Module } from '@nestjs/common';
import { OwnListService } from '@/modules/term-deposits/listing/services/own-list.service';
import { ListingController } from '@/modules/term-deposits/listing/listing.controller';
import { DetailsService } from './services/detail.service';

@Module({
  imports: [],
  controllers: [ListingController],
  providers: [OwnListService, DetailsService],
})
export class TermDepositsListingModule {}
