import { Injectable } from '@nestjs/common';
import {
  ApiAdapterClientService,
  I18nLookupService,
  TermDepositDetailsService,
} from '@modules';
import { CurrentUser, DepositId } from '@types';
import { UserLanguage } from '@enums';
import { decryptUserJson } from '@helpers';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class DetailsService {
  constructor(
    private readonly apiClientService: ApiAdapterClientService,
    private readonly i18n: I18nLookupService,
    private readonly configService: ConfigService,
    private readonly detailsService: TermDepositDetailsService,
  ) {}

  async getDetails(
    encryptedId: string,
    currentUser: CurrentUser,
    userLanguage: UserLanguage,
  ) {
    const { accountId } = decryptUserJson<DepositId>(
      encryptedId,
      this.configService.get('aesSecret'),
      currentUser.userId,
    );

    return this.detailsService.getDetails(accountId, currentUser, {
      userLanguage,
    });
  }
}
