import { Injectable } from '@nestjs/common';
import { CurrentUser } from '@types';
import { UserLanguage } from '@enums';
import { TermDepositListService, UsersService } from '@modules';
import { DepositListQueryDto } from '@dtos';

@Injectable()
export class OwnListService {
  constructor(
    private tdListService: TermDepositListService,
    private readonly usersService: UsersService,
  ) {}

  async getList(
    query: DepositListQueryDto,
    currentUser: CurrentUser,
    userLanguage: UserLanguage = UserLanguage['en-US'],
  ) {
    const defaultCurrency =
      await this.usersService.getDefaultCurrency(currentUser);

    return this.tdListService.getOwnList(
      currentUser,
      {
        userLanguage,
        defaultCurrency,
      },
      query,
    );
  }
}
