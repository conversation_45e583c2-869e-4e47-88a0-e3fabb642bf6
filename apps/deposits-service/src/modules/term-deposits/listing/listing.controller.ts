import { Controller, Get, Param, Query, UseGuards } from '@nestjs/common';
import { getCurrentUser, getUserLanguage, JwtAuthGuard } from '@modules';
import { CurrentUser } from '@types';
import { OwnListService } from '@/modules/term-deposits/listing/services/own-list.service';
import { DetailsService } from '@/modules/term-deposits/listing/services/detail.service';
import { UserLanguage } from '@enums';
import { DepositListQueryDto } from '@dtos';

@Controller('td/listing')
@UseGuards(JwtAuthGuard)
export class ListingController {
  constructor(
    private readonly ownListService: OwnListService,
    private readonly detailService: DetailsService,
  ) {}

  @Get()
  async getAccountByTypeList(
    @Query() query: DepositListQueryDto,
    @getCurrentUser() currentUser: CurrentUser,
    @getUserLanguage() userLanguage: UserLanguage,
  ) {
    return this.ownListService.getList(query, currentUser, userLanguage);
  }

  @Get(':id')
  async getDepositDetails(
    @Param('id') id: string,
    @getCurrentUser() currentUser: CurrentUser,
    @getUserLanguage() userLanguage: UserLanguage,
  ) {
    return this.detailService.getDetails(id, currentUser, userLanguage);
  }
}
