import { Module } from '@nestjs/common';
import {
  ApiAdapterClient,
  CommonModule,
  GlobalConfigModule,
  JwtAuthModule,
  LookupEntity,
  LookupsModule,
  RedisModule,
} from '@modules';
import defaults from './config/defaults';
import { TermDepositsModule } from './modules/term-deposits/term-deposits.module';

@Module({
  imports: [
    GlobalConfigModule.forRoot(defaults),
    RedisModule.forRootAsync(),
    JwtAuthModule.forRoot(),
    ApiAdapterClient,
    LookupsModule.forRoot([
      {
        name: LookupEntity.I18N_SERVER,
      },
      {
        name: LookupEntity.DEPOSIT_PRODUCT,
      },
    ]),
    CommonModule,
    TermDepositsModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
