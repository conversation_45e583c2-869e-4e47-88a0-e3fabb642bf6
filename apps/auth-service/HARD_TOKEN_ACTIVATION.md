# Hard Token Activation Endpoint

## Overview
A new endpoint has been created for hard token OTP activation that works similarly to the existing soft token (step1) OTP activation but handles hard token-specific data.

## Endpoint Details

### POST `/hard-token-activation/step1`

**Purpose:** Submit hard token OTP data for activation step 1

**Headers:**
- `Authorization: Bearer <otpActivationToken>`
- `Content-Type: application/json`
- `Accept-Language: en` (optional)

**Request Payload:**
```json
{
    "nationalId": "27601234455096",
    "phoneNumber": "+201095431289",
    "otpCode": "518630",
    "serialNumber": "2988724091"
}
```

**Successful Response (Step 2):**
```json
{
    "responseCode": "SUCCESS",
    "title": "OTP Activation Required",
    "message": "Please complete the OTP activation process",
    "tokenType": "HARD_TOKEN",
    "userData": {
        "response": "success",
        "mode": "tokenregistersteptwo",
        "objectData": {
            "tokentype": "HARD_TOKEN",
            // ... additional response data from ISAM
        }
    },
    "otpActivationStep": "tokenregistersteptwo"
}
```

## Implementation Details

### New DTO Classes
- `HardTokenActivationStep1Dto`: Simple request DTO with 4 parameters

### Service Method
- `OtpActivationService.completeHardTokenStep1()`: Handles the hard token activation logic
- Similar flow to existing `completeStep1()` but specifically for hard tokens
- Maps the 4 input parameters to the full ISAM payload structure internally
- Expects response to move to `tokenregistersteptwo` on success

### Key Features
- Simple 4-parameter interface similar to other endpoints
- Full payload validation using class-validator decorators
- Proper error handling and response formatting
- Redis caching for token data and attributes
- Logging for debugging and monitoring
- Uses the same ISAM endpoint (`/restportal/login/`) as soft token activation

### Validation Rules
- `nationalId`: Required, max 20 chars, numbers only
- `phoneNumber`: Required, valid phone number format
- `otpCode`: Required, exactly 6 digits
- `serialNumber`: Required string

## Usage Flow
1. User initiates login and receives `otpActivationToken` when hard token activation is required
2. Client calls `/hard-token-activation/step1` with the hard token data and OTP
3. Service validates the request and forwards to ISAM
4. On success, ISAM responds with `tokenregistersteptwo` indicating next step
5. Client can then proceed with existing step 2 flow using the same `otpActivationToken`

## Error Handling
- Invalid token: Returns `TOKEN_ERROR` response
- Validation errors: Returns field-specific validation messages
- ISAM errors: Returns `SYSTEM_ERROR` with OTP activation failed message
- Network errors: Proper timeout and error handling

This implementation maintains consistency with the existing OTP activation flow while supporting the specific requirements for hard token activation.
