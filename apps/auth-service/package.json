{"name": "auth-service", "version": "0.0.1", "description": "NestJS authentication service with ISAM integration", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "dev": "nest start --watch", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest --config ./jest-unit.json --detectOpenHandles --passWithNoTests --forceExit", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"axios-cookiejar-support": "^6.0.2", "cheerio": "^1.0.0", "jsdom": "^26.1.0", "rxjs": "^7.8.1", "tough-cookie": "^5.1.2", "svg-captcha": "^1.4.0"}, "devDependencies": {"@types/jest": "^29.5.14", "jest": "^29.7.0", "ts-jest": "^29.3.4"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "workspaces": ["apps/*", "packages/*"]}