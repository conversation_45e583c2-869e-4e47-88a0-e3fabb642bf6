{"extends": "./tsconfig.json", "compilerOptions": {"module": "commonjs", "target": "ES2021", "types": ["jest", "node"], "esModuleInterop": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "skipLibCheck": true, "skipDefaultLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "noImplicitReturns": false, "noImplicitThis": false, "strict": false}, "include": ["test/**/*", "src/**/*"], "exclude": ["node_modules", "dist"]}