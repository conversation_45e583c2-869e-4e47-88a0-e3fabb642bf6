{"verbose": true, "moduleFileExtensions": ["js", "json", "ts"], "roots": ["<rootDir>/test"], "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "globals": {"ts-jest": {"tsconfig": "tsconfig.test.json", "isolatedModules": true, "useESM": false, "diagnostics": false}}, "collectCoverageFrom": ["src/**/*.(t|j)s"], "resetMocks": true, "clearMocks": true, "testTimeout": 15000, "maxWorkers": 1, "coveragePathIgnorePatterns": ["./dist", "./coverage", "./coverage-e2e", "./node_modules", "./turbo", "./test", ".eslintrc.js", "./*.module.ts", "./index.ts", "main.ts"], "modulePathIgnorePatterns": ["./dist", "./coverage", "./coverage-e2e", "./node_modules", "./turbo"], "coverageDirectory": "./coverage", "testEnvironment": "node", "collectCoverage": true, "coverageThreshold": {"global": {"lines": 5, "functions": 5, "branches": 5, "statements": 5}}, "moduleNameMapper": {"^@/(.*)$": ["<rootDir>/src/$1"], "@dtos": ["<rootDir>/../../packages/dtos/src"], "@types": ["<rootDir>/../../packages/types/src"], "@enums": ["<rootDir>/../../packages/enums/src"], "@modules": ["<rootDir>/../../packages/modules/src"], "@constants": ["<rootDir>/../../packages/constants/src"], "@middlewares": ["<rootDir>/../../packages/middlewares/src"], "@helpers": ["<rootDir>/../../packages/helpers/src"], "@exceptions": ["<rootDir>/../../packages/exceptions/src"], "@filters": ["<rootDir>/../../packages/filters/src"]}}