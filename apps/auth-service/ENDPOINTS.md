# Auth Service API Endpoints

## POST /v1/auth/login

Authenticates a user and handles various login scenarios.

### Request Body

```json
{
  "company": "CAIRO3AFORPOULTRYCOMPANY",
  "username": "<PERSON>BRAH<PERSON>",
  "password": "yourPassword123"
}
```

### Request Headers

- `authorization`: Bearer token (required for termination flow)
- `accept-language`: Language preference (optional)

### Response Scenarios

#### Active Session Detected

```json
{
  "responseCode": "ACTIVE_SESSION",
  "message": "Active session detected. Termination required before login.",
  "terminationToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

#### Authentication Error

```json
{
  "statusCode": 401,
  "message": "Failed to perform authentication request"
}
```

#### Successful Login

```json
{
  "responseCode": "SUCCESS",
  "message": "Authentication successful",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "userData": {
    "mode": "",
    "company": "company",
    "user": "company / rgrbtv asdqwe",
    "classicUXHome": true,
    "redirectionURL": "/portal/screen/GTPLoginScreen",
    "objectData": {
      "tokentype": "NO_REAUTH"
    },
    "response": "success"
  }
}
```

#### First-time Login (Expired Credentials)

```json
{
  "responseCode": "EXPIRED_PASSWORD",
  "message": "auth.login.expired_password",
  "resetPasswordToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

#### Invalid Credentials

```json
{
  "responseCode": "INVALID_CREDENTIALS",
  "message": "auth.login.invalid_credentials"
}
```

#### Terms Acceptance Required

```json
{
  "responseCode": "TERMS_REQUIRED",
  "message": "auth.login.terms_required",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "terms": {
    "1": "The Customer undertakes to procure...",
    "2": "Each Demo Group Bank shall be entitled...",
    "3": "The Customer agrees to comply...",
    "4": "The Customer and/or the Customer Users must notify...",
    "5": "Inform the relevant Demo Group Bank...",
    "6": "The Customer shall ensure that...",
    "7": "The execution or implementation of any Instruction...",
    "8": "The Customer shall not commence or continue..."
  },
  "tokenType": "NO_REAUTH"
}
```

## POST /v1/auth/reset-password

Resets a user's credentials.

### Request Body

```json
{
  "oldPassword": "currentPassword123",
  "newPassword": "newPassword123",
  "confirmPassword": "newPassword123"
}
```

### Request Headers

- `authorization`: Reset password token (required)

### Response Scenarios

#### Success

```json
{
  "responseCode": "SUCCESS",
  "message": "auth.password.reset_success"
}
```

#### Credentials Mismatch

```json
{
  "responseCode": "SYSTEM_ERROR",
  "message": "auth.password.mismatch"
}
```

#### Missing Request Attribute

```json
{
  "message": [
    "confirmPassword should not be empty"
  ],
  "error": "Bad Request",
  "statusCode": 400
}
```

#### Missing Reset Token

```json
{
  "responseCode": "TOKEN_ERROR",
  "message": "auth.token.missing"
}
```

## POST /v1/auth/logout

Logs out the current user.

### Request Headers

- `authorization`: Bearer token (required)

### Response

```json
{
  "responseCode": "SUCCESS",
  "message": "auth.login.logout_success"
}
```

## GET /v1/auth/refresh-token

Refreshes the authentication token.

### Request Headers

- `authorization`: Bearer token (required)

### Response

```json
{
  "responseCode": "SUCCESS",
  "message": "auth.token.refresh_success",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "nearExpiry": false
}
```


