/**
 * Simple validation script to check if test structure and imports are correct
 * This can be run with Node v12 to validate the basic structure
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Validating test structure...\n');

// Check test directory structure
const testStructure = [
  'test/unit/auth/services/auth.service.spec.ts',
  'test/helpers/test-fixtures.ts',
  'test/README.md',
];

let allValid = true;

console.log('📁 Checking test file structure:');
testStructure.forEach(filePath => {
  const fullPath = path.join(__dirname, '..', filePath);
  if (fs.existsSync(fullPath)) {
    console.log(`✅ ${filePath}`);
  } else {
    console.log(`❌ ${filePath} - NOT FOUND`);
    allValid = false;
  }
});

console.log('\n📋 Checking test content:');

// Check if main test file has proper structure
const testFile = path.join(__dirname, 'unit/auth/services/auth.service.spec.ts');
if (fs.existsSync(testFile)) {
  const content = fs.readFileSync(testFile, 'utf8');
  
  const checks = [
    { name: 'AuthService import', pattern: /import.*AuthService.*from/ },
    { name: 'Test module setup', pattern: /Test.createTestingModule/ },
    { name: 'Login tests', pattern: /describe\(['"]login['"]/ },
    { name: 'Logout tests', pattern: /describe\(['"]logout['"]/ },
    { name: 'RefreshToken tests', pattern: /describe\(['"]refreshToken['"]/ },
    { name: 'Helper methods tests', pattern: /describe\(['"]private helper methods['"]/ },
    { name: 'Complex flows tests', pattern: /describe\(['"]complex authentication flows['"]/ },
  ];
  
  checks.forEach(check => {
    if (check.pattern.test(content)) {
      console.log(`✅ ${check.name} found`);
    } else {
      console.log(`❌ ${check.name} not found`);
      allValid = false;
    }
  });
} else {
  console.log('❌ Main test file not found');
  allValid = false;
}

// Check fixtures file
const fixturesFile = path.join(__dirname, 'helpers/test-fixtures.ts');
if (fs.existsSync(fixturesFile)) {
  const content = fs.readFileSync(fixturesFile, 'utf8');
  
  const fixtureChecks = [
    { name: 'mockLoginDto export', pattern: /export const mockLoginDto/ },
    { name: 'mockCurrentUser export', pattern: /export const mockCurrentUser/ },
    { name: 'mockAuthSuccessResponse export', pattern: /export const mockAuthSuccessResponse/ },
    { name: 'createMockConfigService helper', pattern: /export const createMockConfigService/ },
  ];
  
  fixtureChecks.forEach(check => {
    if (check.pattern.test(content)) {
      console.log(`✅ ${check.name} found in fixtures`);
    } else {
      console.log(`❌ ${check.name} not found in fixtures`);
      allValid = false;
    }
  });
} else {
  console.log('❌ Test fixtures file not found');
  allValid = false;
}

console.log('\n🧪 Test Configuration:');

// Check Jest config
const jestConfig = path.join(__dirname, '..', 'jest-unit.json');
if (fs.existsSync(jestConfig)) {
  const config = JSON.parse(fs.readFileSync(jestConfig, 'utf8'));
  
  if (config.roots && config.roots.includes('<rootDir>/test')) {
    console.log('✅ Jest configured to use test directory');
  } else {
    console.log('❌ Jest not configured for test directory');
    allValid = false;
  }
  
  if (config.testRegex && config.testRegex.includes('spec')) {
    console.log('✅ Jest configured to find .spec.ts files');
  } else {
    console.log('❌ Jest not configured to find spec files');
    allValid = false;
  }
  
  if (config.globals && config.globals['ts-jest'] && config.globals['ts-jest']['tsconfig']) {
    console.log('✅ Jest configured with TypeScript config');
  } else {
    console.log('❌ Jest not configured with TypeScript config');
    allValid = false;
  }
} else {
  console.log('❌ Jest config not found');
  allValid = false;
}

// Check TypeScript configs
const tsConfig = path.join(__dirname, '..', 'tsconfig.json');
if (fs.existsSync(tsConfig)) {
  const config = JSON.parse(fs.readFileSync(tsConfig, 'utf8'));
  
  if (config.include && config.include.includes('test/**/*')) {
    console.log('✅ TypeScript configured to include test files');
  } else {
    console.log('❌ TypeScript not configured to include test files');
    allValid = false;
  }
} else {
  console.log('❌ TypeScript config not found');
  allValid = false;
}

const tsTestConfig = path.join(__dirname, '..', 'tsconfig.test.json');
if (fs.existsSync(tsTestConfig)) {
  console.log('✅ Dedicated TypeScript test config found');
} else {
  console.log('❌ Dedicated TypeScript test config not found');
  allValid = false;
}

console.log('\n' + '='.repeat(50));
if (allValid) {
  console.log('🎉 All validation checks passed!');
  console.log('📝 Test structure is properly organized and ready to run.');
  console.log('⚠️  Note: Tests require Node.js >= 20 to run due to Jest version compatibility.');
} else {
  console.log('⚠️  Some validation checks failed.');
  console.log('📝 Please review the issues above before running tests.');
}

console.log('\n📖 To run tests when Node.js >= 20 is available:');
console.log('   npm test                              # Run all tests');
console.log('   npm test -- --testPathPattern=auth   # Run auth service tests');
console.log('   npm run test:cov                     # Run with coverage');
console.log('   npm run test:watch                   # Run in watch mode');
