import { LoginDto } from '../../src/auth/dto/login.dto';
import { ResetPasswordDto } from '../../src/auth/dto/reset-password.dto';
import { AuthResponseDto } from '../../src/auth/dto/auth-response.dto';
import { AuthResponseCode } from '../../src/auth/types/auth-response.types';
import { UserLanguage } from '@enums';
import { CurrentUser } from '@types';

/**
 * Common test fixtures and mock data for auth service tests
 */

export const mockLoginDto: LoginDto = {
  username: 'testuser',
  password: 'testpassword',
  company: 'testcompany',
};

export const mockLoginDtoWithCaptcha: LoginDto = {
  username: 'testuser',
  password: 'testpassword',
  company: 'testcompany',
  captchaToken: 'captcha-token-123',
  captchaText: 'ABCD123',
};

export const mockResetPasswordDto: ResetPasswordDto = {
  oldPassword: 'oldPassword123!',
  newPassword: 'newPassword123!',
  confirmPassword: 'newPassword123!',
};

export const mockCurrentUser: CurrentUser = {
  username: 'testuser',
  company: 'testcompany',
  cookies: 'session=abc123; token=xyz789; JSESSIONID=**********',
  userId: 'user123',
  cif: 'CIF123456',
  industry: '104',
  segment: 'business_banking',
  fullName: 'Test User',
};

export const mockUserDetails = {
  userDetails: {
    userId: 'user123',
    customerReferences: [
      { ID: 'CIF123456', name: 'cif123' },
      { ID: 'CIF789012', name: 'cif789' },
    ],
  },
  t24CompanyProfile: {
    industry: '104',
    companyName: 'Test Company Ltd',
  },
  segment: {
    id: 'business_banking',
    name: 'Business Banking',
  },
};

export const mockAuthSuccessResponse: AuthResponseDto = {
  responseCode: AuthResponseCode.SUCCESS,
  message: 'Authentication successful',
  title: 'Login Successful',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  userData: {
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    company: 'testcompany',
  },
  userDetails: mockUserDetails.userDetails,
  segment: mockUserDetails.segment,
};

export const mockAuthErrorResponse: AuthResponseDto = {
  responseCode: AuthResponseCode.INVALID_CREDENTIALS,
  message: 'The provided credentials are incorrect',
  title: 'Invalid Credentials',
};

export const mockCaptchaRequiredResponse: AuthResponseDto = {
  responseCode: AuthResponseCode.CAPTCHA_REQUIRED,
  message: 'Please complete the captcha challenge',
  title: 'Captcha Required',
  captchaRequired: true,
  userData: {
    captchaImage: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTUwIi...',
    captchaToken: 'captcha-token-123',
  },
};

export const mockTermsRequiredResponse: AuthResponseDto = {
  responseCode: AuthResponseCode.TERMS_REQUIRED,
  message: 'Please accept the terms and conditions',
  title: 'Terms Required',
  termsRequired: true,
  terms: 'By using this service, you agree to our terms and conditions...',
  termsAndConditionToken: 'terms-token-123',
  tokenType: 'NO_REAUTH',
};

export const mockOtpActivationResponse: AuthResponseDto = {
  responseCode: AuthResponseCode.OTP_ACTIVATION_REQUIRED,
  message: 'OTP activation is required',
  title: 'OTP Activation Required',
  otpActivationRequired: true,
  otpActivationToken: 'otp-token-123',
  otpActivationStep: 'step1',
  tokenType: 'SMS',
};

export const mockActiveSessionResponse: AuthResponseDto = {
  responseCode: AuthResponseCode.ACTIVE_SESSION,
  message: 'An active session was detected. Please terminate it to continue.',
  title: 'Active Session Detected',
  terminationToken: 'termination-token-123',
};

export const mockExpiredPasswordResponse: AuthResponseDto = {
  responseCode: AuthResponseCode.EXPIRED_PASSWORD,
  message: 'Your password has expired. Please reset it.',
  title: 'Password Expired',
  resetPasswordToken: 'reset-token-123',
};

export const mockLockedAccountResponse: AuthResponseDto = {
  responseCode: AuthResponseCode.LOCKED_ACCOUNT,
  message: 'Your account has been locked due to multiple failed attempts',
  title: 'Account Locked',
};

export const mockSystemErrorResponse: AuthResponseDto = {
  responseCode: AuthResponseCode.SYSTEM_ERROR,
  message: 'A system error occurred. Please try again later.',
  title: 'System Error',
};

export const mockIsamResponse = {
  statusCode: 200,
  status: 200,
  body: '<html><body>Authentication successful</body></html>',
  headers: {
    'set-cookie': [
      'JSESSIONID=**********; Path=/; HttpOnly',
      'session=abc123; Path=/; Secure',
      'token=xyz789; Path=/; HttpOnly; Secure',
    ],
  },
};

export const mockIsamErrorResponse = {
  statusCode: 200,
  status: 200,
  body: '<html><body>Your user or password is incorrect</body></html>',
  headers: {
    'set-cookie': ['JSESSIONID=error123; Path=/; HttpOnly'],
  },
};

export const mockIsamLockedAccountResponse = {
  statusCode: 200,
  status: 200,
  body: '<html><body>Your account has been locked</body></html>',
  headers: {
    'set-cookie': ['JSESSIONID=locked123; Path=/; HttpOnly'],
  },
};

export const mockIsamExpiredPasswordResponse = {
  statusCode: 200,
  status: 200,
  body: '<html><body>Your password is expired</body></html>',
  headers: {
    'set-cookie': ['JSESSIONID=expired123; Path=/; HttpOnly'],
  },
};

export const mockAxiosResponse = {
  data: { success: true },
  status: 200,
  statusText: 'OK',
  headers: {},
  config: {} as any,
};

export const mockStateIdExtractionResult = {
  stateId: 'state-id-123',
  response: mockIsamResponse,
};

export const mockRestPortalResponse = {
  body: JSON.stringify({
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    company: 'testcompany',
    mode: 'normal',
  }),
  headers: {
    'set-cookie': ['portal-session=portal123; Path=/; HttpOnly'],
  },
};

export const mockCaptchaGeneration = {
  image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTUwIi...',
  token: 'captcha-token-123',
};

// Configuration mock values
export const mockConfigValues = {
  AUTH_MAX_ATTEMPTS: 5,
  AUTH_CAPTCHA_THRESHOLD: 3,
  AUTH_ATTEMPT_TTL: 86400,
  REDIS_TOKEN_TTL: 600,
  FCC_URL: 'https://dev.example.com',
  ISAM_BASE_URL: 'https://isam.example.com/FBCCBB',
};

// I18n translation mock values
export const mockTranslations = {
  'auth.login.success.title': 'Login Successful',
  'auth.login.success.body': 'Welcome back!',
  'auth.login.logout_success.title': 'Logout Successful',
  'auth.login.logout_success.body': 'You have been logged out successfully',
  'auth.login.invalid_credentials.title': 'Invalid Credentials',
  'auth.login.invalid_credentials.body': 'The provided credentials are incorrect',
  'auth.login.invalid_credentials_captcha.title': 'Invalid Credentials',
  'auth.login.invalid_credentials_captcha.body': 'The provided credentials are incorrect. Please complete the captcha.',
  'auth.login.locked_account.title': 'Account Locked',
  'auth.login.locked_account.body': 'Your account has been locked',
  'auth.login.expired_password.title': 'Password Expired',
  'auth.login.expired_password.body': 'Your password has expired',
  'auth.login.active_session.title': 'Active Session',
  'auth.login.active_session.body': 'An active session was detected',
  'auth.login.terms_required.title': 'Terms Required',
  'auth.login.terms_required.body': 'Please accept the terms and conditions',
  'auth.login.otp_activation_required.title': 'OTP Activation Required',
  'auth.login.otp_activation_required.body': 'Please activate your OTP device',
  'auth.login.system_error.title': 'System Error',
  'auth.login.system_error.body': 'A system error occurred',
};

// Helper function to create mock functions with default implementations
export const createMockConfigService = () => ({
  get: jest.fn().mockImplementation((key: string, defaultValue?: any) => {
    return mockConfigValues[key] ?? defaultValue;
  }),
  getOrThrow: jest.fn().mockImplementation((key: string) => {
    const value = mockConfigValues[key];
    if (value === undefined) {
      throw new Error(`Configuration key '${key}' not found`);
    }
    return value;
  }),
});

export const createMockI18nService = () => ({
  translate: jest.fn().mockImplementation((key: string, lang: UserLanguage) => {
    return mockTranslations[key] || key;
  }),
});
