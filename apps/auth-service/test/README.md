# Auth Service Tests

This directory contains all tests for the auth service, organized by type and functionality.

## Structure

```
test/
├── unit/                    # Unit tests
│   ├── auth/               # Auth module tests  
│   │   ├── services/       # Service tests
│   │   │   ├── auth.service.spec.ts
│   │   │   ├── jwt-token.service.spec.ts
│   │   │   ├── isam.service.spec.ts
│   │   │   ├── captcha.service.spec.ts
│   │   │   ├── user-details.service.spec.ts
│   │   │   ├── termination.service.spec.ts
│   │   │   ├── reset-password.service.spec.ts
│   │   │   ├── terms-acceptance.service.spec.ts
│   │   │   └── otp-activation.service.spec.ts
│   │   ├── controllers/     # Controller tests
│   │   │   └── auth.controller.spec.ts
│   │   ├── guards/         # Guard tests
│   │   └── utils/          # Utility tests
│   └── helpers/            # Test helper functions and mocks
├── integration/            # Integration tests
├── e2e/                   # End-to-end tests
└── fixtures/              # Test data and fixtures
```

## Running Tests

### Unit Tests
```bash
npm test
```

### Specific Test File
```bash
npm test -- --testPathPattern=auth.service.spec.ts
```

### With Coverage
```bash
npm run test:cov
```

### Watch Mode
```bash
npm run test:watch
```

## Test Conventions

1. **File Naming**: Use `.spec.ts` suffix for all test files
2. **Structure**: Mirror the source code structure in the test directory
3. **Mocking**: Create comprehensive mocks for all dependencies
4. **Test Organization**: Group tests by method/functionality using `describe` blocks
5. **Test Names**: Use descriptive names that explain what the test validates

## Adding New Tests

When adding tests for new services or methods:

1. Create the test file in the appropriate directory following the structure
2. Include comprehensive test coverage for all scenarios (success, failure, edge cases)
3. Mock all external dependencies
4. Follow the existing patterns for setup and teardown
5. Update this README if adding new test categories
