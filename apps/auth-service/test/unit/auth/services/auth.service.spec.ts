import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { UnauthorizedException, Logger } from '@nestjs/common';
import { of, throwError } from 'rxjs';

import { AuthService } from '@/auth/services/auth.service';
import { JwtTokenService } from '@/auth/services/jwt-token.service';
import { IsamService } from '@/auth/services/isam.service';
import { StateIdParserService } from '@/auth/services/stateIdParserService';
import { CaptchaService } from '@/auth/services/captcha.service';
import { UserDetailsService } from '@/auth/services/user-details.service';
import { TerminationService } from '@/auth/services/termination.service';
import { ResetPasswordService } from '@/auth/services/reset-password.service';
import { TermsAcceptanceService } from '@/auth/services/terms-acceptance.service';
import { OtpActivationService } from '@/auth/services/otp-activation.service';
import { I18nLookupService, RedisCacheService } from '@modules';
import { LoginDto } from '@/auth/dto/login.dto';
import { ResetPasswordDto } from '@/auth/dto/reset-password.dto';
import { AuthResponseDto } from '@/auth/dto/auth-response.dto';
import { AuthResponseCode } from '@/auth/types/auth-response.types';
import { UserLanguage } from '@enums';
import { CurrentUser } from '@types';
import { AuthenticationException } from '@/auth/exceptions/authentication.exception';

describe('AuthService', () => {
  let service: AuthService;
  let configService: jest.Mocked<ConfigService>;
  let httpService: jest.Mocked<HttpService>;
  let jwtTokenService: jest.Mocked<JwtTokenService>;
  let isamService: jest.Mocked<IsamService>;
  let stateIdParserService: jest.Mocked<StateIdParserService>;
  let i18nService: jest.Mocked<I18nLookupService>;
  let redisCacheService: jest.Mocked<RedisCacheService>;
  let captchaService: jest.Mocked<CaptchaService>;
  let userDetailsService: jest.Mocked<UserDetailsService>;
  let terminationService: jest.Mocked<TerminationService>;
  let resetPasswordService: jest.Mocked<ResetPasswordService>;
  let termsAcceptanceService: jest.Mocked<TermsAcceptanceService>;
  let otpActivationService: jest.Mocked<OtpActivationService>;

  const mockLoginDto: LoginDto = {
    username: 'testuser',
    password: 'testpassword',
    company: 'testcompany',
  };

  const mockCurrentUser: CurrentUser = {
    username: 'testuser',
    company: 'testcompany',
    cookies: 'session=abc123; token=xyz789',
    userId: 'user123',
  };

  const mockResetPasswordDto: ResetPasswordDto = {
    oldPassword: 'oldPassword123',
    newPassword: 'newPassword123',
    confirmPassword: 'newPassword123',
  };

  beforeEach(async () => {
    const mockConfigService = {
      get: jest.fn(),
      getOrThrow: jest.fn(),
    };

    const mockHttpService = {
      post: jest.fn(),
      get: jest.fn(),
    };

    const mockJwtTokenService = {
      generateToken: jest.fn(),
      generateRefreshToken: jest.fn(),
      isTokenNearExpiry: jest.fn(),
      verifyToken: jest.fn(),
    };

    const mockIsamService = {
      performAuthentication: jest.fn(),
      performRestPortalAuthentication: jest.fn(),
      createRestPortalPayload: jest.fn(),
      hasActiveSession: jest.fn(),
    };

    const mockStateIdParserService = {
      extractStateId: jest.fn(),
      parseStateIdFromHtml: jest.fn(),
    };

    const mockI18nService = {
      translate: jest.fn(),
    };

    const mockRedisCacheService = {
      get: jest.fn(),
      set: jest.fn(),
      del: jest.fn(),
    };

    const mockLogger = {
      log: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn(),
      verbose: jest.fn(),
    };

    const mockCaptchaService = {
      validateCaptchaChallenge: jest.fn(),
      validateCaptcha: jest.fn(),
      generateCaptcha: jest.fn(),
      createHashedLoginAttemptsKey: jest.fn(),
      incrementFailedAttempts: jest.fn(),
      getFailedAttempts: jest.fn(),
      resetFailedAttempts: jest.fn(),
      buildCaptchaResponse: jest.fn(),
    };

    const mockUserDetailsService = {
      fetchUserDetails: jest.fn(),
      checkEligibility: jest.fn(),
    };

    const mockTerminationService = {
      handleTermination: jest.fn(),
      extractTerminationInfo: jest.fn(),
    };

    const mockResetPasswordService = {
      resetPassword: jest.fn(),
    };

    const mockTermsAcceptanceService = {
      acceptTerms: jest.fn(),
    };

    const mockOtpActivationService = {
      checkAndHandleOtpActivationRequired: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        { provide: ConfigService, useValue: mockConfigService },
        { provide: HttpService, useValue: mockHttpService },
        { provide: JwtTokenService, useValue: mockJwtTokenService },
        { provide: IsamService, useValue: mockIsamService },
        { provide: StateIdParserService, useValue: mockStateIdParserService },
        { provide: I18nLookupService, useValue: mockI18nService },
        { provide: RedisCacheService, useValue: mockRedisCacheService },
        { provide: CaptchaService, useValue: mockCaptchaService },
        { provide: UserDetailsService, useValue: mockUserDetailsService },
        { provide: TerminationService, useValue: mockTerminationService },
        { provide: ResetPasswordService, useValue: mockResetPasswordService },
        { provide: TermsAcceptanceService, useValue: mockTermsAcceptanceService },
        { provide: OtpActivationService, useValue: mockOtpActivationService },
        { provide: Logger, useValue: mockLogger },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    configService = module.get(ConfigService);
    httpService = module.get(HttpService);
    jwtTokenService = module.get(JwtTokenService);
    isamService = module.get(IsamService);
    stateIdParserService = module.get(StateIdParserService);
    i18nService = module.get(I18nLookupService);
    redisCacheService = module.get(RedisCacheService);
    captchaService = module.get(CaptchaService);
    userDetailsService = module.get(UserDetailsService);
    terminationService = module.get(TerminationService);
    resetPasswordService = module.get(ResetPasswordService);
    termsAcceptanceService = module.get(TermsAcceptanceService);
    otpActivationService = module.get(OtpActivationService);

    // Setup default config values
    configService.get.mockImplementation((key: string, defaultValue?: any) => {
      const config = {
        AUTH_MAX_ATTEMPTS: 5,
        AUTH_CAPTCHA_THRESHOLD: 3,
        AUTH_ATTEMPT_TTL: 86400,
        REDIS_TOKEN_TTL: 600,
        FCC_URL: 'https://dev.example.com',
      };
      return config[key] ?? defaultValue;
    });

    configService.getOrThrow.mockImplementation((key: string) => {
      const config = {
        ISAM_BASE_URL: 'https://isam.example.com/FBCCBB',
      };
      return config[key];
    });

    // Setup default i18n responses
    i18nService.translate.mockImplementation((key: string, lang: UserLanguage) => {
      const translations = {
        'auth.login.success.title': 'Login Successful',
        'auth.login.success.body': 'Welcome back!',
        'auth.login.logout_success.title': 'Logout Successful',
        'auth.login.logout_success.body': 'You have been logged out successfully',
        'auth.login.invalid_credentials.title': 'Invalid Credentials',
        'auth.login.invalid_credentials.body': 'The provided credentials are incorrect',
        'auth.login.locked_account.title': 'Account Locked',
        'auth.login.locked_account.body': 'Your account has been locked',
        'auth.login.system_error.title': 'System Error',
        'auth.login.system_error.body': 'A system error occurred',
      };
      return translations[key] || key;
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('login', () => {
    it('should handle termination login when termination header is provided', async () => {
      const terminationHeader = 'termination-token-123';
      const expectedResponse: AuthResponseDto = {
        responseCode: AuthResponseCode.SUCCESS,
        message: 'Termination handled',
        title: 'Success',
      };

      terminationService.handleTermination.mockResolvedValue(expectedResponse);

      const result = await service.login(mockLoginDto, terminationHeader, UserLanguage['en-US']);

      expect(terminationService.handleTermination).toHaveBeenCalledWith(
        terminationHeader,
        UserLanguage['en-US']
      );
      expect(result).toEqual(expectedResponse);
    });

    it('should handle normal login when no termination header is provided', async () => {
      const expectedResponse: AuthResponseDto = {
        responseCode: AuthResponseCode.SUCCESS,
        message: 'Login successful',
        title: 'Success',
        token: 'jwt-token',
      };

      // Mock the captcha validation to return null (no captcha required)
      captchaService.validateCaptchaChallenge.mockResolvedValue(null);

      // Mock state extraction
      stateIdParserService.extractStateId.mockResolvedValue({
        stateId: 'state123',
        response: { statusCode: 200, body: 'portal response', headers: { 'set-cookie': ['portal=cookie'] } } as any,
      });

      // Mock ISAM authentication
      isamService.performAuthentication.mockResolvedValue({
        statusCode: '200',
        body: 'Authentication successful',
        headers: { 'set-cookie': ['session=abc123'] },
      } as any);

      // Mock successful authentication completion
      const mockAuthResult = {
        token: 'jwt-token',
        userData: { firstName: 'John', lastName: 'Doe' },
        userDetails: { userId: 'user123' },
        segment: 'business_banking',
      };

      // We need to spy on the private method through the service
      jest.spyOn(service as any, 'completeAuthentication').mockResolvedValue(mockAuthResult);

      // Mock captcha service methods
      captchaService.createHashedLoginAttemptsKey.mockReturnValue('hashed-key');
      captchaService.resetFailedAttempts.mockResolvedValue();

      const result = await service.login(mockLoginDto, '', UserLanguage['en-US']);

      expect(result.responseCode).toBe(AuthResponseCode.SUCCESS);
      expect(result.token).toBe('jwt-token');
    });

    it('should handle captcha validation failure', async () => {
      const captchaResponse: AuthResponseDto = {
        responseCode: AuthResponseCode.CAPTCHA_REQUIRED,
        message: 'Captcha required',
        title: 'Captcha Required',
      };

      captchaService.validateCaptchaChallenge.mockResolvedValue(captchaResponse);

      const result = await service.login(mockLoginDto, '', UserLanguage['en-US']);

      expect(captchaService.validateCaptchaChallenge).toHaveBeenCalledWith(
        mockLoginDto,
        UserLanguage['en-US']
      );
      expect(result).toEqual(captchaResponse);
    });

    it('should handle system errors during login', async () => {
      captchaService.validateCaptchaChallenge.mockRejectedValue(new Error('System error'));

      const result = await service.login(mockLoginDto, '', UserLanguage['en-US']);

      expect(result.responseCode).toBe(AuthResponseCode.SYSTEM_ERROR);
    });
  });

  describe('logout', () => {
    const mockAxiosResponse = {
      data: { success: true },
      status: 200,
      statusText: 'OK',
      headers: {},
      config: { headers: {} as any },
    };

    it('should logout successfully and always return success', async () => {
      httpService.post.mockReturnValue(of(mockAxiosResponse));
      httpService.get.mockReturnValue(of(mockAxiosResponse));

      const result = await service.logout(mockCurrentUser, UserLanguage['en-US']);

      expect(result.responseCode).toBe(AuthResponseCode.SUCCESS);
      expect(i18nService.translate).toHaveBeenCalledWith(
        'auth.login.logout_success.title',
        UserLanguage['en-US']
      );

      // Verify REST portal logout call
      expect(httpService.post).toHaveBeenCalledWith(
        'https://isam.example.com/FBCCBB/restportal/fbcclogout/',
        {
          userData: {
            company: mockCurrentUser.company,
            username: mockCurrentUser.username,
            userSelectedLanguage: null,
          },
          requestData: null,
        },
        expect.objectContaining({
          headers: {
            'Content-Type': 'application/json',
            Cookie: mockCurrentUser.cookies,
          },
        })
      );

      // Verify ISAM logout call
      expect(httpService.get).toHaveBeenCalledWith(
        'https://isam.example.com/pkmslogout',
        expect.objectContaining({
          headers: { Cookie: mockCurrentUser.cookies },
        })
      );
    });

    it('should return success even when REST portal logout fails', async () => {
      httpService.post.mockReturnValue(throwError(() => new Error('REST portal error')));
      httpService.get.mockReturnValue(of(mockAxiosResponse));

      const result = await service.logout(mockCurrentUser, UserLanguage['en-US']);

      expect(result.responseCode).toBe(AuthResponseCode.SUCCESS);
    });

    it('should return success even when ISAM logout fails', async () => {
      httpService.post.mockReturnValue(of(mockAxiosResponse));
      httpService.get.mockReturnValue(throwError(() => new Error('ISAM error')));

      const result = await service.logout(mockCurrentUser, UserLanguage['en-US']);

      expect(result.responseCode).toBe(AuthResponseCode.SUCCESS);
    });

    it('should return success even when both logout calls fail', async () => {
      httpService.post.mockReturnValue(throwError(() => new Error('REST portal error')));
      httpService.get.mockReturnValue(throwError(() => new Error('ISAM error')));

      const result = await service.logout(mockCurrentUser, UserLanguage['en-US']);

      expect(result.responseCode).toBe(AuthResponseCode.SUCCESS);
    });
  });

  describe('devLogin', () => {
    it('should successfully perform development login', async () => {
      const mockPayload = { username: 'testuser', password: 'testpass', company: 'testco' };
      const mockDevResponse = {
        body: JSON.stringify({ firstName: 'John', lastName: 'Doe' }),
        headers: { 'set-cookie': ['session=dev123'] },
      };
      const mockUserDetails = {
        userId: 'user123',
        customerReferences: [{ ID: 'cif123' }],
      };

      isamService.createRestPortalPayload.mockReturnValue(mockPayload);
      isamService.performRestPortalAuthentication.mockResolvedValue(mockDevResponse);
      userDetailsService.fetchUserDetails.mockResolvedValue(mockUserDetails);
      jwtTokenService.generateToken.mockResolvedValue('dev-jwt-token');

      // Mock the private extractHeadersAndCookies method
      jest.spyOn(service as any, 'extractHeadersAndCookies').mockReturnValue(['session=dev123']);

      const result = await service.devLogin(mockLoginDto, UserLanguage['en-US']);

      expect(result.responseCode).toBe(AuthResponseCode.SUCCESS);
      expect(result.token).toBe('dev-jwt-token');
      expect(isamService.createRestPortalPayload).toHaveBeenCalledWith(
        {
          username: mockLoginDto.username,
          company: mockLoginDto.company,
          password: mockLoginDto.password,
        },
        UserLanguage['en-US']
      );
    });

    it('should handle development login failure', async () => {
      isamService.createRestPortalPayload.mockImplementation(() => {
        throw new Error('Dev login error');
      });

      const result = await service.devLogin(mockLoginDto, UserLanguage['en-US']);

      expect(result.responseCode).toBe(AuthResponseCode.INVALID_CREDENTIALS);
    });
  });

  describe('refreshToken', () => {
    it('should successfully refresh a valid token', async () => {
      const authHeader = 'Bearer valid-jwt-token';
      const newToken = 'new-jwt-token';

      jwtTokenService.generateRefreshToken.mockResolvedValue(newToken);
      jwtTokenService.isTokenNearExpiry.mockResolvedValue(false);

      const result = await service.refreshToken(authHeader);

      expect(result.token).toBe(newToken);
      expect(result.nearExpiry).toBe(false);
      expect(jwtTokenService.generateRefreshToken).toHaveBeenCalledWith('valid-jwt-token');
    });

    it('should detect near expiry tokens', async () => {
      const authHeader = 'Bearer near-expiry-token';
      const newToken = 'new-jwt-token';

      jwtTokenService.generateRefreshToken.mockResolvedValue(newToken);
      jwtTokenService.isTokenNearExpiry.mockResolvedValue(true);

      const result = await service.refreshToken(authHeader);

      expect(result.token).toBe(newToken);
      expect(result.nearExpiry).toBe(true);
    });

    it('should throw UnauthorizedException when authorization header is missing', async () => {
      await expect(service.refreshToken('')).rejects.toThrow(UnauthorizedException);
      await expect(service.refreshToken(null)).rejects.toThrow(UnauthorizedException);
    });

    it('should handle token refresh failures', async () => {
      const authHeader = 'Bearer invalid-token';
      jwtTokenService.generateRefreshToken.mockRejectedValue(new Error('Token refresh failed'));

      await expect(service.refreshToken(authHeader)).rejects.toThrow(UnauthorizedException);
    });

    it('should propagate UnauthorizedException from token service', async () => {
      const authHeader = 'Bearer invalid-token';
      const tokenError = new UnauthorizedException('Token expired');
      jwtTokenService.generateRefreshToken.mockRejectedValue(tokenError);

      await expect(service.refreshToken(authHeader)).rejects.toThrow(tokenError);
    });
  });

  describe('resetPassword', () => {
    it('should delegate to reset password service', async () => {
      const resetToken = 'reset-token-123';
      const expectedResponse: AuthResponseDto = {
        responseCode: AuthResponseCode.SUCCESS,
        message: 'Password reset successful',
        title: 'Success',
      };

      resetPasswordService.resetPassword.mockResolvedValue(expectedResponse);

      const result = await service.resetPassword(
        mockResetPasswordDto,
        resetToken,
        UserLanguage['en-US']
      );

      expect(resetPasswordService.resetPassword).toHaveBeenCalledWith(
        mockResetPasswordDto,
        resetToken,
        UserLanguage['en-US']
      );
      expect(result).toEqual(expectedResponse);
    });
  });

  describe('acceptTerms', () => {
    it('should delegate to terms acceptance service', async () => {
      const expectedResponse: AuthResponseDto = {
        responseCode: AuthResponseCode.SUCCESS,
        message: 'Terms accepted successfully',
        title: 'Success',
      };

      termsAcceptanceService.acceptTerms.mockResolvedValue(expectedResponse);

      const result = await service.acceptTerms(mockCurrentUser, UserLanguage['en-US']);

      expect(termsAcceptanceService.acceptTerms).toHaveBeenCalledWith(
        mockCurrentUser,
        UserLanguage['en-US']
      );
      expect(result).toEqual(expectedResponse);
    });
  });

  describe('private helper methods', () => {
    describe('hasIncorrectCredentialsError', () => {
      it('should detect incorrect credentials error in HTML', () => {
        const htmlWithError = '<div>Your user or password is incorrect</div>';
        const htmlWithoutError = '<div>Some other content</div>';

        expect(service['hasIncorrectCredentialsError'](htmlWithError)).toBe(true);
        expect(service['hasIncorrectCredentialsError'](htmlWithoutError)).toBe(false);
      });
    });

    describe('hasLockedAccountError', () => {
      it('should detect locked account error in HTML', () => {
        const htmlWithError = '<div>Your account has been locked</div>';
        const htmlWithoutError = '<div>Some other content</div>';

        expect(service['hasLockedAccountError'](htmlWithError)).toBe(true);
        expect(service['hasLockedAccountError'](htmlWithoutError)).toBe(false);
      });
    });

    describe('hasPasswordExpiredError', () => {
      it('should detect password expired error in HTML', () => {
        const htmlWithError = '<div>Your password is expired</div>';
        const htmlWithoutError = '<div>Some other content</div>';

        expect(service['hasPasswordExpiredError'](htmlWithError)).toBe(true);
        expect(service['hasPasswordExpiredError'](htmlWithoutError)).toBe(false);
      });
    });

    describe('extractHeadersAndCookies', () => {
      it('should extract headers and cookies from response', () => {
        const responseWithCookies = {
          headers: {
            'set-cookie': ['session=abc123', 'token=xyz789'],
          },
        };
        const responseWithoutCookies = {
          headers: {},
        };

        expect(service['extractHeadersAndCookies'](responseWithCookies)).toEqual([
          'session=abc123',
          'token=xyz789',
        ]);
        expect(service['extractHeadersAndCookies'](responseWithoutCookies)).toEqual([]);
      });
    });
  });

  describe('complex authentication flows', () => {
    describe('handleNormalLogin edge cases', () => {
      it('should handle locked account response', async () => {
        captchaService.validateCaptchaChallenge.mockResolvedValue(null);
        stateIdParserService.extractStateId.mockResolvedValue({
          stateId: 'state123',
          response: { statusCode: 200, body: 'portal response', headers: { 'set-cookie': ['portal=cookie'] } },
        });

        isamService.performAuthentication.mockResolvedValue({
          statusCode: '200',
          body: 'Your account has been locked',
          headers: { 'set-cookie': ['session=abc123'] },
        });

        const result = await service.login(mockLoginDto, '', UserLanguage['en-US']);

        expect(result.responseCode).toBe(AuthResponseCode.LOCKED_ACCOUNT);
      });

      it('should handle incorrect credentials with captcha threshold reached', async () => {
        captchaService.validateCaptchaChallenge.mockResolvedValue(null);
        stateIdParserService.extractStateId.mockResolvedValue({
          stateId: 'state123',
          response: { statusCode: 200, body: 'portal response', headers: { 'set-cookie': ['portal=cookie'] } },
        });

        isamService.performAuthentication.mockResolvedValue({
          statusCode: '200',
          body: 'Your user or password is incorrect',
          headers: { 'set-cookie': ['session=abc123'] },
        });

        captchaService.createHashedLoginAttemptsKey.mockReturnValue('hashed-key');
        captchaService.incrementFailedAttempts.mockResolvedValue();
        captchaService.getFailedAttempts.mockResolvedValue(3); // Threshold reached
        captchaService.generateCaptcha.mockResolvedValue({
          image: 'captcha-image-data',
          token: 'captcha-token',
        });

        const result = await service.login(mockLoginDto, '', UserLanguage['en-US']);

        expect(result.responseCode).toBe(AuthResponseCode.CAPTCHA_REQUIRED);
      });

      it('should handle password expired response', async () => {
        captchaService.validateCaptchaChallenge.mockResolvedValue(null);
        stateIdParserService.extractStateId.mockResolvedValue({
          stateId: 'state123',
          response: { statusCode: 200, body: 'portal response', headers: { 'set-cookie': ['portal=cookie'] } },
        });

        isamService.performAuthentication.mockResolvedValue({
          statusCode: '200',
          body: 'Your password is expired',
          headers: { 'set-cookie': ['session=abc123'] },
        });

        jest.spyOn(service as any, 'extractResetInfo').mockResolvedValue('reset-token-123');
        captchaService.createHashedLoginAttemptsKey.mockReturnValue('hashed-key');
        captchaService.resetFailedAttempts.mockResolvedValue();

        const result = await service.login(mockLoginDto, '', UserLanguage['en-US']);

        expect(result.responseCode).toBe(AuthResponseCode.EXPIRED_PASSWORD);
        expect(result.resetPasswordToken).toBe('reset-token-123');
      });

      it('should handle active session response', async () => {
        captchaService.validateCaptchaChallenge.mockResolvedValue(null);
        stateIdParserService.extractStateId.mockResolvedValue({
          stateId: 'state123',
          response: { statusCode: 200, body: 'portal response', headers: { 'set-cookie': ['portal=cookie'] } },
        });

        const isamResponse = {
          statusCode: '200',
          body: 'active session detected',
          headers: { 'set-cookie': ['session=abc123'] },
        };

        isamService.performAuthentication.mockResolvedValue(isamResponse);
        isamService.hasActiveSession.mockReturnValue(true);

        terminationService.extractTerminationInfo.mockResolvedValue({
          terminationToken: 'termination-token-123',
        });

        captchaService.createHashedLoginAttemptsKey.mockReturnValue('hashed-key');
        captchaService.resetFailedAttempts.mockResolvedValue();

        const result = await service.login(mockLoginDto, '', UserLanguage['en-US']);

        expect(result.responseCode).toBe(AuthResponseCode.ACTIVE_SESSION);
        expect(result.terminationToken).toBe('termination-token-123');
      });
    });

    describe('completeAuthentication', () => {
      it('should handle OTP activation required response', async () => {
        const mockUserData = { mode: 'otp_activation', firstName: 'John' };
        const mockParsedHeaders = 'session=abc123; token=xyz789';
        
        isamService.performRestPortalAuthentication.mockResolvedValue({
          body: JSON.stringify(mockUserData),
          headers: { 'set-cookie': ['session=abc123'] },
        });

        otpActivationService.checkAndHandleOtpActivationRequired.mockResolvedValue({
          otpActivationToken: 'otp-token-123',
          tokenType: 'SMS',
          otpActivationStep: 'step1',
        });

        jest.spyOn(service as any, 'extractHeadersAndCookies').mockReturnValue(['session=abc123']);

        const result = await service['completeAuthentication'](
          { headers: {} },
          { headers: {} },
          'state123',
          UserLanguage['en-US'],
          mockLoginDto
        );

        expect(result.otpActivationRequired).toBe(true);
        expect(result.otpActivationToken).toBe('otp-token-123');
      });

      it('should handle terms acceptance required response', async () => {
        const mockUserData = { 
          mode: 'accept_terms', 
          objectData: { 
            tandctext: 'Terms and conditions text',
            tokentype: 'NO_REAUTH'
          } 
        };
        
        isamService.performRestPortalAuthentication.mockResolvedValue({
          body: JSON.stringify(mockUserData),
          headers: { 'set-cookie': ['session=abc123'] },
        });

        otpActivationService.checkAndHandleOtpActivationRequired.mockResolvedValue(null);
        jwtTokenService.generateToken.mockResolvedValue('terms-jwt-token');

        jest.spyOn(service as any, 'extractHeadersAndCookies').mockReturnValue(['session=abc123']);

        const result = await service['completeAuthentication'](
          { headers: {} },
          { headers: {} },
          'state123',
          UserLanguage['en-US'],
          mockLoginDto
        );

        expect(result.termsRequired).toBe(true);
        expect(result.terms).toBe('Terms and conditions text');
        expect(result.token).toBe('terms-jwt-token');
      });

      it('should handle successful authentication with user details', async () => {
        const mockUserData = { firstName: 'John', lastName: 'Doe' };
        const mockUserDetails = {
          userDetails: {
            userId: 'user123',
            customerReferences: [{ name: 'cif123' }],
          },
          t24CompanyProfile: { industry: '104' },
          segment: { id: 'business_banking' },
        };

        isamService.performRestPortalAuthentication.mockResolvedValue({
          body: JSON.stringify(mockUserData),
          headers: { 'set-cookie': ['session=abc123'] },
        });

        otpActivationService.checkAndHandleOtpActivationRequired.mockResolvedValue(null);
        userDetailsService.checkEligibility.mockResolvedValue(mockUserDetails);
        jwtTokenService.generateToken.mockResolvedValue('success-jwt-token');

        jest.spyOn(service as any, 'extractHeadersAndCookies').mockReturnValue(['session=abc123']);

        const result = await service['completeAuthentication'](
          { headers: {} },
          { headers: {} },
          'state123',
          UserLanguage['en-US'],
          mockLoginDto
        );

        expect(result.token).toBe('success-jwt-token');
        expect(result.userData).toEqual(mockUserData);
        expect(result.userDetails).toEqual(mockUserDetails.userDetails);
      });

      it('should handle authentication exception during user eligibility check', async () => {
        const mockUserData = { firstName: 'John', lastName: 'Doe' };
        const authException = new AuthenticationException(
          AuthResponseCode.NOT_ELIGIBLE,
          'auth.error.not_eligible',
          'User not eligible'
        );

        isamService.performRestPortalAuthentication.mockResolvedValue({
          body: JSON.stringify(mockUserData),
          headers: { 'set-cookie': ['session=abc123'] },
        });

        otpActivationService.checkAndHandleOtpActivationRequired.mockResolvedValue(null);
        userDetailsService.checkEligibility.mockRejectedValue(authException);

        // Mock logout to avoid actual logout during test
        jest.spyOn(service, 'logout').mockResolvedValue({
          responseCode: AuthResponseCode.SUCCESS,
          message: 'Logged out',
          title: 'Success',
        });

        jest.spyOn(service as any, 'extractHeadersAndCookies').mockReturnValue(['session=abc123']);

        const result = await service['completeAuthentication'](
          { headers: {} },
          { headers: {} },
          'state123',
          UserLanguage['en-US'],
          mockLoginDto
        );

        expect(result.responseCode).toBe(AuthResponseCode.NOT_ELIGIBLE);
        expect(service.logout).toHaveBeenCalled();
      });
    });
  });
});
