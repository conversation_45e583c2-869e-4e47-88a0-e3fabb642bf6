import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { UnauthorizedException } from '@nestjs/common';
import { of, throwError } from 'rxjs';

import { AuthService } from '@/auth/services/auth.service';
import { JwtTokenService } from '@/auth/services/jwt-token.service';
import { IsamService } from '@/auth/services/isam.service';
import { StateIdParserService } from '@/auth/services/stateIdParserService';
import { CaptchaService } from '@/auth/services/captcha.service';
import { UserDetailsService } from '@/auth/services/user-details.service';
import { TerminationService } from '@/auth/services/termination.service';
import { ResetPasswordService } from '@/auth/services/reset-password.service';
import { TermsAcceptanceService } from '@/auth/services/terms-acceptance.service';
import { OtpActivationService } from '@/auth/services/otp-activation.service';
import { I18nLookupService, RedisCacheService } from '@modules';
import { LoginDto } from '@/auth/dto/login.dto';
import { AuthResponseCode } from '@/auth/types/auth-response.types';
import { UserLanguage } from '@enums';

describe('AuthService - Simplified Tests', () => {
  let service: AuthService;
  let configService: jest.Mocked<ConfigService>;
  let httpService: jest.Mocked<HttpService>;
  let jwtTokenService: jest.Mocked<JwtTokenService>;

  const mockLoginDto: LoginDto = {
    username: 'testuser',
    password: 'testpassword',
    company: 'testcompany',
  };

  beforeEach(async () => {
    // Create the mock config service with pre-configured return values
    const mockConfigService = {
      get: jest.fn().mockImplementation((key: string, defaultValue?: any) => {
        const config = {
          AUTH_MAX_ATTEMPTS: 5,
          AUTH_CAPTCHA_THRESHOLD: 3,
          AUTH_ATTEMPT_TTL: 86400,
          REDIS_TOKEN_TTL: 600,
        };
        return config[key] ?? defaultValue;
      }),
      getOrThrow: jest.fn().mockReturnValue('https://isam.example.com/FBCCBB'),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        { provide: ConfigService, useValue: mockConfigService },
        { provide: HttpService, useValue: { post: jest.fn(), get: jest.fn() } },
        { provide: JwtTokenService, useValue: { generateRefreshToken: jest.fn(), isTokenNearExpiry: jest.fn() } },
        { provide: IsamService, useValue: {} },
        { provide: StateIdParserService, useValue: {} },
        { provide: I18nLookupService, useValue: { translate: jest.fn() } },
        { provide: RedisCacheService, useValue: {} },
        { provide: CaptchaService, useValue: {} },
        { provide: UserDetailsService, useValue: {} },
        { provide: TerminationService, useValue: { handleTermination: jest.fn() } },
        { provide: ResetPasswordService, useValue: { resetPassword: jest.fn() } },
        { provide: TermsAcceptanceService, useValue: { acceptTerms: jest.fn() } },
        { provide: OtpActivationService, useValue: {} },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    configService = module.get(ConfigService);
    httpService = module.get(HttpService);
    jwtTokenService = module.get(JwtTokenService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('refreshToken', () => {
    it('should successfully refresh a valid token', async () => {
      const authHeader = 'Bearer valid-jwt-token';
      const newToken = 'new-jwt-token';

      jwtTokenService.generateRefreshToken.mockResolvedValue(newToken);
      jwtTokenService.isTokenNearExpiry.mockResolvedValue(false);

      const result = await service.refreshToken(authHeader);

      expect(result.token).toBe(newToken);
      expect(result.nearExpiry).toBe(false);
      expect(jwtTokenService.generateRefreshToken).toHaveBeenCalledWith('valid-jwt-token');
    });

    it('should throw UnauthorizedException when authorization header is missing', async () => {
      await expect(service.refreshToken('')).rejects.toThrow(UnauthorizedException);
      await expect(service.refreshToken(null as any)).rejects.toThrow(UnauthorizedException);
    });

    it('should handle token refresh failures', async () => {
      const authHeader = 'Bearer invalid-token';
      jwtTokenService.generateRefreshToken.mockRejectedValue(new Error('Token refresh failed'));

      await expect(service.refreshToken(authHeader)).rejects.toThrow(UnauthorizedException);
    });
  });

  describe('logout', () => {
    const mockCurrentUser = {
      username: 'testuser',
      company: 'testcompany',
      cookies: 'session=abc123',
      userId: 'user123',
    };

    const mockAxiosResponse = {
      data: { success: true },
      status: 200,
      statusText: 'OK',
      headers: {},
      config: { headers: {} } as any,
    };

    it('should always return success even when logout calls fail', async () => {
      httpService.post.mockReturnValue(throwError(() => new Error('REST portal error')));
      httpService.get.mockReturnValue(throwError(() => new Error('ISAM error')));

      const result = await service.logout(mockCurrentUser as any, UserLanguage['en-US']);

      expect(result.responseCode).toBe(AuthResponseCode.SUCCESS);
    });
  });

  describe('private helper methods', () => {
    it('should detect incorrect credentials error in HTML', () => {
      const htmlWithError = '<div>Your user or password is incorrect</div>';
      const htmlWithoutError = '<div>Some other content</div>';

      expect(service['hasIncorrectCredentialsError'](htmlWithError)).toBe(true);
      expect(service['hasIncorrectCredentialsError'](htmlWithoutError)).toBe(false);
    });

    it('should detect locked account error in HTML', () => {
      const htmlWithError = '<div>Your account has been locked</div>';
      const htmlWithoutError = '<div>Some other content</div>';

      expect(service['hasLockedAccountError'](htmlWithError)).toBe(true);
      expect(service['hasLockedAccountError'](htmlWithoutError)).toBe(false);
    });

    it('should extract headers and cookies from response', () => {
      const responseWithCookies = {
        headers: {
          'set-cookie': ['session=abc123', 'token=xyz789'],
        },
      };

      expect(service['extractHeadersAndCookies'](responseWithCookies)).toEqual([
        'session=abc123',
        'token=xyz789',
      ]);
    });
  });
});
