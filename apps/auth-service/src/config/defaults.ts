import * as process from 'node:process';

export default () => ({
  captcha: {
    TTL: process.env.CAPTCHA_TTL || 300,
    size: process.env.CAPTCHA_SIZE || 6,
    color: process.env.CAPTCHA_COLOR || 'true',
    maxAttempts: process.env.CAPTCHA_MAX_ATTEMPTS || 5,
    threshold: process.env.CAPTCHA_THRESHOLD || 3,
    noise: process.env.CAPTCHA_NOISE || 2,
    attemptsExpirySeconds: process.env.ATTEMPTS_EXPIRY_SECONDS || 3600,
  },
  isamBaseUrl: process.env.ISAM_BASE_URL
});
