import {
  IsString,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
} from 'class-validator';

/**
 * DTO for change request.
 * Used when user is logged in and wants to change their credentials.
 */
export class ChangePasswordDto {
  @IsString()
  @IsNotEmpty()
  @MaxLength(20)
  @Matches(/^[^\u0600-\u06FF]+$/, {
    message: 'Old password cannot contain Arabic letters',
  })
  oldPassword: string;

  @IsString()
  @IsNotEmpty()
  @MaxLength(20)
  @Matches(/^[^\u0600-\u06FF]+$/, {
    message: 'New password cannot contain Arabic letters',
  })
  newPassword: string;

  @IsString()
  @IsNotEmpty()
  @MaxLength(20)
  @Matches(/^[^\u0600-\u06FF]+$/, {
    message: 'Confirm password cannot contain Arabic letters',
  })
  confirmPassword: string;
}
