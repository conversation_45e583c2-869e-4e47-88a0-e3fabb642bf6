import { Expose } from 'class-transformer';
import { AuthResponseCode } from '../types/auth-response.types';

/**
 * DTO for authentication responses.
 * Includes captcha challenge data when required.
 */
export class AuthResponseDto {
  @Expose()
  responseCode: AuthResponseCode;

  @Expose()
  title?: string;

  @Expose()
  message: string;

  @Expose()
  token?: string;

  @Expose()
  otpActivationToken?: string;

  @Expose()
  otpActivationStep?: string;

  @Expose()
  termsAndConditionToken?: string;

  @Expose()
  otpActivationRequired?: boolean;

  // Contains captchaImage and captchaToken when required
  @Expose()
  userData?: any;

  @Expose()
  segment?: any;

  @Expose()
  terms?: string;

  @Expose()
  tokenType?: string;

  @Expose()
  terminationToken?: string;

  @Expose()
  resetPasswordToken?: string;

  @Expose()
  nearExpiry?: boolean;

  @Expose()
  termsRequired?: boolean;

  @Expose()
  captchaRequired?: boolean;

  @Expose()
  passwordHints?: string[];

  @Expose()
  userDetails?: any;

  @Expose()
  attributes?: any;
}
