import {
  IsString,
  IsNotEmpty,
  Matches,
  <PERSON><PERSON>ength,
  IsOptional,
} from 'class-validator';

/**
 * DTO for user login.
 * Includes required validations for username, credentials, and company ID.
 * Includes optional captcha fields for challenge/response.
 */
export class LoginDto {
  @IsString()
  @IsNotEmpty()
  @MaxLength(30)
  @Matches(/^[a-zA-Z0-9_-]+$/, {
    message:
      'Username cannot contain spaces, special characters, or Arabic letters',
  })
  username: string;

  @IsString()
  @IsNotEmpty()
  @MaxLength(20)
  @Matches(/^[^\u0600-\u06FF]+$/, {
    message: 'Password cannot contain Arabic letters',
  })
  password: string;

  @IsString()
  @IsNotEmpty()
  @MaxLength(35)
  @Matches(/^[a-zA-Z0-9_-]+$/, {
    message:
      'Company ID cannot contain spaces, special characters, or Arabic letters',
  })
  company: string;

  @IsOptional()
  @IsString()
  captchaToken?: string;

  @IsOptional()
  @IsString()
  captchaText?: string;
}
