import { IsNotEmpty, IsString, Matches, MaxLength } from 'class-validator';

export class OtpActivationStep1Dto {
  @IsString()
  @IsNotEmpty()
  @MaxLength(20)
  @Matches(/^[0-9]+$/, {
    message: 'National ID must contain only numbers',
  })
  nationalId: string;

  @IsString()
  @IsNotEmpty()
  @Matches(/^[0-9+\-\s()]+$/, {
    message: 'Phone number must be valid',
  })
  phoneNumber: string;
}

export class OtpActivationStep2Dto {
  @IsString()
  @IsNotEmpty()
  verificationToken: string;
}

export class OtpActivationStep3Dto {
  @IsString()
  @IsNotEmpty()
  otpCode: string;
}

export class HardTokenActivationStep1Dto {
  @IsString()
  @IsNotEmpty()
  @MaxLength(20)
  @Matches(/^[0-9]+$/, {
    message: 'National ID must contain only numbers',
  })
  nationalId: string;

  @IsString()
  @IsNotEmpty()
  @Matches(/^[0-9+\-\s()]+$/, {
    message: 'Phone number must be valid',
  })
  phoneNumber: string;

  @IsString()
  @IsNotEmpty()
  @Matches(/^[0-9]{6}$/, {
    message: 'OTP must be 6 digits',
  })
  otpCode: string;

  @IsString()
  @IsNotEmpty()
  serialNumber: string;
}

export class HardTokenActivationStep2Dto {
  @IsString()
  @IsNotEmpty()
  @Matches(/^[0-9]{6}$/, {
    message: 'OTP must be 6 digits',
  })
  otpCode: string;
}
