import { IsNotEmpty, IsString, Matches, MaxLength } from 'class-validator';

export class ForgetPasswordOptionsDto {
  @IsString()
  @IsNotEmpty()
  @MaxLength(30)
  @Matches(/^[a-zA-Z0-9_-]+$/, {
    message:
      '<PERSON>rname cannot contain spaces, special characters, or Arabic letters',
  })
  username: string;

  @IsString()
  @IsNotEmpty()
  @MaxLength(35)
  @Matches(/^[a-zA-Z0-9_-]+$/, {
    message:
      'Company ID cannot contain spaces, special characters, or Arabic letters',
  })
  company: string;

  @IsString()
  @IsNotEmpty()
  captchaToken: string;

  @IsString()
  @IsNotEmpty()
  captchaText: string;
}