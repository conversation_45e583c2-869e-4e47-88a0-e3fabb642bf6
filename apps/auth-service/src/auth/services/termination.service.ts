import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtTokenService } from './jwt-token.service';
import { IsamService } from './isam.service';
import { StateIdParserService } from './stateIdParserService';
import { I18nLookupService } from '@modules';
import { UserLanguage } from '@enums';
import { AuthResponseDto } from '../dto/auth-response.dto';
import { AuthResponseCode } from '../types/auth-response.types';
import { AUTH_MESSAGES } from '../constants/auth-messages.constants';
import { buildResponse } from '../utils/response.utils';
import { parseSetCookieHeaders } from '../utils/cookie.utils';
import {
  ActiveSessionResponse,
  IsamError,
  IsamErrorCodes,
} from '../types/isam.types';
import { lastValueFrom } from 'rxjs';
import { HttpService } from '@nestjs/axios';
import { UserDetailsService } from './user-details.service';
import { AuthenticationException } from '../exceptions/authentication.exception';
import { OtpActivationService } from './otp-activation.service';

@Injectable()
export class TerminationService {
  private readonly logger: Logger;

  constructor(
    private readonly configService: ConfigService,
    private readonly jwtTokenService: JwtTokenService,
    private readonly isamService: IsamService,
    private readonly stateIdParserService: StateIdParserService,
    private readonly i18nService: I18nLookupService,
    private readonly httpService: HttpService,
    private readonly userDetailsService: UserDetailsService,
    private readonly otpActivationService: OtpActivationService,
  ) {
    this.logger = new Logger(TerminationService.name);
  }

  /**
   * Handle session termination flow
   */
  async handleTermination(
    terminationHeader: string,
    language: UserLanguage,
  ): Promise<AuthResponseDto> {
    try {
      const result = await this.terminateSession(terminationHeader, language);

      if (result.responseCode == 'NOT_ELIGIBLE') {
        return result;
      } else if (result.otpActivationRequired) {
        return {
          ...buildResponse(
            this.i18nService,
            AuthResponseCode.OTP_ACTIVATION_REQUIRED,
            AUTH_MESSAGES.LOGIN.OTP_ACTIVATION_REQUIRED,
            language,
          ),
          otpActivationToken: result.otpActivationToken,
          otpActivationStep: result.otpActivationStep,
          tokenType: result.tokenType,
        };
      } else if (result.termsRequired) {
        return {
          ...buildResponse(
            this.i18nService,
            AuthResponseCode.TERMS_REQUIRED,
            AUTH_MESSAGES.LOGIN.TERMS_REQUIRED,
            language,
          ),
          termsAndConditionToken: result.termsAndConditionToken,
          termsRequired: result.termsRequired,
          terms: result.terms,
        };
      } else {
        return {
          ...buildResponse(
            this.i18nService,
            AuthResponseCode.SUCCESS,
            AUTH_MESSAGES.LOGIN.SESSION_TERMINATED,
            language,
          ),
          token: result.token,
          termsRequired: result.termsRequired,
          userData: result.userData,
          terms: result.terms,
          tokenType: result.tokenType,
          userDetails: result.userDetails?.userDetails,  // Include user details like normal login
          segment: result.userDetails?.segment,           // Include segment like normal login
        };
      }
    } catch (error) {
      this.logger.error('Termination failed', error);
      return buildResponse(
        this.i18nService,
        AuthResponseCode.TOKEN_ERROR,
        AUTH_MESSAGES.LOGIN.TOKEN_ERROR,
        language,
      );
    }
  }

  /**
   * Extract termination information and generate a termination token.
   */
  async extractTerminationInfo(
    portalResponse: any,
    isamResponse: any,
    loginDto: { username: string; company: string },
  ): Promise<ActiveSessionResponse | null> {
    try {
      const terminationStateId = this.stateIdParserService.parseStateIdFromHtml(
        isamResponse.body,
      );
      const portalHeaders = this.extractHeadersAndCookies(portalResponse);
      const isamAuthenticationHeaders =
        this.extractHeadersAndCookies(isamResponse);
      const collectedHeaders = [...portalHeaders, ...isamAuthenticationHeaders];

      // Parse the collected headers into a cookie string
      const parsedCookies = parseSetCookieHeaders(collectedHeaders);

      const terminationToken = await this.jwtTokenService.generateToken(
        parsedCookies,
        terminationStateId,
        {
          username: loginDto.username,
          company: loginDto.company,
        },
      );
      return {
        terminationToken,
        message: 'Active session detected. Termination required before login.',
      };
    } catch (error) {
      this.logger.error('Failed to extract termination info', error);
      return null;
    }
  }

  /**
   * Terminate an active session
   */
  public async terminateSession(
    terminationToken: string,
    language: UserLanguage,
  ): Promise<any> {
    try {
      const decodedToken =
        await this.validateTerminationToken(terminationToken);
      const { stateId, cookies, username, company } = this.extractTokenData(decodedToken);

      if (!stateId) {
        throw new IsamError('Missing state ID in token', 'INVALID_TOKEN', 401);
      }

      const terminateUrl = `${this.isamService.loginBaseUrl}/mga/sps/authsvc?StateId=${stateId}`;
      const formData = new URLSearchParams();
      formData.append('operation', 'verify');
      formData.append('sub', 'true');

      const requestConfig = {
        headers: {
          Accept:
            'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
          'Accept-Encoding': 'gzip, deflate, br, zstd',
          'Accept-Language': 'en-GB,en;q=0.9',
          'Cache-Control': 'no-cache',
          Connection: 'keep-alive',
          'Content-Type': 'application/x-www-form-urlencoded',
          Cookie: cookies,
          Host: this.isamService.loginBaseUrl,
          Origin: this.isamService.loginBaseUrl,
          Pragma: 'no-cache',
          Referer: terminateUrl,
          'Sec-Fetch-Dest': 'document',
          'Sec-Fetch-Mode': 'navigate',
          'Sec-Fetch-Site': 'same-origin',
          'Sec-Fetch-User': '?1',
          'Upgrade-Insecure-Requests': '1',
          'User-Agent':
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
          'sec-ch-ua':
            '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
          'sec-ch-ua-mobile': '?0',
          'sec-ch-ua-platform': '"macOS"',
        },
        maxRedirects: 0,
        validateStatus: (status) => status < 500,
      };

      const terminationPortalResponse = await lastValueFrom(
        this.httpService.post(terminateUrl, formData.toString(), requestConfig),
      );

      return this.processTerminationResponse(
        terminationPortalResponse,
        cookies,
        stateId,
        language,
        username,
        company,
      );
      this.logger.log('Active session terminated successfully');
    } catch (error) {
      return this.handleTerminationError(error);
    }
  }

  /**
   * Process the termination response and generate a new token
   */
  private async processTerminationResponse(
    response,
    cookies,
    stateId,
    language: UserLanguage,
    username?: string,
    company?: string,
  ) {
    const terminationPortalResponseHeaders = response.headers['set-cookie'];
    const isamRestPortalResponse =
      await this.isamService.performRestPortalAuthentication(
        response,
        language
      );
    const isamAuthenticationHeaders =
      isamRestPortalResponse.headers['set-cookie'];

    const collectedHeaders = [
      ...cookies.split(';'),
      ...terminationPortalResponseHeaders,
      ...isamAuthenticationHeaders,
    ];
    const parsedHeaders = parseSetCookieHeaders(collectedHeaders);

    // Parse the response body
    const userData = JSON.parse(isamRestPortalResponse.body);

    // Check if this is a terms acceptance response
    if (userData.mode === 'accept_terms' && userData.objectData?.tandctext) {
      this.logger.log('Terms and conditions acceptance required');

      // Generate JWT token without userId/CIF for terms acceptance
      const jwtToken = await this.jwtTokenService.generateToken(
        parsedHeaders,
        stateId,
      );

      return {
        termsAndConditionToken: jwtToken,
        termsRequired: true,
        terms: userData.objectData.tandctext,
      };
    }

    // Check if this is a token registration step one response
    const otpResponse = await this.otpActivationService.checkAndHandleOtpActivationRequired(
      userData,
      parsedHeaders,
      stateId,
      language,
    );
    if (otpResponse) {
      return {
        otpActivationToken: otpResponse.otpActivationToken,
        otpActivationRequired: true,
        otpActivationStep: otpResponse.otpActivationStep,
        tokenType: otpResponse.tokenType,
        userData: userData,
      };
    } else {
      // normal termination , get the user details .
      // Extract userId and CIF for caching (same as regular login flow)
      try {
        const  userDetails =
          await this.userDetailsService.checkEligibility(parsedHeaders, language);
        const userId = userDetails.userDetails?.userId;
        const cif = userDetails.userDetails?.customerReferences?.at(0)?.name;

        // Generate JWT token with userId and CIF
        const jwtToken = await this.jwtTokenService.generateToken(
          parsedHeaders,
          stateId,
          { userId,
            cif,
            industry: userDetails?.t24CompanyProfile?.industry,
            segment: userDetails?.segment?.id,
            username,
            company,
          },
        );

        // Return standard authentication result
        return {
          token: jwtToken,
          userData: userData,
          userDetails
        };
      } catch (error) {
        // Handle AuthenticationException specifically
        if (error instanceof AuthenticationException) {
          this.logger.warn('User eligibility check failed', error);
          // Return the error response with proper formatting
          return {
            responseCode: error.responseCode,
            message: this.i18nService.translate(error.messageKey, language),
            details: error.details,
          };
        }

        // Log the error for debugging
        this.logger.error('User eligibility check failed with unexpected error', {
          error: error.message,
          stack: error.stack,
        });

        // Re-throw other errors to cascade them up
        throw error;
      }
    }
  }

  /**
   * Validate the termination token
   */
  private async validateTerminationToken(token: string) {
    try {
      const decodedToken =
        await this.jwtTokenService.decodeAndValidateToken(token);
      if (!decodedToken) {
        throw new Error('Token decoded but returned empty result');
      }
      return decodedToken;
    } catch (error) {
      this.logger.error(`Token validation failed: ${error.message}`, {
        errorName: error.name,
        errorType: error.constructor.name,
        errorCode: error.status || error.code,
        stack: error.stack,
      });

      if (error instanceof UnauthorizedException) {
        const errorCode = error.message.includes('expired')
          ? 'TOKEN_EXPIRED'
          : error.message.includes('revoked')
            ? 'TOKEN_REVOKED'
            : 'INVALID_TOKEN';

        throw new IsamError(
          `Invalid termination token: ${error.message}`,
          errorCode,
          401,
          { originalError: error } as any,
        );
      }

      throw new IsamError(
        `Invalid termination token: ${error.message || 'Unknown error'}`,
        'INVALID_TOKEN',
        401,
        error,
      );
    }
  }

  /**
   * Handle termination errors
   */
  private handleTerminationError(error) {
    if (error instanceof IsamError) {
      throw error;
    }
    this.logger.error('Session termination failed', error);
    throw new IsamError(
      'Failed to terminate active session',
      IsamErrorCodes.SESSION_TERMINATION_FAILED,
      400,
      error,
    );
  }

  /**
   * Extract stateId and headers from decoded JWT token
   */
  private extractTokenData(decodedToken: any): {
    stateId: string;
    cookies: string;
    username?: string;
    company?: string;
  } {
    try {
      let stateId: string;
      let cookies: string;
      let username: string;
      let company: string;

      if (typeof decodedToken === 'string') {
        stateId = decodedToken;
      } else if (typeof decodedToken === 'object') {
        stateId = decodedToken.stateId || decodedToken.terminationStateId;
        cookies = decodedToken.cookies || '';
        username = decodedToken.username;
        company = decodedToken.company;
      } else {
        throw new Error('Unexpected token format');
      }

      return { stateId, cookies, username, company };
    } catch (error) {
      this.logger.error('Failed to extract data from token', error);
      throw new IsamError(
        'Invalid token structure',
        'INVALID_TOKEN',
        401,
        error,
      );
    }
  }

  /**
   * Extract headers and cookies from response
   */
  private extractHeadersAndCookies(response: any): string[] {
    return response.headers['set-cookie'] || [];
  }
}
