import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { LoginDto } from '../dto/login.dto';
import { lastValueFrom } from 'rxjs';
import { AxiosRequestConfig } from 'axios';
import * as https from 'node:https';
import { JwtTokenService } from '@/auth/services/jwt-token.service';
import { IsamErrorCodes } from '@/auth/types/isam.types';
import { JSDOM } from 'jsdom';
import { StateIdParserService } from './stateIdParserService';
import { UserDetailsService } from '@/auth/services/user-details.service';
import { I18nLookupService } from '@modules';
import { UserLanguageMap } from '@constants';
import { UserLanguage } from '@enums';

class IsamError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number,
    public details?: any,
  ) {
    super(message);
    this.name = 'IsamError';
  }
}

@Injectable()
export class IsamService {
  readonly baseUrl: string;
  readonly loginBaseUrl: string;
  private readonly logger = new Logger(IsamService.name);
  private readonly agent: https.Agent;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    private jwtTokenService: JwtTokenService,
    private userDetailsService: UserDetailsService,
    private i18nService: I18nLookupService,
  ) {
    this.baseUrl = this.configService.getOrThrow('ISAM_BASE_URL');
    this.loginBaseUrl = this.configService
      .getOrThrow('ISAM_BASE_URL')
      .replace(/\/(FBCCBB|FBCCSIT)$/, '');
    this.agent = new https.Agent({ rejectUnauthorized: false });
  }

  /**
   * Perform authentication request
   */
  async performAuthentication(
    loginDto: LoginDto,
    data: any,
  ): Promise<{
    statusCode: string;
    headers: Record<string, string | string[]>;
    body: string;
  }> {
    try {
      const loginUrl = `${this.loginBaseUrl}/mga/sps/authsvc?StateId=${data.stateId}`;
      const cookies = data.response.headers['set-cookie'].join('');
      const headers = this.getAuthenticationHeaders(cookies);
      const requestConfig = this.getRequestConfig(headers);
      const formData = this.createAuthFormData(loginDto);

      const response = await lastValueFrom(
        this.httpService.post(loginUrl, formData.toString(), requestConfig),
      );

      return this.formatResponse(response);
    } catch (error) {
      this.logger.error('Authentication request failed', error);
      throw new IsamError(
        'Failed to perform authentication request',
        IsamErrorCodes.INVALID_CREDENTIALS,
        401,
      );
    }
  }

  /**
   * Perform REST portal authentication with custom payload
   */
  public async performRestPortalAuthentication(
    isamResponse: any,
    language?: string,
    customPayload?: any,
    customUrl?: string,
  ) {
    if (isamResponse.statusCode != 302 && isamResponse.status != 302) {
      throw new IsamError(
        'Failed to perform authentication request',
        IsamErrorCodes.INVALID_CREDENTIALS,
        401,
      );
    }

    try {
      const loginUrl = customUrl || `${this.baseUrl}/restportal/login/`;
      const cookies = isamResponse.headers['set-cookie']?.join(';') || '';
      const headers = { Cookie: cookies, Connection: 'keep-alive' };
      const requestConfig = this.getRequestConfig(headers);
      const payload = customPayload || this.createRestPortalPayload(undefined, language);

      const response = await lastValueFrom(
        this.httpService.post(loginUrl, payload, requestConfig),
      );

      return this.formatResponse(response);
    } catch (error) {
      this.logger.error('REST Portal authentication failed', error);
      throw new IsamError(
        'Failed to perform authentication request',
        IsamErrorCodes.INVALID_CREDENTIALS,
        401,
      );
    }
  }

  /**
   * Check if response indicates an active session
   */
  public hasActiveSession(html: string): boolean {
    const dom = new JSDOM(html);
    const heading = dom.window.document
      .querySelector('.form-signin-heading')
      ?.textContent?.toLowerCase();

    return heading?.includes('you are already has an active session') ?? false;
  }

  /**
   * Create REST portal payload
   */
  public createRestPortalPayload(
    credentials?: {
      username?: string;
      company?: string;
      password?: string;
    },
    language?: string | UserLanguage
  ) {
    // Map UserLanguage enum to the expected REST portal format
    let userSelectedLanguage = 'en'; // default to English
    
    if (language) {
      if (typeof language === 'string') {
        // If it's a string, check if it's a UserLanguage enum value
        const languageKey = language as UserLanguage;
        userSelectedLanguage = UserLanguageMap[languageKey] || (language.startsWith('ar') ? 'ar' : 'en');
      } else {
        // If it's already a UserLanguage enum
        userSelectedLanguage = UserLanguageMap[language] || 'en';
      }
    }
    
    return {
      userData: {
        username: credentials?.username || 'username',
        company: credentials?.company || 'company',
        userSelectedLanguage: userSelectedLanguage,
      },
      requestData: {
        password: credentials?.password || '******',
        mode: '',
        tandcflag: '',
        newUserName: '',
        newPasswordValue: '',
        newPasswordConfirm: '',
        phone: '',
        email: '',
        nextScreen: '/portal/screen/GTPLoginScreen',
        preferredMode: '',
      },
      vascoData: {
        vasco_primary_key_web: '',
        vasco_serial_number_web: '',
        vasco_mobile_number_web: '',
        vasco_token_web: '',
        vasco_account_name_web: '',
        vasco_finger_print_web: '',
        vasco_OTP_web: '',
      },
    };
  }

  /**
   * Initiate forget process
   * @param username User's username
   * @param company User's company
   * @param stateIdParserService StateIdParserService instance
   * @returns HTML response containing reset options
   */
  async initiateForgetPassword(
    username: string,
    company: string,
    stateIdParserService: StateIdParserService,
  ): Promise<{
    statusCode: string;
    headers: Record<string, string | string[]>;
    body: string;
  }> {
    try {
      const forgetPasswordUrl = `${this.loginBaseUrl}/mga/sps/authsvc?PolicyId=urn:ibm:security:authentication:asf:testForget`;

      // Create request configuration
      const requestConfig = {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          Accept: 'text/html,application/xhtml+xml,application/xml',
        },
        httpsAgent: this.agent,
        maxRedirects: 0,
        validateStatus: (status) => status < 500,
      };

      // Make the initial request to get the forget page
      const response = await lastValueFrom(
        this.httpService.get(forgetPasswordUrl, requestConfig),
      );

      // Extract stateId from the response using StateIdParserService
      const stateId = stateIdParserService.parseStateIdFromHtml(response.data);

      if (!stateId) {
        throw new Error(
          'Failed to extract stateId from forget password response',
        );
      }

      // Prepare form data for forget request
      const formData = new URLSearchParams();
      formData.append('username', username);
      formData.append('company', company);
      formData.append('operation', 'verify');
      formData.append('Submit', 'Login');

      // Submit the forget form
      const submitUrl = `${this.loginBaseUrl}/mga/sps/authsvc?StateId=${stateId}`;
      const submitResponse = await lastValueFrom(
        this.httpService.post(submitUrl, formData.toString(), {
          ...requestConfig,
          headers: {
            ...requestConfig.headers,
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }),
      );

      this.logger.log('Forget password request initiated successfully');

      return this.formatResponse(submitResponse);
    } catch (error) {
      this.logger.error('Forget password request failed', error);
      throw new IsamError(
        'Failed to initiate forget password request',
        IsamErrorCodes.INVALID_CREDENTIALS,
        401,
        error,
      );
    }
  }

  /**
   * Create authentication form data
   */
  private createAuthFormData(loginDto: LoginDto): URLSearchParams {
    const formData = new URLSearchParams();
    formData.append('username', loginDto.username);
    formData.append('password', loginDto.password);
    formData.append('company', loginDto.company);
    formData.append('operation', 'verify');
    formData.append('g-recaptcha-response', '');
    formData.append('Submit', '');
    return formData;
  }

  /**
   * Get authentication headers
   */
  private getAuthenticationHeaders(cookies: string) {
    return {
      Accept:
        'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
      'Accept-Encoding': 'gzip, deflate, br, zstd',
      'Accept-Language': 'en-GB,en-US;q=0.9,en;q=0.8',
      'Cache-Control': 'no-cache',
      Connection: 'keep-alive',
      'Content-Type': 'application/x-www-form-urlencoded',
      Cookie: cookies,
      Host: 'isam-digital-test.hodomain.local:448',
      Origin: 'https://isam-digital-test.hodomain.local:448',
      Pragma: 'no-cache',
      Referer: 'https://isam-digital-test.hodomain.local:448/FBCCBB/portal',
      'Sec-Fetch-Dest': 'document',
      'Sec-Fetch-Mode': 'navigate',
      'Sec-Fetch-Site': 'same-origin',
      'Sec-Fetch-User': '?1',
      'Upgrade-Insecure-Requests': '1',
      'User-Agent':
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      'sec-ch-ua':
        '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
      'sec-ch-ua-mobile': '?0',
      'sec-ch-ua-platform': '"macOS"',
    };
  }

  /**
   * Get request configuration
   */
  private getRequestConfig(headers: any): AxiosRequestConfig {
    return {
      headers,
      httpsAgent: this.agent,
      timeout: 100000,
      maxRedirects: 0,
      validateStatus: null,
    };
  }

  /**
   * Format response
   */
  private formatResponse(response: any) {
    return {
      statusCode: String(response.status),
      headers: response.headers as Record<string, string | string[]>,
      body:
        typeof response.data === 'string'
          ? response.data
          : JSON.stringify(response.data),
    };
  }

  async submitNewPasswordAndTempPassword(
    stateId: string,
    cookies: string,
    smsCode: string,
    newPassword: string,
    flow: string,
  ): Promise<any> {
    try {
      const url = `${this.loginBaseUrl}/mga/sps/authsvc?StateId=${stateId}`;

      // Convert cookies object to cookie header string
      const cookieHeader = Object.entries(cookies)
        .map(([name, value]) => `${name}=${value}`)
        .join('; ');

      let formData: URLSearchParams;
      // Prepare form data for submission
      const SMSFormData = new URLSearchParams();
      SMSFormData.append('operation', 'verify');
      SMSFormData.append('password1', smsCode);
      SMSFormData.append('newPass1', newPassword);
      SMSFormData.append('confPass1', newPassword);
      SMSFormData.append('change', 'change');

      const mailFormData = new URLSearchParams();
      mailFormData.append('operation', 'verify');
      mailFormData.append('password', smsCode);
      mailFormData.append('newPass', newPassword);
      mailFormData.append('confPass', newPassword);
      mailFormData.append('Submit', 'change');

      // determine the for
      formData = flow == 'EMAIL' ? mailFormData : SMSFormData;

      const response = await this.httpService.axiosRef.post(url, formData, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          Cookie: cookieHeader,
        },
        maxRedirects: 0,
        validateStatus: (status) => status < 500, // Accept all responses except server errors
      });

      return {
        body: response.data,
        headers: response.headers,
        status: response.status,
      };
    } catch (error) {
      this.logger.error('Error submitting forget password SMS', error);
      throw error;
    }
  }

  async selectOptionFromISAM(
    stateId: string,
    cookies: string,
    option: string = 'SMS',
  ): Promise<any> {
    try {
      const url = `${this.loginBaseUrl}/mga/sps/authsvc?StateId=${stateId}`;

      // Convert cookies object to cookie header string
      const cookieHeader = Object.entries(cookies)
        .map(([name, value]) => `${name}=${value}`)
        .join('; ');

      // Prepare form data for submission
      const formData = new URLSearchParams();
      formData.append('operation', 'verify');
      formData.append('option', option.toLowerCase());
      formData.append('Submit', 'Login');

      const response = await this.httpService.axiosRef.post(url, formData, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          Cookie: cookieHeader,
          Accept: 'text/html,application/xhtml+xml,application/xml',
        },
        httpsAgent: this.agent,
        maxRedirects: 0,
        validateStatus: (status) => status < 500, // Accept all responses except server errors
      });

      return {
        body: response.data,
        headers: response.headers,
        status: response.status,
      };
    } catch (error) {
      this.logger.error('Error verifying phone number', error);
      throw error;
    }
  }

  /**
   * Send SMS verification code
   *
   * @param stateId The state ID from the previous step
   * @param cookies Cookies from the previous step
   * @returns The response from ISAM
   */
  async sendSmsVerification(stateId: string, cookies: string): Promise<any> {
    try {
      const url = `${this.loginBaseUrl}/mga/sps/authsvc?StateId=${stateId}`;

      // Convert cookies object to cookie header string
      const cookieHeader = Object.entries(cookies)
        .map(([name, value]) => `${name}=${value}`)
        .join('; ');

      // Prepare form data for submission
      const formData = new URLSearchParams();
      formData.append('operation', 'verify');
      formData.append('send', 'true');

      const response = await this.httpService.axiosRef.post(url, formData, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          Cookie: cookieHeader,
          Accept: 'text/html,application/xhtml+xml,application/xml',
        },
        httpsAgent: this.agent,
        maxRedirects: 0,
        validateStatus: (status) => status < 500, // Accept all responses except server errors
      });

      return {
        body: response.data,
        headers: response.headers,
        status: response.status,
      };
    } catch (error) {
      this.logger.error('Error sending SMS verification', error);
      throw error;
    }
  }

  /**
   * Send email verification code
   *
   * @param stateId The state ID from the previous step
   * @param cookies Cookies from the previous step
   * @param email email enter by the user.
   * @returns The response from ISAM
   */
  async sendEmailVerification(
    stateId: string,
    cookies: string,
    email: string,
  ): Promise<any> {
    try {
      const url = `${this.loginBaseUrl}/mga/sps/authsvc?StateId=${stateId}`;

      // Convert cookies object to cookie header string
      const cookieHeader = Object.entries(cookies)
        .map(([name, value]) => `${name}=${value}`)
        .join('; ');

      // Prepare form data for submission
      const formData = new URLSearchParams();
      formData.append('operation', 'verify');
      formData.append('send', 'true');
      formData.append('email', email);

      const response = await this.httpService.axiosRef.post(url, formData, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          Cookie: cookieHeader,
          Accept: 'text/html,application/xhtml+xml,application/xml',
        },
        httpsAgent: this.agent,
        maxRedirects: 0,
        validateStatus: (status) => status < 500, // Accept all responses except server errors
      });

      return {
        body: response.data,
        headers: response.headers,
        status: response.status,
      };
    } catch (error) {
      this.logger.error('Error sending Email verification', error);
      throw error;
    }
  }

  /**
   * Verify OTP code for forget credentials flow
   *
   * @param stateId The state ID from the previous step
   * @param cookies Cookies from the previous step
   * @param otpCode OTP code from authenticator app
   * @returns The response from ISAM
   */
  async verifyOtpForForgetPassword(
    stateId: string,
    cookies: string,
    otpCode: string,
  ): Promise<any> {
    try {
      const url = `${this.loginBaseUrl}/mga/sps/authsvc?StateId=${stateId}`;

      // Convert cookies object to cookie header string
      const cookieHeader = Object.entries(cookies)
        .map(([name, value]) => `${name}=${value}`)
        .join('; ');

      // Prepare form data for OTP verification
      const formData = new URLSearchParams();
      formData.append('operation', 'verify');
      formData.append('passOTP', otpCode);
      formData.append('g-recaptcha-response', '');
      formData.append('Submit', 'Login');

      this.logger.log(`Verifying OTP for forget password - StateId: ${stateId}`);
      this.logger.log(`Form data: ${formData.toString()}`);

      const response = await this.httpService.axiosRef.post(url, formData, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          Cookie: cookieHeader,
          Accept: 'text/html,application/xhtml+xml,application/xml',
        },
        httpsAgent: this.agent,
        maxRedirects: 0,
        validateStatus: (status) => status < 500, // Accept all responses except server errors
      });

      this.logger.log(`OTP verification response status: ${response.status}`);

      return {
        body: response.data,
        headers: response.headers,
        status: response.status,
      };
    } catch (error) {
      this.logger.error('Error verifying OTP for forget password', error);
      throw error;
    }
  }

  /**
   * Submit new credentials for OTP forget credentials flow (without temp credentials)
   *
   * @param stateId The state ID from the previous step
   * @param cookies Cookies from the previous step
   * @returns The response from ISAM
   */
  async submitOtpPasswordReset(
    stateId: string,
    cookies: string,
    newPassword: string,
  ): Promise<any> {
    try {
      const url = `${this.loginBaseUrl}/mga/sps/authsvc?StateId=${stateId}`;

      // Convert cookies object to cookie header string
      const cookieHeader = Object.entries(cookies)
        .map(([name, value]) => `${name}=${value}`)
        .join('; ');

      // Prepare form data for reset (OTP flow - no temp)
      const formData = new URLSearchParams();
      formData.append('newPass', newPassword);
      formData.append('operation', 'verify');
      formData.append('confPass', newPassword);
      formData.append('Submit', 'Login');

      this.logger.log(`Submitting OTP password reset - StateId: ${stateId}`);
      this.logger.log(`Form data: ${formData.toString()}`);

      const response = await this.httpService.axiosRef.post(url, formData, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          Cookie: cookieHeader,
        },
        maxRedirects: 0,
        validateStatus: (status) => status < 500, // Accept all responses except server errors
      });

      this.logger.log(`OTP password reset response status: ${response.status}`);

      return {
        body: response.data,
        headers: response.headers,
        status: response.status,
      };
    } catch (error) {
      this.logger.error('Error submitting OTP password reset', error);
      throw error;
    }
  }
}
