import { Injectable } from '@nestjs/common';
import { I18nLookupService } from '@modules';
import { UserLanguage } from '@enums';

@Injectable()
export class AuthI18nService {
  constructor(private readonly i18nService: I18nLookupService) {}

  translate(key: string, language: UserLanguage = UserLanguage['en-US'], values?: Record<string, string | number>): string {
    return this.i18nService.translate(key, language, values);
  }
}