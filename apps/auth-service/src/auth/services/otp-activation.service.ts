import {
  Injectable,
  Logger,
  Inject,
  forwardRef,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { I18nLookupService, RedisCacheService } from '@modules';
import { JwtTokenService } from './jwt-token.service';
import { StateIdParserService } from './stateIdParserService';
import { AuthResponseDto } from '../dto/auth-response.dto';
import { AuthResponseCode } from '../types/auth-response.types';
import { AUTH_MESSAGES } from '../constants/auth-messages.constants';
import { buildResponse } from '../utils/response.utils';
import { UserLanguage } from '@enums';
import { lastValueFrom } from 'rxjs';
import { parseSetCookieHeaders } from '../utils/cookie.utils';
import {
  OtpActivationStep1Dto,
  OtpActivationStep2Dto,
  OtpActivationStep3Dto,
  HardTokenActivationStep1Dto,
  HardTokenActivationStep2Dto,
} from '@/auth/dto/otp-activation.dto';
import { AuthenticationException } from '@/auth/exceptions/authentication.exception';
import { CurrentUser } from '@types';
import { UserDetailsService } from '@/auth/services/user-details.service';
import { AuthService } from './auth.service';

@Injectable()
export class OtpActivationService {
  private readonly logger = new Logger(OtpActivationService.name);
  private readonly loginBaseUrl: string;
  private readonly baseUrl: string;
  private readonly agent: any;
  private readonly REDIS_TOKEN_TTL: number;

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
    private readonly i18nService: I18nLookupService,
    private readonly redisCacheService: RedisCacheService,
    private readonly jwtTokenService: JwtTokenService,
    private readonly stateIdParserService: StateIdParserService,
    private readonly userDetailsService: UserDetailsService,
    @Inject(forwardRef(() => AuthService))
    private readonly authService: AuthService,
  ) {
    this.REDIS_TOKEN_TTL = this.configService.get<number>(
      'REDIS_TOKEN_TTL',
      600, // Default 10 minutes in seconds
    );
    this.loginBaseUrl = this.configService
      .getOrThrow('ISAM_BASE_URL')
      .replace(/\/(FBCCBB|FBCCSIT)$/, '');
    this.agent = new (require('https').Agent)({ rejectUnauthorized: false });

    this.baseUrl = this.configService.getOrThrow('ISAM_BASE_URL');
  }

  /**
   * Check if response requires OTP activation and build appropriate response
   */
  async checkAndHandleOtpActivationRequired(
    responseData: any,
    parsedCookies: any,
    stateId: string | null,
    language: UserLanguage,
  ): Promise<AuthResponseDto | null> {
    if (
      responseData.mode === 'tokenregisterstepone' ||
      responseData.mode === 'tokenregistersteptwo' ||
      responseData.mode === 'tokenregisterstepthree'
    ) {
      this.logger.log('Token registration required');

      // build array of attributes to be stored in redis, from whatever request we are , collect data as you find
      const attributes = this.buildAttributes(responseData);
      // Generate JWT token without userId/CIF for OTP activation flow
      const otpActivationToken = await this.jwtTokenService.generateToken(
        parsedCookies,
        stateId,
        { attributes: attributes },
      );

      return {
        responseCode: AuthResponseCode.OTP_ACTIVATION_REQUIRED,
        title: this.i18nService.translate(
          AUTH_MESSAGES.LOGIN.OTP_ACTIVATION_REQUIRED + '.title',
          language,
        ),
        message: this.i18nService.translate(
          AUTH_MESSAGES.LOGIN.OTP_ACTIVATION_REQUIRED + '.body',
          language,
        ),
        otpActivationToken: otpActivationToken,
        tokenType: responseData.objectData?.tokentype || 'SOFT_TOKEN',
        userData: responseData,
        otpActivationStep: responseData.mode,
        attributes: attributes, // Include the merged attributes in the response
      };
    }

    return null;
  }

  /**
   * Complete Soft Token Activation Step 1 - Submit national ID and phone number
   */
  async completeSoftTokenStep1(
    otpStep1Dto: OtpActivationStep1Dto,
    otpActivationToken: string,
    language: UserLanguage = UserLanguage['en-US'],
  ): Promise<AuthResponseDto> {
    try {
      if (!otpActivationToken) {
        return buildResponse(
          this.i18nService,
          AuthResponseCode.TOKEN_ERROR,
          AUTH_MESSAGES.TOKEN.MISSING,
          language,
        );
      }

      // Get token data
      const tokenData =
        await this.jwtTokenService.getTokenData(otpActivationToken);
      if (!tokenData) {
        return buildResponse(
          this.i18nService,
          AuthResponseCode.TOKEN_ERROR,
          AUTH_MESSAGES.TOKEN.INVALID,
          language,
        );
      }

      // Prepare request payload
      const payload = {
        userData: {
          userSelectedLanguage: language === UserLanguage['ar-EG'] ? 'ar' : 'en',
        },
        requestData: {
          mode: 'tokenregisterstepone',
          tandcflag: '',
          newUserName: '',
          newPasswordValue: '',
          newPasswordConfirm: '',
          phone: '',
          email: '',
          nextScreen: '',
          preferredMode: '',
        },
        vascoData: {
          vasco_primary_key_web: otpStep1Dto.nationalId,
          vasco_serial_number_web: '',
          vasco_mobile_number_web: otpStep1Dto.phoneNumber,
          vasco_token_web: 'SOFT_TOKEN',
          vasco_account_name_web: '',
          vasco_finger_print_web: '',
          vasco_OTP_web: '',
        },
      };

      // Make request to ISAM
      const response = await this.makeRequest(payload, tokenData.cookies);

      // Parse response
      const responseData = JSON.parse(response.body);
      console.log('responseData of step1 ', responseData);
      // Extract new cookies from response
      const responseCookies = this.extractHeadersAndCookies(response);
      // const parsedCookies = parseSetCookieHeaders(responseCookies);

      // build array of attributes to be stored in redis.
      const attributes = this.buildAttributes(responseData);

      // merge token data attributes with the new build attributes and override duplicate
      const mergedAttributes = this.mergeAttributes(
        tokenData.attributes,
        attributes,
      );

      // keep the old cookies for now , as the response cookies not valuable and we need to keep track of the first cookies we got
      // Update Redis with new cookies, in case of success of failure
      await this.updateRedisCache(
        tokenData.jti,
        tokenData.cookies,
        tokenData.stateId,
        mergedAttributes,
      );

      // Check if successful
      if (
        responseData.response === 'success' &&
        responseData.mode === 'tokenregistersteptwo'
      ) {
        return {
          responseCode: AuthResponseCode.SUCCESS,
          title: this.i18nService.translate(
            AUTH_MESSAGES.LOGIN.OTP_ACTIVATION_REQUIRED + '.title',
            language,
          ),
          message: this.i18nService.translate(
            AUTH_MESSAGES.LOGIN.OTP_ACTIVATION_REQUIRED + '.body',
            language,
          ),
          tokenType: responseData.objectData?.tokentype || 'SOFT_TOKEN',
          userData: responseData,
          otpActivationStep: responseData.mode,
        };
      }

      // Handle failure
      return buildResponse(
        this.i18nService,
        AuthResponseCode.SYSTEM_ERROR,
        AUTH_MESSAGES.LOGIN.OTP_ACTIVATION_FAILED,
        language,
      );
    } catch (error) {
      this.logger.error('OTP Activation Step 1 failed', error);
      return buildResponse(
        this.i18nService,
        AuthResponseCode.SYSTEM_ERROR,
        AUTH_MESSAGES.LOGIN.OTP_ACTIVATION_FAILED,
        language,
      );
    }
  }

  /**
   * Complete Hard Token Activation Step 1 - Submit hard token OTP data
   */
  async completeHardTokenStep1(
    hardTokenDto: HardTokenActivationStep1Dto,
    otpActivationToken: string,
    language: UserLanguage = UserLanguage['en-US'],
  ): Promise<AuthResponseDto> {
    try {
      if (!otpActivationToken) {
        return buildResponse(
          this.i18nService,
          AuthResponseCode.TOKEN_ERROR,
          AUTH_MESSAGES.TOKEN.MISSING,
          language,
        );
      }

      // Get token data
      const tokenData =
        await this.jwtTokenService.getTokenData(otpActivationToken);
      if (!tokenData) {
        return buildResponse(
          this.i18nService,
          AuthResponseCode.TOKEN_ERROR,
          AUTH_MESSAGES.TOKEN.INVALID,
          language,
        );
      }

      // Prepare request payload for hard token
      const payload = {
        userData: {
          userSelectedLanguage: language === UserLanguage['ar-EG'] ? 'ar' : 'en',
        },
        requestData: {
          mode: 'tokenregisterstepone',
          tandcflag: '',
          newUserName: '',
          newPasswordValue: '',
          newPasswordConfirm: '',
          phone: '',
          email: '',
          nextScreen: '',
          preferredMode: '',
        },
        vascoData: {
          vasco_primary_key_web: hardTokenDto.nationalId,
          vasco_serial_number_web: hardTokenDto.serialNumber,
          vasco_mobile_number_web: hardTokenDto.phoneNumber,
          vasco_token_web: 'HARD_TOKEN',
          vasco_account_name_web: '',
          vasco_finger_print_web: '',
          vasco_OTP_web: hardTokenDto.otpCode,
        },
      };

      // Make request to ISAM
      const response = await this.makeRequest(payload, tokenData.cookies);

      // Parse response
      const responseData = JSON.parse(response.body);
      this.logger.log('responseData of hard token step1:', responseData);

      // Extract new cookies from response
      const responseCookies = this.extractHeadersAndCookies(response);

      // build array of attributes to be stored in redis.
      const attributes = this.buildAttributes(responseData);

      // merge token data attributes with the new build attributes and override duplicate
      const mergedAttributes = this.mergeAttributes(
        tokenData.attributes,
        attributes,
      );

      // Update Redis with new cookies, in case of success or failure
      await this.updateRedisCache(
        tokenData.jti,
        tokenData.cookies,
        tokenData.stateId,
        mergedAttributes,
      );

      // Check if successful and moved to step 2
      if (
        responseData.response === 'success' &&
        responseData.mode === 'tokenregistersteptwo'
      ) {
        return {
          responseCode: AuthResponseCode.SUCCESS,
          title: this.i18nService.translate(
            AUTH_MESSAGES.LOGIN.OTP_ACTIVATION_REQUIRED + '.title',
            language,
          ),
          message: this.i18nService.translate(
            AUTH_MESSAGES.LOGIN.OTP_ACTIVATION_REQUIRED + '.body',
            language,
          ),
          tokenType: responseData.objectData?.tokentype || 'HARD_TOKEN',
          userData: responseData,
          otpActivationStep: responseData.mode,
        };
      }

      // Handle failure
      return buildResponse(
        this.i18nService,
        AuthResponseCode.SYSTEM_ERROR,
        AUTH_MESSAGES.LOGIN.OTP_ACTIVATION_FAILED,
        language,
      );
    } catch (error) {
      this.logger.error('Hard Token Activation Step 1 failed', error);
      return buildResponse(
        this.i18nService,
        AuthResponseCode.SYSTEM_ERROR,
        AUTH_MESSAGES.LOGIN.OTP_ACTIVATION_FAILED,
        language,
      );
    }
  }

  /**
   * Complete Hard Token Activation Step 2 - Submit hard token OTP code
   */
  async completeHardTokenStep2(
    hardTokenStep2Dto: HardTokenActivationStep2Dto,
    otpActivationToken: string,
    language: UserLanguage = UserLanguage['en-US'],
  ): Promise<AuthResponseDto> {
    try {
      if (!otpActivationToken) {
        return buildResponse(
          this.i18nService,
          AuthResponseCode.TOKEN_ERROR,
          AUTH_MESSAGES.TOKEN.MISSING,
          language,
        );
      }

      // Get token data
      const tokenData =
        await this.jwtTokenService.getTokenData(otpActivationToken);
      if (!tokenData) {
        return buildResponse(
          this.i18nService,
          AuthResponseCode.TOKEN_ERROR,
          AUTH_MESSAGES.TOKEN.INVALID,
          language,
        );
      }

      // Prepare request payload for hard token step 2
      const payload = {
        userData: {
          userSelectedLanguage: language === UserLanguage['ar-EG'] ? 'ar' : 'en',
        },
        requestData: {
          mode: 'tokenregistersteptwo',
          tandcflag: '',
          newUserName: '',
          newPasswordValue: '',
          newPasswordConfirm: '',
          phone: '',
          email: '',
          nextScreen: '',
          preferredMode: '',
        },
        vascoData: {
          vasco_primary_key_web: tokenData.attributes?.nationalId,
          vasco_serial_number_web: tokenData.attributes?.serialNumber || '',
          vasco_mobile_number_web: tokenData.attributes?.mobileNumber,
          vasco_token_web: 'HARD_TOKEN',
          vasco_account_name_web: tokenData.attributes?.accountName,
          vasco_finger_print_web: '',
          vasco_OTP_web: hardTokenStep2Dto.otpCode,
        },
      };

      // Make request to ISAM
      const response = await this.makeRequest(payload, tokenData.cookies);

      // Parse response
      const responseData = JSON.parse(response.body);
      this.logger.log('responseData of hard token step2:', responseData);

      // Extract new cookies from response
      const responseCookies = this.extractHeadersAndCookies(response);

      // build array of attributes to be stored in redis.
      const attributes = this.buildAttributes(responseData);

      // merge token data attributes with the new build attributes and override duplicate
      const mergedAttributes = this.mergeAttributes(
        tokenData.attributes,
        attributes,
      );

      // Update Redis with new cookies, in case of success or failure
      await this.updateRedisCache(
        tokenData.jti,
        tokenData.cookies,
        tokenData.stateId,
        mergedAttributes,
      );

      // Check if successful and hard token activation is complete
      if (responseData.response === 'success' && responseData.mode === '') {
        this.logger.log(
          'Hard token activation step 2 completed successfully - proceeding with full auth flow',
        );

        // Extract and parse cookies from response (same as normal login flow)
        const responseCookies = this.extractHeadersAndCookies(response);
        this.logger.log(
          'Extracted cookies from hard token activation step 2 response',
        );
        this.logger.log('responseData', responseData);

        // Merge existing cookies from tokenData with new cookies from step 2 response
        const existingCookiesArray = tokenData.cookies ? tokenData.cookies.split(';').map(c => c.trim()) : [];
        const allCookies = [...existingCookiesArray, ...responseCookies];
        const mergedParsedCookies = parseSetCookieHeaders(allCookies);
        
        this.logger.log(
          'Merged existing ISAM cookies with new step 2 response cookies for user details check',
        );

        // Build and merge attributes for caching
        const attributes = this.buildAttributes(responseData);
        const finalMergedAttributes = this.mergeAttributes(
          tokenData.attributes,
          attributes,
        );
        await this.updateRedisCache(
          tokenData.jti,
          mergedParsedCookies,
          tokenData.stateId,
          finalMergedAttributes,
        );

        this.logger.log(
          'Hard token activation completed - following same flow as normal login',
        );

        // Follow the same flow as normal login: check user details and generate full auth response
        // Use merged cookies that include both existing ISAM cookies and new step 2 cookies
        return await this.finalizeHardTokenActivation(
          language,
          mergedParsedCookies,
          responseData,
        );
      }

      // Handle failure
      return buildResponse(
        this.i18nService,
        AuthResponseCode.SYSTEM_ERROR,
        AUTH_MESSAGES.LOGIN.OTP_ACTIVATION_FAILED,
        language,
      );
    } catch (error) {
      this.logger.error('Hard Token Activation Step 2 failed', error);
      return buildResponse(
        this.i18nService,
        AuthResponseCode.SYSTEM_ERROR,
        AUTH_MESSAGES.LOGIN.OTP_ACTIVATION_FAILED,
        language,
      );
    }
  }

  /**
   * Complete Soft Token Activation Step 2 - Submit the verification token
   */
  async completeSoftTokenStep2(
    otpActivationStep2Dto: OtpActivationStep2Dto,
    otpActivationToken: string,
    language: UserLanguage = UserLanguage['en-US'],
  ): Promise<AuthResponseDto> {
    try {
      if (!otpActivationToken) {
        return buildResponse(
          this.i18nService,
          AuthResponseCode.TOKEN_ERROR,
          AUTH_MESSAGES.TOKEN.MISSING,
          language,
        );
      }

      // Get token data
      const tokenData =
        await this.jwtTokenService.getTokenData(otpActivationToken);
      if (!tokenData) {
        return buildResponse(
          this.i18nService,
          AuthResponseCode.TOKEN_ERROR,
          AUTH_MESSAGES.TOKEN.INVALID,
          language,
        );
      }

      // Prepare request payload
      const payload = {
        userData: {
          userSelectedLanguage: language === UserLanguage['ar-EG'] ? 'ar' : 'en',
        },
        requestData: {
          mode: 'tokenregistersteptwo',
          tandcflag: '',
          newUserName: '',
          newPasswordValue: '',
          newPasswordConfirm: '',
          phone: '',
          email: '',
          nextScreen: '',
          preferredMode: '',
        },
        vascoData: {
          vasco_primary_key_web: tokenData.attributes?.nationalId,
          vasco_serial_number_web: tokenData.attributes?.serialNumber || '',
          vasco_mobile_number_web: tokenData.attributes?.mobileNumber,
          vasco_token_web: tokenData.attributes?.serialNumber ? 'HARD_TOKEN' : 'SOFT_TOKEN',
          vasco_account_name_web: tokenData.attributes?.accountName,
          vasco_finger_print_web: otpActivationStep2Dto.verificationToken,
          vasco_OTP_web: '',
        },
      };

      // Make request to ISAM
      const response = await this.makeRequest(payload, tokenData.cookies);

      // Parse response
      const responseData = JSON.parse(response.body);

      // Extract new cookies from response
      const responseCookies = this.extractHeadersAndCookies(response);
      const parsedCookies = parseSetCookieHeaders(responseCookies);

      // build array of attributes to be stored in redis.
      const attributes = this.buildAttributes(responseData);

      // merge token data attributes with the new build attributes and override duplicate
      const mergedAttributes = this.mergeAttributes(
        tokenData.attributes,
        attributes,
      );

      // Update Redis with new cookies
      await this.updateRedisCache(
        tokenData.jti,
        tokenData.cookies,
        tokenData.stateId,
        mergedAttributes,
      );

      // Check if successful
      if (
        responseData.response === 'success' &&
        responseData.mode === 'tokenregisterstepthree'
      ) {
        return {
          responseCode: AuthResponseCode.SUCCESS,
          title: this.i18nService.translate(
            AUTH_MESSAGES.LOGIN.OTP_ACTIVATION_REQUIRED + '.title',
            language,
          ),
          message: this.i18nService.translate(
            AUTH_MESSAGES.LOGIN.OTP_ACTIVATION_REQUIRED + '.body',
            language,
          ),
          tokenType: responseData.objectData?.tokentype || 'SOFT_TOKEN',
          userData: responseData,
          otpActivationStep: responseData.mode,
          attributes: mergedAttributes, // Include the merged attributes in the response
        };
      }

      // Handle failure
      return buildResponse(
        this.i18nService,
        AuthResponseCode.SYSTEM_ERROR,
        AUTH_MESSAGES.LOGIN.OTP_ACTIVATION_FAILED,
        language,
      );
    } catch (error) {
      this.logger.error('OTP Activation Step 2 failed', error);
      return buildResponse(
        this.i18nService,
        AuthResponseCode.SYSTEM_ERROR,
        AUTH_MESSAGES.LOGIN.OTP_ACTIVATION_FAILED,
        language,
      );
    }
  }

  /**
   * Complete Soft Token Activation Step 3 - Submit OTP from the APP
   */
  async completeSoftTokenStep3(
    otpActivationStep3Dto: OtpActivationStep3Dto,
    otpActivationToken: string,
    language: UserLanguage = UserLanguage['en-US'],
    updateIsamOnTimeout: boolean = true,
  ): Promise<AuthResponseDto> {
    try {
      // Validate token
      const tokenValidationResult = await this.validateOtpActivationToken(
        otpActivationToken,
        language,
      );
      if (tokenValidationResult) {
        return tokenValidationResult;
      }

      // Get token data
      const tokenData =
        await this.jwtTokenService.getTokenData(otpActivationToken);
      if (!tokenData) {
        return buildResponse(
          this.i18nService,
          AuthResponseCode.TOKEN_ERROR,
          AUTH_MESSAGES.TOKEN.INVALID,
          language,
        );
      }

      // Prepare and make ISAM request
      const payload = this.buildStep3Payload(otpActivationStep3Dto, tokenData, language);
      const response = await this.makeRequest(payload, tokenData.cookies);

      // Handle different response scenarios
      if (this.isTimeoutResponse(response.body)) {
        return await this.handleTimeoutResponse(
          tokenData,
          language,
          updateIsamOnTimeout,
        );
      }

      return await this.handleSuccessResponse(response, tokenData, language);
    } catch (error) {
      this.logger.error('OTP Activation Step 3 failed', error);
      return buildResponse(
        this.i18nService,
        AuthResponseCode.SYSTEM_ERROR,
        AUTH_MESSAGES.LOGIN.OTP_ACTIVATION_FAILED,
        language,
      );
    }
  }

  /**
   * Validate OTP activation token
   */
  private async validateOtpActivationToken(
    token: string,
    language: UserLanguage,
  ): Promise<AuthResponseDto | null> {
    if (!token) {
      return buildResponse(
        this.i18nService,
        AuthResponseCode.TOKEN_ERROR,
        AUTH_MESSAGES.TOKEN.MISSING,
        language,
      );
    }
    return null;
  }

  /**
   * Build payload for step 3 request
   */
  private buildStep3Payload(dto: OtpActivationStep3Dto, tokenData: any, language?: UserLanguage): any {
    return {
      userData: {
        userSelectedLanguage: language === UserLanguage['ar-EG'] ? 'ar' : 'en',
      },
      requestData: {
        mode: 'tokenregisterstepthree',
        tandcflag: '',
        newUserName: '',
        newPasswordValue: '',
        newPasswordConfirm: '',
        phone: '',
        email: '',
        nextScreen: '',
        preferredMode: '',
      },
      vascoData: {
        vasco_primary_key_web: tokenData.attributes?.nationalId,
        vasco_serial_number_web: '',
        vasco_mobile_number_web: tokenData.attributes?.mobileNumber,
        vasco_token_web: '',
        vasco_account_name_web: tokenData.attributes?.accountName,
        vasco_finger_print_web: tokenData.attributes.verificationToken,
        vasco_OTP_web: dto.otpCode,
      },
    };
  }

  /**
   * Check if response indicates a timeout
   */
  private isTimeoutResponse(responseBody: string): boolean {
    return (
      responseBody.includes('<html>') ||
      responseBody.includes('Gateway Time-out') ||
      responseBody.includes('504')
    );
  }

  /**
   * Handle timeout response - treat as success and update OTP flag
   */
  private async handleTimeoutResponse(
    tokenData: any,
    language: UserLanguage,
    updateIsamOnTimeout: boolean = true,
  ): Promise<AuthResponseDto> {
    this.logger.log('Gateway timeout detected, treating as success');

    if (updateIsamOnTimeout) {
      // Extract company ID and user from account name (format: company.username)
      const accountName = tokenData.attributes?.accountName || '';
      const [companyPart, userPart] = accountName.split('.');

      // Update OTP flag in ISAM
      const otpUpdateSuccess = await this.updateOtpFlagInIsam(
        companyPart,
        userPart,
      );
      if (!otpUpdateSuccess) {
        this.logger.error('Failed to update OTP flag in ISAM after timeout');
        return buildResponse(
          this.i18nService,
          AuthResponseCode.SYSTEM_ERROR,
          AUTH_MESSAGES.LOGIN.OTP_ACTIVATION_FAILED,
          language,
        );
      }

      this.logger.log(
        `Successfully updated OTP flag in ISAM for user: ${userPart} (company: ${companyPart})`,
      );
    } else {
      this.logger.log(
        'Skipping ISAM OTP flag update as updateIsamOnTimeout is false',
      );
    }

    // Skip user details check due to invalid cookies in timeout scenario
    // Return success response without token - user will need to re-login
    this.logger.log(
      'Skipping user details check due to timeout - cookies are not valid',
    );

    return {
      responseCode: AuthResponseCode.SUCCESS,
      title: this.i18nService.translate(
        AUTH_MESSAGES.LOGIN.OTP_ACTIVATION_SUCCESS + '.title',
        language,
      ),
      message: this.i18nService.translate(
        AUTH_MESSAGES.LOGIN.OTP_ACTIVATION_SUCCESS + '.body',
        language,
      ),
      // No token provided - user needs to re-login
      userData: {},
    };
  }

  /**
   * Handle successful response from ISAM (no timeout detected)
   * Parse cookies, build token, check user details same as normal login
   */
  private async handleSuccessResponse(
    response: any,
    tokenData: any,
    language: UserLanguage,
  ): Promise<AuthResponseDto> {
    this.logger.log('No timeout detected, handling normal success response');

    // Parse response
    let responseData;
    try {
      responseData = JSON.parse(response.body);
      this.logger.log('Successfully parsed OTP activation response body');
    } catch (parseError) {
      this.logger.error('Failed to parse response body', {
        body: response.body,
        error: parseError,
      });
      return buildResponse(
        this.i18nService,
        AuthResponseCode.SYSTEM_ERROR,
        AUTH_MESSAGES.LOGIN.OTP_ACTIVATION_FAILED,
        language,
      );
    }

    // Extract and parse cookies from response (same as normal login flow)
    const responseCookies = this.extractHeadersAndCookies(response);
    this.logger.log(
      'Extracted cookies from OTP activation step 3 response',
    );
    this.logger.log('responseData', responseData);

    // Merge existing cookies from tokenData with new cookies from step 3 response
    const existingCookiesArray = tokenData.cookies ? tokenData.cookies.split(';').map(c => c.trim()) : [];
    const allCookies = [...existingCookiesArray, ...responseCookies];
    const mergedParsedCookies = parseSetCookieHeaders(allCookies);
    
    this.logger.log(
      'Merged existing ISAM cookies with new step 3 response cookies for user details check',
    );

    // Build and merge attributes for caching
    const attributes = this.buildAttributes(responseData);
    const mergedAttributes = this.mergeAttributes(
      tokenData.attributes,
      attributes,
    );
    await this.updateRedisCache(
      tokenData.jti,
      mergedParsedCookies,
      tokenData.stateId,
      mergedAttributes,
    );

    // Check if the OTP activation was successful
    if (responseData.response === 'success') {
      this.logger.log(
        'OTP activation successful, proceeding with user details check and token generation',
      );

      // Follow the same flow as normal login: check user details and generate full auth response
      // Use merged cookies that include both existing ISAM cookies and new step 3 cookies
      return await this.finalizeOtpActivation(
        language,
        mergedParsedCookies,
        responseData,
      );
    }

    // Handle OTP activation failure
    this.logger.warn('OTP activation failed', {
      response: responseData.response,
      userData: responseData,
    });
    return buildResponse(
      this.i18nService,
      AuthResponseCode.SYSTEM_ERROR,
      AUTH_MESSAGES.LOGIN.OTP_ACTIVATION_FAILED,
      language,
    );
  }

  /**
   * Finalize OTP activation by checking user details and generating access token
   * This follows the same flow as normal login after successful OTP activation
   */
  private async finalizeOtpActivation(
    language: UserLanguage,
    cookies: string,
    userData: any,
  ): Promise<AuthResponseDto> {
    this.logger.log(
      'Finalizing OTP activation - checking user details and generating access token',
    );

    try {
      // Follow the same flow as normal login: check user details and generate full auth response
      const authResult = await this.checkUserDetails(language, cookies, userData);
      
      // If authResult has a responseCode, it means an error occurred (eligibility failed)
      if ('responseCode' in authResult && authResult.responseCode) {
        this.logger.warn('User eligibility check failed after OTP activation', {
          responseCode: authResult.responseCode,
        });
        
        // Perform logout to cleanup session
        try {
          const mockUser: CurrentUser = { cookies: cookies };
          await this.authService.logout(mockUser, language);
          this.logger.log('Successfully performed logout after eligibility check failure');
        } catch (logoutError) {
          this.logger.error('Failed to perform logout after eligibility check failure', logoutError);
        }
        
        // Return properly typed error response (without details since AuthResponseDto doesn't have it)
        return {
          responseCode: authResult.responseCode,
          title: authResult.title,
          message: authResult.message,
        };
      }
      
      // Success case - authResult is the success type, so we can safely access its properties
      this.logger.log('OTP activation completed successfully with token generation');
      return {
        responseCode: AuthResponseCode.SUCCESS,
        title: this.i18nService.translate(
          AUTH_MESSAGES.LOGIN.OTP_ACTIVATION_SUCCESS + '.title',
          language,
        ),
        message: this.i18nService.translate(
          AUTH_MESSAGES.LOGIN.OTP_ACTIVATION_SUCCESS + '.body',
          language,
        ),
      ...authResult
      };
      
    } catch (error) {
      this.logger.error('Failed to finalize OTP activation', error);
      
      // Fallback: perform logout and return success without token (legacy behavior)
      try {
        const mockUser: CurrentUser = { cookies: cookies };
        await this.authService.logout(mockUser, language);
        this.logger.log('Performed fallback logout after error');
      } catch (logoutError) {
        this.logger.error('Failed to perform fallback logout', logoutError);
      }
      
      // Return success without token as fallback
      return {
        responseCode: AuthResponseCode.SUCCESS,
        title: this.i18nService.translate(
          AUTH_MESSAGES.LOGIN.OTP_ACTIVATION_SUCCESS + '.title',
          language,
        ),
        message: this.i18nService.translate(
          AUTH_MESSAGES.LOGIN.OTP_ACTIVATION_SUCCESS + '.body',
          language,
        ),
        // No token provided in error case - user needs to re-login
        userData: {},
      };
    }
  }

  /**
   * Finalize Hard Token activation by checking user details and generating access token
   * This follows the same flow as normal login after successful hard token activation
   */
  private async finalizeHardTokenActivation(
    language: UserLanguage,
    cookies: string,
    userData: any,
  ): Promise<AuthResponseDto> {
    this.logger.log(
      'Finalizing Hard Token activation - checking user details and generating access token',
    );

    try {
      // Follow the same flow as normal login: check user details and generate full auth response
      const authResult = await this.checkUserDetails(language, cookies, userData);
      
      // If authResult has a responseCode, it means an error occurred (eligibility failed)
      if ('responseCode' in authResult && authResult.responseCode) {
        this.logger.warn('User eligibility check failed after Hard Token activation', {
          responseCode: authResult.responseCode,
        });
        
        // Perform logout to cleanup session
        try {
          const mockUser: CurrentUser = { cookies: cookies };
          await this.authService.logout(mockUser, language);
          this.logger.log('Successfully performed logout after eligibility check failure');
        } catch (logoutError) {
          this.logger.error('Failed to perform logout after eligibility check failure', logoutError);
        }
        
        // Return properly typed error response (without details since AuthResponseDto doesn't have it)
        return {
          responseCode: authResult.responseCode,
          title: authResult.title,
          message: authResult.message,
        };
      }
      
      // Success case - authResult is the success type, so we can safely access its properties
      this.logger.log('Hard Token activation completed successfully with token generation');
      return {
        responseCode: AuthResponseCode.SUCCESS,
        title: this.i18nService.translate(
          AUTH_MESSAGES.LOGIN.OTP_ACTIVATION_SUCCESS + '.title',
          language,
        ),
        message: this.i18nService.translate(
          AUTH_MESSAGES.LOGIN.OTP_ACTIVATION_SUCCESS + '.body',
          language,
        ),
        ...authResult
      };
      
    } catch (error) {
      this.logger.error('Failed to finalize Hard Token activation', error);
      
      // Fallback: perform logout and return success without token (legacy behavior)
      try {
        const mockUser: CurrentUser = { cookies: cookies };
        await this.authService.logout(mockUser, language);
        this.logger.log('Performed fallback logout after error');
      } catch (logoutError) {
        this.logger.error('Failed to perform fallback logout', logoutError);
      }
      
      // Return success without token as fallback
      return {
        responseCode: AuthResponseCode.SUCCESS,
        title: this.i18nService.translate(
          AUTH_MESSAGES.LOGIN.OTP_ACTIVATION_SUCCESS + '.title',
          language,
        ),
        message: this.i18nService.translate(
          AUTH_MESSAGES.LOGIN.OTP_ACTIVATION_SUCCESS + '.body',
          language,
        ),
        // No token provided in error case - user needs to re-login
        userData: {},
      };
    }
  }

  /**
   * Make Step 1 request to ISAM
   */
  private async makeRequest(payload: any, cookies: string): Promise<any> {
    const url = `${this.baseUrl}/restportal/login/`;

    const headers = {
      'Content-Type': 'application/json',
      'Cookie': cookies,
      'Connection': 'keep-alive',
    };

    const requestConfig = {
      headers,
      httpsAgent: this.agent,
      maxRedirects: 0,
      timeout: 3 * 60 * 1000, // 3 minutes timeout in milliseconds
      validateStatus: (status) => status < 600, // Accept all status codes including 5xx
    };

    try {
      const response = await lastValueFrom(
        this.httpService.post(url, payload, requestConfig),
      );

      return {
        statusCode: response.status.toString(),
        headers: response.headers,
        body: JSON.stringify(response.data),
      };
    } catch (error) {
      // Handle timeout and gateway errors specifically
      if (error.response && (error.response.status === 504 || error.response.status === 502 || error.response.status === 503)) {
        this.logger.warn(`Gateway error ${error.response.status} detected, treating as timeout`);
        return {
          statusCode: error.response.status.toString(),
          headers: error.response.headers || {},
          body: `<html><body><h1>${error.response.status} Gateway Time-out</h1>\nThe server didn't respond in time.\n</body></html>`,
        };
      }

      // Re-throw other errors
      throw error;
    }
  }

  /**
   * Update Redis cache with new cookies and stateId
   */
  private async updateRedisCache(jti: string, cookies: string, stateId: string, attributes?: any): Promise<void> {
    if (jti) {
      try {
        const updatedPayload = {
          cookies: cookies,
          stateId: stateId,
          attributes: attributes,
          jti: jti,
        };

        await this.redisCacheService.set(
          jti,
          updatedPayload,
          { ttl: this.REDIS_TOKEN_TTL },
        );
        this.logger.debug(`Updated Redis cache for token ${jti}`);
      } catch (redisError) {
        this.logger.error('Failed to update Redis', redisError);
      }
    }
  }

  /**
   * Extract headers and cookies from response
   */
  private extractHeadersAndCookies(response: any): string[] {
    const headers = response.headers || {};
    const setCookieHeaders = headers['set-cookie'] || [];
    return Array.isArray(setCookieHeaders) ? setCookieHeaders : [setCookieHeaders];
  }

  private async checkUserDetails(language, parsedHeaders, userData) {
    try {
      // Check user details and eligibility
      const userDetails = await this.userDetailsService.checkEligibility(
        parsedHeaders,
        language,
      );

      // Extract userId from userDetails for caching
      const userId = userDetails.userDetails?.userId;

      // Extract CIF from customerReferences (use first one just in case)
      const cif = userDetails.userDetails?.customerReferences?.at(0)?.name; //name? use ID instead

      // Generate JWT token with userId and CIF with the JWT payload data
      const jwtToken = await this.jwtTokenService.generateToken(
        parsedHeaders,
        null, // stateId is null as we don't have state id .
        {
          userId,
          cif,
          industry: userDetails?.t24CompanyProfile?.industry,
          segment: userDetails?.segment?.id,
        },
      );

      // Return a standard authentication result
      return {
        token: jwtToken,
        userData: userData,
        ...userDetails,
      };
    } catch (error) {
      // Handle AuthenticationException specifically
      if (error instanceof AuthenticationException) {
        this.logger.warn('User eligibility check failed', error);

        // // Create a mock user with the parsed cookies to logout
        // try {
        //   const mockUser = { cookies: parsedHeaders } as CurrentUser;
        //   await this.logout(mockUser);
        //   this.logger.log(
        //     'Successfully logged out user after eligibility check failure',
        //   );
        // } catch (logoutError) {
        //   this.logger.error(
        //     'Failed to logout after eligibility check failure',
        //     logoutError,
        //   );
        //   // Continue with the error response even if logout fails
        // }

        return {
          responseCode: error.responseCode,
          title: this.i18nService.translate(
            error.messageKey + '.title',
            language,
          ),
          message: this.i18nService.translate(
            error.messageKey + '.body',
            language,
          ),
          details: error.details,
        };
      }
      // Re-throw other errors
      throw error;
    }
  }

  private buildAttributes(responseData) {
    return {
      nationalId: responseData?.objectData?.vasco_primary_key,
      mobileNumber: responseData?.objectData?.vasco_mobile_number,
      accountName: responseData?.objectData?.vasco_account_name_web, //ex: company.username
      verificationToken: responseData?.objectData?.vasco_finger_print_web,
      serialNumber: responseData?.objectData?.vasco_serial_number, // Include serial number for hard tokens
    };
  }

  private mergeAttributes(oldAttributes, newAttributes): any {
    const filteredAttributes = Object.fromEntries(
      Object.entries(newAttributes).filter(([_, value]) => value !== null && value !== ''),
    );

    return { ...oldAttributes, ...filteredAttributes };
  }

  /**
   * Update OTP flag to true in ISAM for the user
   * Implementation matches Java SendHTTPRequesttoISAMForUpdateOTPFlag method
   */
  private async updateOtpFlagInIsam(
    companyID: string,
    fbccUser: string,
  ): Promise<boolean> {
    try {
      // Use the same URL pattern as Java implementation
      const url = `${this.baseUrl}/mga/sps/authsvc?PolicyId=urn:ibm:security:authentication:asf:FBCCCreateUser`;

      // Match Java implementation header names from configuration
      const headers = {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'fbcc-user': fbccUser,           // tivoli.http.header.user = fbcc-user
        'companyID': companyID,          // tivoli.http.header.companyID = companyID
        'sn': `${fbccUser} user`,        // tivoli.http.header.snlastuser = sn
        'action': 'update',              // tivoli.http.header.action = action
        'otp': 'true',                   // tivoli.http.header.otp = otp
      };

      const requestConfig = {
        headers,
        httpsAgent: this.agent,
        maxRedirects: 0,
        timeout: 30 * 1000, // 30 seconds timeout to prevent hanging
        validateStatus: (status) => status < 600, // Allow all status codes for proper error handling
      };

      this.logger.log(`TIVOLI HTTP> Sending Request to ISAM for OTP Flag Update`);
      this.logger.log(`Updating OTP flag in ISAM for user: ${fbccUser} (company: ${companyID})`);

      const response = await lastValueFrom(
        this.httpService.post(url, {}, requestConfig),
      );

      this.logger.log(`Tivoli http Response status code: ${response.status}`);

      // Match Java implementation - only HTTP_OK (200) is considered success
      if (response.status === 200) {
        this.logger.log(`Successfully updated OTP flag in ISAM for user: ${fbccUser} (company: ${companyID})`);
        return true;
      } else {
        this.logger.error(`OTP flag update failed with status: ${response.status}`);
        return false;
      }

    } catch (error) {
      // Enhanced error logging to match Java implementation
      if (error.response) {
        this.logger.error(`Error while Processing Tivoli http request - Status: ${error.response.status}, Message: ${error.message}`);
      } else if (error.code === 'ECONNREFUSED' || error.code === 'ETIMEDOUT') {
        this.logger.error(`Error while Processing Tivoli http request - Connection error: ${error.message}`);
      } else {
        this.logger.error(`Error while Processing Tivoli http request: ${error.message}`);
      }
      return false;
    }
  }
}
