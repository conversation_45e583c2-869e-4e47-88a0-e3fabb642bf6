import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { I18nLookupService, RedisCacheService } from '@modules';
import { ForgetPasswordOptionsDto } from '../dto/forget-password-options.dto';
import { CaptchaService } from './captcha.service';
import { AuthResponseCode } from '../types/auth-response.types';
import { AUTH_MESSAGES } from '../constants/auth-messages.constants';
import { buildCaptchaResponse, buildResponse } from '../utils/response.utils';
import { IsamService } from './isam.service';
import { StateIdParserService } from './stateIdParserService';
import { JSDOM } from 'jsdom';
import { UserLanguage } from '@enums';
import { JwtTokenService } from './jwt-token.service';
import { parseSetCookieHeaders, updateRedisCache } from '../utils/cookie.utils';
import { AuthResponseDto } from '@/auth/dto/auth-response.dto';
import { ForgetPasswordSmsSubmitDto } from '@/auth/dto/forget-password-sms-submit.dto';
import { SelectForgetPasswordOptionDto } from '@/auth/dto/select-forget-password-option.dto';
import { ConfirmPhoneNumberDto } from '@/auth/dto/confirm-phone-number.dto';
import { AuthService } from '@/auth/services/auth.service';
import { ValidateEmailDto } from '@/auth/dto/validate-email.dto';
import { ForgetPasswordOtpVerifyDto } from '@/auth/dto/forget-password-otp-verify.dto';
import { ForgetPasswordOtpResetDto } from '@/auth/dto/forget-password-otp-reset.dto';

@Injectable()
export class ForgetPasswordService {
  private readonly logger: Logger;
  private readonly REDIS_TOKEN_TTL: number;

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
    private readonly i18nService: I18nLookupService,
    private readonly redisCacheService: RedisCacheService,
    private readonly captchaService: CaptchaService,
    private readonly isamService: IsamService,
    private readonly authService: AuthService,
    private readonly stateIdParserService: StateIdParserService,
    private readonly jwtTokenService: JwtTokenService,
  ) {
    this.logger = new Logger(ForgetPasswordService.name);
    this.REDIS_TOKEN_TTL = this.configService.get<number>('REDIS_TOKEN_TTL', 600);
  }

  /**
   * Get available forget options for a user
   * @param language User's preferred language
   * @returns Available forget options
   */
  async getForgetPasswordOptions(
    forgetPasswordOptionsDto: ForgetPasswordOptionsDto,
    language: UserLanguage = UserLanguage['en-US'],
  ) {
    const { username, company, captchaText, captchaToken } = forgetPasswordOptionsDto;

    // Validate the provided captcha
    const isValid = await this.captchaService.validateCaptcha(captchaToken, captchaText);
    if (!isValid) {
      // Generate new captcha for the response
      const { image, token } = await this.captchaService.generateCaptcha();
      return buildCaptchaResponse(
        this.i18nService,
        AuthResponseCode.CAPTCHA_REQUIRED,
        AUTH_MESSAGES.FORGET_PASSWORD.INVALID_CAPTCHA,
        language,
        image,
        token,
      );
    }

    try {
      // Initiate a forget process
      const forgetPasswordResponse = await this.isamService.initiateForgetPassword(
        username,
        company,
        this.stateIdParserService,
      );

      // Log the headers for debugging
      this.logger.debug('Forget password response headers:', forgetPasswordResponse.headers);

      // Check if the response indicates account is locked
      if (this.isAccountLockedResponse(forgetPasswordResponse.body)) {
        return buildResponse(
          this.i18nService,
          AuthResponseCode.ACCOUNT_LOCKED,
          AUTH_MESSAGES.FORGET_PASSWORD.ACCOUNT_LOCKED,
          language,
        );
      }

      // Check if the response indicates user not found
      if (this.isUserNotFoundResponse(forgetPasswordResponse.body)) {
        return buildResponse(
          this.i18nService,
          AuthResponseCode.USER_OR_COMPANY_NOT_FOUND,
          AUTH_MESSAGES.FORGET_PASSWORD.USER_OR_COMPANY_NOT_FOUND,
          language,
        );
      }

      // Extract available options from the response
      const options = this.extractForgetPasswordOptions(forgetPasswordResponse.body);

      // Extract cookies from response headers
      const cookies = this.extractHeadersAndCookies(forgetPasswordResponse);
      const parsedCookies = parseSetCookieHeaders(cookies);

      // Extract stateId from the HTML response
      // this stateId of the button that submit the chosen channel to BE .
      const stateId = this.stateIdParserService.parseStateIdFromHtml(forgetPasswordResponse.body);

      // Generate token with cookies, stateId and available options
      const token1 = await this.jwtTokenService.generateToken(
        parsedCookies,
        stateId,
        { attributes: { availableOptions: options } }, // Store options in token payload
      );

      return {
        responseCode: AuthResponseCode.SUCCESS,
        message: this.i18nService.translate(
          AUTH_MESSAGES.FORGET_PASSWORD.OPTIONS_SUCCESS,
          language,
        ),
        options,
        token1,
      };
    } catch (error) {
      this.logger.error('Failed to get forget password options', error);
      return buildResponse(
        this.i18nService,
        AuthResponseCode.SYSTEM_ERROR,
        AUTH_MESSAGES.FORGET_PASSWORD.OPTIONS_FAILED,
        language,
      );
    }
  }

  /**
   * Check if the response indicates user not found
   */
  private isUserNotFoundResponse(html: string): boolean {
    return html.includes('User not found') ||
      html.includes('Invalid username') ||
      html.includes('Invalid credentials') ||
      html.includes('Invalid CompanyID or Username') ||
      html.includes('errorMessage = "Invalid CompanyID or Username"');
  }

  /**
   * Check if the response indicates account is locked
   */
  private isAccountLockedResponse(html: string): boolean {
    return html.includes('Your account has been locked') ||
      html.includes('account has been locked') ||
      html.includes('errorMessage = "Your account has been locked');
  }

  /**
   * Extract available forget credentials options from the HTML response
   */
  private extractForgetPasswordOptions(html: string): any[] {
    try {
      const dom = new JSDOM(html);
      const document = dom.window.document;
      const options = [];
      const supportedTypes = ['SMS', 'OTP', 'EMAIL'];

      // Check for available reset options (radio buttons)
      const radioButtons = document.querySelectorAll('input[type="radio"][name="option"]');

      radioButtons.forEach(radio => {
        const value = radio.getAttribute('value');
        const parentDiv = radio.closest('.form-check');

        // Only include options that are not hidden by CSS or JavaScript
        if (parentDiv) {
          // Check if the option is visible (not hidden by display:none)
          const isVisible = parentDiv.style.display !== 'none';

          // Get the div ID to check against JavaScript variables
          const divId = parentDiv.getAttribute('id');

          // Extract JavaScript variable values from the HTML
          const jsVarRegex = new RegExp(`var\\s+${divId}\\s*=\\s*"([^"]*)"`, 'i');
          const jsVarMatch = html.match(jsVarRegex);

          // Check if the option is hidden by JavaScript
          const scriptHidden = (jsVarMatch && jsVarMatch[1] === 'false') ||
            html.includes(`document.getElementById('${divId}').style.display = 'none'`);

          if (isVisible && !scriptHidden) {
            const label = parentDiv.querySelector('.form-check-label');
            const labelText = label ? label.textContent.trim() : '';
            const optionType = this.determineOptionType(value, labelText);

            // Only add supported option types
            if (supportedTypes.includes(optionType)) {
              options.push(value);
            }
          }
        }
      });

      return options;
    } catch (error) {
      this.logger.error('Failed to extract forget password options', error);
      return [];
    }
  }

  /**
   * Determine the standardized option type from value and label
   */
  private determineOptionType(value: string, label: string): string {
    const normalizedLabel = label.toUpperCase();
    const normalizedValue = value.toUpperCase();

    if (normalizedLabel.includes('SMS') || normalizedValue.includes('SMS') ||
      normalizedLabel.includes('TEXT') || normalizedValue.includes('TEXT') ||
      normalizedLabel.includes('PHONE')) {
      return 'SMS';
    }

    if (normalizedLabel.includes('EMAIL') || normalizedValue.includes('EMAIL')) {
      return 'EMAIL';
    }

    if (normalizedLabel.includes('OTP') || normalizedValue.includes('OTP') ||
      normalizedLabel.includes('ONE-TIME') || normalizedValue.includes('ONE-TIME') ||
      normalizedLabel.includes('AUTHENTICATOR')) {
      return 'OTP';
    }

    // Default to the original value if no match
    return value;
  }

  /**
   * Extract headers and cookies from response
   */
  private extractHeadersAndCookies(response: any): string[] {
    return response.headers['set-cookie'] || [];
  }

  /**
   * Submit forget credentials via SMS
   * @param submitSmsDto DTO containing new credentials and SMS code
   * @param token3 Token containing cookies and stateId from options step
   * @param language User's preferred language
   * @returns Response indicating success or failure
   */
  async submitNewPassword(
    submitSmsDto: ForgetPasswordSmsSubmitDto,
    token3: string,
    language: UserLanguage = UserLanguage['en-US'],
  ): Promise<AuthResponseDto> {
    try {
      // Verify and decode the token
      const tokenData = await this.jwtTokenService.getTokenData(token3);
      if (!tokenData || !tokenData.cookies || !tokenData.stateId) {
        return buildResponse(
          this.i18nService,
          AuthResponseCode.TOKEN_ERROR,
          AUTH_MESSAGES.FORGET_PASSWORD.INVALID_TOKEN,
          language,
        );
      }

      const { cookies, stateId, attributes } = tokenData;
      const { newPassword, confirmPassword, tempPassword } = submitSmsDto;

      // Check if passwords match
      if (newPassword !== confirmPassword) {
        return buildResponse(
          this.i18nService,
          AuthResponseCode.VALIDATION_ERROR,
          AUTH_MESSAGES.PASSWORD.MISMATCH,
          language,
        );
      }

      // Submit the SMS verification and new credentials to ISAM
      const response = await this.isamService.submitNewPasswordAndTempPassword(
        stateId,
        cookies,
        tempPassword,
        newPassword,
        attributes?.flow,
      );

      // Check if the response indicates success
      if (this.isPasswordResetSuccessful(response.body)) {
        // If the response contains logout redirect, perform logout
        if (response.body.includes('pkmslogout')) {
          try {
            // Attempt to log out the session
            const logout_response = await this.authService.logout({ cookies: cookies });
          } catch (logoutError) {
            this.logger.warn('Failed to logout after password reset', logoutError);
            // Continue with success response even if logout fails
          }
        }

        return buildResponse(
          this.i18nService,
          AuthResponseCode.SUCCESS,
          AUTH_MESSAGES.FORGET_PASSWORD.RESET_SUCCESS,
          language,
        );
      } else {
        // Extract any new cookies or state from the response
        const newStateId = this.stateIdParserService.parseStateIdFromHtml(response.body);
        const responseCookies = this.extractHeadersAndCookies(response);
        const parsedCookies = parseSetCookieHeaders(responseCookies);

        // update redis with the new cookies
        await updateRedisCache(tokenData.jti, parsedCookies, newStateId, this.redisCacheService, this.REDIS_TOKEN_TTL, tokenData.attributes);


        if (this.isInvalidTempPassword(response.body)) {
          return buildResponse(
            this.i18nService,
            AuthResponseCode.INVALID_TEMP_PASSWORD,
            AUTH_MESSAGES.FORGET_PASSWORD.INVALID_TEMP_PASSWORD,
            language,
          );
        } else if (this.isInvalidPasswordFormat(response.body)) {
          // Extract hints if available
          const passwordHints = this.extractPasswordHints(response.body);

          const errorResponse = buildResponse(
            this.i18nService,
            AuthResponseCode.PASSWORD_COMPLEXITY,
            AUTH_MESSAGES.PASSWORD.COMPLEXITY,
            language,
          );
          // Add hints to the response if available
          if (passwordHints.length > 0) {
            return {
              ...errorResponse,
              passwordHints,
            };
          }
          return errorResponse;
        } else {
          return buildResponse(
            this.i18nService,
            AuthResponseCode.SYSTEM_ERROR,
            AUTH_MESSAGES.FORGET_PASSWORD.RESET_FAILED,
            language,
          );
        }
      }
    } catch (error) {
      this.logger.error('Error in submitNewPassword', error);
      return buildResponse(
        this.i18nService,
        AuthResponseCode.SYSTEM_ERROR,
        AUTH_MESSAGES.FORGET_PASSWORD.RESET_FAILED,
        language,
      );
    }
  }

  /**
   * Check if reset was successful
   */
  private isPasswordResetSuccessful(html: string): boolean {
    // Check for logout redirect which indicates successful reset
    if (html.includes('pkmslogout') || html.includes('window.location.href="../../pkmslogout"')) {
      return true;
    }
  }

  /**
   * Check if SMS code is invalid
   */
  private isInvalidTempPassword(html: string): boolean {
    return html.includes('invalid code') ||
      html.includes('incorrect code') ||
      html.includes('verification failed') ||
      html.includes('errorMessage ="Invalid temp password"');
  }

  /**
   * Check if the response indicates invalid format
   */
  private isInvalidPasswordFormat(html: string): boolean {
    return html.includes('Your new password is invalid') ||
      html.includes('password is invalid') ||
      html.includes('Please comply with the below password') ||
      html.includes('errorMessage ="Your new password is invalid');
  }

  /**
   * Select a forget credentials option
   */
  async selectOption(
    selectOptionDto: SelectForgetPasswordOptionDto,
    token1: string,
    language: UserLanguage = UserLanguage['en-US'],
  ): Promise<any> {
    try {
      // Verify and decode the token
      const tokenData = await this.jwtTokenService.getTokenData(token1);
      if (!tokenData || !tokenData.cookies || !tokenData.stateId) {
        return buildResponse(
          this.i18nService,
          AuthResponseCode.TOKEN_ERROR,
          AUTH_MESSAGES.FORGET_PASSWORD.INVALID_TOKEN,
          language,
        );
      }

      const { stateId, cookies, attributes } = tokenData;
      const { option } = selectOptionDto;

      // Validate that the selected option is one of the available options
      if (attributes?.availableOptions && !attributes?.availableOptions.some(opt =>
        this.determineOptionType(opt, opt) === option)) {
        return buildResponse(
          this.i18nService,
          AuthResponseCode.VALIDATION_ERROR,
          AUTH_MESSAGES.FORGET_PASSWORD.INVALID_OPTION,
          language,
        );
      }

      // Call ISAM to select the option
      const response = await this.isamService.selectOptionFromISAM(
        stateId,
        cookies,
        option,
      );

      // Extract any new cookies or state from the response
      const newStateId = this.stateIdParserService.parseStateIdFromHtml(response.body);
      const responseCookies = this.extractHeadersAndCookies(response);
      const parsedCookies = parseSetCookieHeaders(responseCookies);

      // update redis with the new cookies
      await updateRedisCache(tokenData.jti, parsedCookies, newStateId, this.redisCacheService, this.REDIS_TOKEN_TTL);

      // Generate a new token (token2) with the updated cookies and stateId
      // const token2 = await this.jwtTokenService.generateToken(parsedCookies, newStateId || stateId);

      if (option === 'SMS') {
        // Extract phone number from response if possible
        const phoneNumber = this.extractPhoneNumber(response.body);
        return {
          responseCode: AuthResponseCode.SUCCESS,
          message: this.i18nService.translate(
            AUTH_MESSAGES.FORGET_PASSWORD.CONFIRM_NUMBER,
            language,
          ),
          phoneNumber: phoneNumber,
          token2: token1, // return same token for FE compatibility, but no need to return this token
        };
      }
      // For OTP option, return ready for OTP verification
      if (option === 'OTP') {
        return {
          responseCode: AuthResponseCode.SUCCESS,
          message: this.i18nService.translate(
            AUTH_MESSAGES.FORGET_PASSWORD.OPTION_SELECTED,
            language,
          ),
          // message: 'OTP option selected. Please enter your OTP code from authenticator app.',
          token2: token1,
        };
      }

      // For other options, return a generic success response with the new token
      return {
        responseCode: AuthResponseCode.SUCCESS,
        message: this.i18nService.translate(
          AUTH_MESSAGES.FORGET_PASSWORD.OPTION_SELECTED,
          language,
        ),
        token2: token1,
      };
    } catch (error) {
      this.logger.error('Error in selecting option', error);
      return buildResponse(
        this.i18nService,
        AuthResponseCode.SYSTEM_ERROR,
        AUTH_MESSAGES.FORGET_PASSWORD.OPTION_SELECTION_FAILED,
        language,
      );
    }
  }

  /**
   * Confirm phone number and trigger SMS sending
   *
   * @param confirmPhoneDto DTO containing confirmation
   * @param token2 Token containing cookies and stateId from select option step
   * @param language User's preferred language
   * @returns Response indicating success or failure
   */
  async confirmPhoneNumber(
    confirmPhoneDto: ConfirmPhoneNumberDto,
    token2: string,
    language: UserLanguage = UserLanguage['en-US'],
  ): Promise<any> {
    try {
      // Verify and decode the token
      const tokenData = await this.jwtTokenService.getTokenData(token2);
      if (!tokenData || !tokenData.cookies || !tokenData.stateId) {
        return buildResponse(
          this.i18nService,
          AuthResponseCode.TOKEN_ERROR,
          AUTH_MESSAGES.FORGET_PASSWORD.INVALID_TOKEN,
          language,
        );
      }

      const { cookies, stateId } = tokenData;
      const { confirm } = confirmPhoneDto;

      // If user doesn't confirm, return error
      if (!confirm) {
        return buildResponse(
          this.i18nService,
          AuthResponseCode.USER_CANCELLED,
          AUTH_MESSAGES.FORGET_PASSWORD.USER_CANCELLED,
          language,
        );
      }

      // Trigger SMS sending with ISAM
      const response = await this.isamService.sendSmsVerification(
        stateId,
        cookies,
      );

      // Process verification response and update cache
      return this.processVerificationResponse(
        response,
        tokenData,
        token2,
        { flow: 'PHONE' },
        language,
      );
    } catch (error) {
      this.logger.error('Error in confirmPhoneNumber', error);
      return buildResponse(
        this.i18nService,
        AuthResponseCode.SYSTEM_ERROR,
        AUTH_MESSAGES.FORGET_PASSWORD.SMS_FAILED,
        language,
      );
    }
  }


  /**
   * Confirm phone number and trigger SMS sending
   *
   * @param validateEmailDto
   * @param token2 Token containing cookies and stateId from select option step
   * @param language User's preferred language
   * @returns Response indicating success or failure
   */
  async validateEmail(
    validateEmailDto: ValidateEmailDto,
    token2: string,
    language: UserLanguage = UserLanguage['en-US'],
  ): Promise<any> {
    try {
      // Verify and decode the token
      const tokenData = await this.jwtTokenService.getTokenData(token2);
      if (!tokenData || !tokenData.cookies || !tokenData.stateId) {
        return buildResponse(
          this.i18nService,
          AuthResponseCode.TOKEN_ERROR,
          AUTH_MESSAGES.FORGET_PASSWORD.INVALID_TOKEN,
          language,
        );
      }

      const { cookies, stateId } = tokenData;
      const { email } = validateEmailDto;


      // Trigger SMS sending with ISAM
      const response = await this.isamService.sendEmailVerification(
        stateId,
        cookies,
        email,
      );

      // Process verification response and update cache
      return this.processVerificationResponse(
        response,
        tokenData,
        token2,
        { flow: 'EMAIL' },
        language,
      );
    } catch (error) {
      this.logger.error('Error in validateEmail', error);
      return buildResponse(
        this.i18nService,
        AuthResponseCode.SYSTEM_ERROR,
        AUTH_MESSAGES.FORGET_PASSWORD.SMS_FAILED,
        language,
      );
    }
  }

  /**
   * Process verification response and update cache
   */
  private async processVerificationResponse(
    response: any,
    tokenData: any,
    token2: string,
    attributes: { flow: string },
    language: UserLanguage,
  ): Promise<any> {
    // Extract any new cookies or state from the response
    const newStateId = this.stateIdParserService.parseStateIdFromHtml(response.body);
    const responseCookies = response.headers['set-cookie'] || [];
    const combinedCookies = [...(Array.isArray(tokenData.cookies) ? tokenData.cookies : [tokenData.cookies]), ...responseCookies];
    const parsedCookies = parseSetCookieHeaders(combinedCookies);

    // update redis with the new cookies
    await updateRedisCache(tokenData.jti, parsedCookies, newStateId, this.redisCacheService, this.REDIS_TOKEN_TTL, attributes);

    // Check if the response indicates success
    if (this.isCodeSent(response.body)) {
      return {
        responseCode: AuthResponseCode.SUCCESS,
        message: this.i18nService.translate(
          (attributes.flow === 'EMAIL') ? AUTH_MESSAGES.FORGET_PASSWORD.MAIL_SENT : AUTH_MESSAGES.FORGET_PASSWORD.SMS_SENT,
          language,
        ),
        token3: token2,
      };
    } else {
      this.logger.error('Failed to send verification code', response.body);
      return buildResponse(
        this.i18nService,
        AuthResponseCode.SYSTEM_ERROR,
        (attributes.flow === 'EMAIL') ? AUTH_MESSAGES.FORGET_PASSWORD.MAIL_FAILED : AUTH_MESSAGES.FORGET_PASSWORD.SMS_FAILED,
        language,
      );
    }
  }

  /**
   * Check if code was sent successfully
   */
  private isCodeSent(html: string): boolean {
    // Check for standard code sent message or email
    const standardMessages = html.includes('verification code has been sent') ||
      html.includes('SMS sent') ||
      html.includes('code sent to your phone') ||
      html.includes('Enter the code') ||
      html.includes('verification code');

    // Check for credentials reset form elements that indicate we're on the reset screen
    const isPasswordResetForm = html.includes('Temporary Password') &&
      html.includes('New Password') &&
      html.includes('Confirm Password') &&
      html.includes('Change password');

    // Check for form input fields specific to the reset screen
    const hasPasswordFormFields = html.includes('name="password1"') &&
      html.includes('name="newPass1"') &&
      html.includes('name="confPass1"');

    return standardMessages || isPasswordResetForm || hasPasswordFormFields;
  }

  /**
   * Extract phone number from HTML response
   */
  private extractPhoneNumber(html: string): string | null {
    try {
      // First try to extract from input field with masked phone number
      const inputValueRegex = /<input[^>]*value=([*\d]+)>/i;
      const inputMatch = html.match(inputValueRegex);
      if (inputMatch && inputMatch[1]) {
        return inputMatch[1];
      }

      // Try with more specific selector for the provided HTML structure
      const dom = new JSDOM(html);
      const document = dom.window.document;
      const phoneInput = document.querySelector('input[name="passOTP"]');
      if (phoneInput && phoneInput.getAttribute('value')) {
        return phoneInput.getAttribute('value');
      }

      // Fallback to common patterns for masked phone numbers in text
      const patterns = [
        /sent to (\*+\d+)/i,
        /sent to your phone (\*+\d+)/i,
        /phone number (\*+\d+)/i,
        /(\*+\d{4})/,  // Matches patterns like ****1234
      ];

      for (const pattern of patterns) {
        const match = html.match(pattern);
        if (match && match[1]) {
          return match[1];
        }
      }

      return null;
    } catch (error) {
      this.logger.error('Failed to extract phone number', error);
      return null;
    }
  }

  /**
   * Verify OTP code for forget flow
   *
   * @param otpVerifyDto DTO containing OTP code
   * @param token2 Token containing cookies and stateId from select option step
   * @param language User's preferred language
   * @returns Response indicating success or failure
   */
  async verifyOtpCode(
    otpVerifyDto: ForgetPasswordOtpVerifyDto,
    token2: string,
    language: UserLanguage = UserLanguage['en-US'],
  ): Promise<AuthResponseDto> {
    this.logger.log('=== Starting OTP Verification for Forget Password ===');
    this.logger.log(`OTP Code length: ${otpVerifyDto.otpCode.length}`);

    try {
      // Verify and decode the token
      const tokenData = await this.jwtTokenService.getTokenData(token2);
      if (!tokenData || !tokenData.cookies || !tokenData.stateId) {
        this.logger.error('Invalid or missing token data');
        return buildResponse(
          this.i18nService,
          AuthResponseCode.TOKEN_ERROR,
          AUTH_MESSAGES.FORGET_PASSWORD.INVALID_TOKEN,
          language,
        );
      }

      const { cookies, stateId } = tokenData;
      this.logger.log(`Token validated - StateId: ${stateId}`);

      // Verify OTP with ISAM
      this.logger.log('Calling ISAM OTP verification...');
      const response = await this.isamService.verifyOtpForForgetPassword(
        stateId,
        cookies,
        otpVerifyDto.otpCode,
      );

      this.logger.log(`ISAM response status: ${response.status}`);

      // Extract any new cookies or state from the response
      const newStateId = this.stateIdParserService.parseStateIdFromHtml(response.body);
      const responseCookies = this.extractHeadersAndCookies(response);
      const parsedCookies = parseSetCookieHeaders(responseCookies);

      // Update Redis cache with new state
      this.logger.log('Updating Redis cache after OTP verification...');
      await updateRedisCache(tokenData.jti, parsedCookies, newStateId, this.redisCacheService, this.REDIS_TOKEN_TTL, { flow: 'OTP' });

      // Check if OTP verification was successful
      if (this.isOtpVerificationSuccessful(response.body)) {
        this.logger.log('✓ OTP verification successful');
        return {
          responseCode: AuthResponseCode.SUCCESS,
          message: 'OTP verified successfully. You can now set your new password.',
          token3: token2, // Use the same token for next step
        } as any;
      } else {
        this.logger.error('✗ OTP verification failed');
        this.logger.error('Response body:', response.body);

        // Check for specific OTP error messages
        if (this.isInvalidOtpCode(response.body)) {
          return buildResponse(
            this.i18nService,
            AuthResponseCode.VALIDATION_ERROR,
            AUTH_MESSAGES.LOGIN.INVALID_OTP_CODE,
            language,
          );
        }

        return buildResponse(
          this.i18nService,
          AuthResponseCode.SYSTEM_ERROR,
          AUTH_MESSAGES.FORGET_PASSWORD.RESET_FAILED,
          language,
        );
      }
    } catch (error) {
      this.logger.error('Error in OTP verification:', error);
      return buildResponse(
        this.i18nService,
        AuthResponseCode.SYSTEM_ERROR,
        AUTH_MESSAGES.FORGET_PASSWORD.RESET_FAILED,
        language,
      );
    }
  }

  /**
   * Submit new credentials for OTP forget credentials flow (without temp credentials)
   *
   * @param otpResetDto DTO containing new credentials and confirmation
   * @param token3 Token containing cookies and stateId from OTP verification step
   * @param language User's preferred language
   * @returns Response indicating success or failure
   */
  async submitOtpPasswordReset(
    otpResetDto: ForgetPasswordOtpResetDto,
    token3: string,
    language: UserLanguage = UserLanguage['en-US'],
  ): Promise<AuthResponseDto> {
    this.logger.log('=== Starting OTP Password Reset ===');

    try {
      // Verify and decode the token
      const tokenData = await this.jwtTokenService.getTokenData(token3);
      if (!tokenData || !tokenData.cookies || !tokenData.stateId) {
        this.logger.error('Invalid or missing token data');
        return buildResponse(
          this.i18nService,
          AuthResponseCode.TOKEN_ERROR,
          AUTH_MESSAGES.FORGET_PASSWORD.INVALID_TOKEN,
          language,
        );
      }

      const { cookies, stateId } = tokenData;
      const { newPassword, confirmPassword } = otpResetDto;

      // Check if passwords match
      if (newPassword !== confirmPassword) {
        this.logger.error('Password mismatch');
        return buildResponse(
          this.i18nService,
          AuthResponseCode.VALIDATION_ERROR,
          AUTH_MESSAGES.PASSWORD.MISMATCH,
          language,
        );
      }

      this.logger.log(`Token validated - StateId: ${stateId}`);

      // Submit the credentials reset to ISAM
      this.logger.log('Submitting OTP password reset to ISAM...');
      const response = await this.isamService.submitOtpPasswordReset(
        stateId,
        cookies,
        newPassword,
      );

      this.logger.log(`ISAM response status: ${response.status}`);

      // Check if the credentials reset was successful
      if (this.isPasswordResetSuccessful(response.body)) {
        this.logger.log('✓ OTP password reset successful');

        // If the response contains logout redirect, perform logout
        if (response.body.includes('pkmslogout')) {
          try {
            this.logger.log('Attempting logout after successful password reset...');
            await this.authService.logout({ cookies: cookies });
            this.logger.log('✓ Logout successful');
          } catch (logoutError) {
            this.logger.warn('Failed to logout after password reset', logoutError);
            // Continue with success response even if logout fails
          }
        }

        return buildResponse(
          this.i18nService,
          AuthResponseCode.SUCCESS,
          AUTH_MESSAGES.FORGET_PASSWORD.RESET_SUCCESS,
          language,
        );
      } else {
        this.logger.error('✗ OTP password reset failed');
        this.logger.error('Response body:', response.body);

        // Check for specific error conditions
        if (this.isInvalidPasswordFormat(response.body)) {
          // Extract any new cookies or state from the response for complexity error
          const newStateIdForError = this.stateIdParserService.parseStateIdFromHtml(response.body);
          const responseCookiesForError = this.extractHeadersAndCookies(response);
          const parsedCookiesForError = parseSetCookieHeaders(responseCookiesForError);

          // Update Redis cache with new state for complexity error
          this.logger.log('Updating Redis cache after complexity error...');
          await updateRedisCache(tokenData.jti, parsedCookiesForError, newStateIdForError, this.redisCacheService, this.REDIS_TOKEN_TTL);

          // Extract hints if available
          const passwordHints = this.extractPasswordHints(response.body);

          const errorResponse = buildResponse(
            this.i18nService,
            AuthResponseCode.PASSWORD_COMPLEXITY,
            AUTH_MESSAGES.PASSWORD.COMPLEXITY,
            language,
          );

          // Add hints to the response if available
          if (passwordHints.length > 0) {
            return {
              ...errorResponse,
              passwordHints,
            };
          }
          return errorResponse;
        }

        return buildResponse(
          this.i18nService,
          AuthResponseCode.SYSTEM_ERROR,
          AUTH_MESSAGES.FORGET_PASSWORD.RESET_FAILED,
          language,
        );
      }
    } catch (error) {
      this.logger.error('Error in OTP password reset:', error);
      return buildResponse(
        this.i18nService,
        AuthResponseCode.SYSTEM_ERROR,
        AUTH_MESSAGES.FORGET_PASSWORD.RESET_FAILED,
        language,
      );
    }
  }

  /**
   * Check if OTP verification was successful
   */
  private isOtpVerificationSuccessful(html: string): boolean {
    // Check for success indicators in the HTML response
    const successIndicators = [
      html.includes('New Password'),
      html.includes('Confirm Password'),
      html.includes('name="newPass"'),
      html.includes('name="confPass"'),
      html.includes('Change password'),
      html.includes('Set new password'),
      html.includes('Password reset'),
      !html.includes('Invalid OTP'),
      !html.includes('Incorrect OTP'),
      !html.includes('OTP verification failed'),
    ];

    // Must have at least one success indicator and no failure indicators
    const hasSuccessIndicators = successIndicators.slice(0, 7).some(indicator => indicator);
    const hasNoFailureIndicators = successIndicators.slice(7).every(indicator => indicator);

    this.logger.log(`OTP verification check - Success indicators: ${hasSuccessIndicators}, No failure indicators: ${hasNoFailureIndicators}`);

    return hasSuccessIndicators && hasNoFailureIndicators;
  }

  /**
   * Check if OTP code is invalid
   */
  private isInvalidOtpCode(html: string): boolean {
    return html.includes('Invalid OTP') ||
      html.includes('Incorrect OTP') ||
      html.includes('OTP verification failed') ||
      html.includes('Invalid one-time password') ||
      html.includes('errorMessage ="Invalid OTP"');
  }

  /**
   * Extract hints from HTML response
   */
  private extractPasswordHints(html: string): string[] {
    try {
      const dom = new JSDOM(html);
      const document = dom.window.document;
      const hints: string[] = [];

      // Look for the hints table
      const hintRows = document.querySelectorAll('#tips table tr');
      if (hintRows && hintRows.length > 0) {
        // Skip the header row (index 0)
        for (let i = 1; i < hintRows.length; i++) {
          const row = hintRows[i];
          const leftCell = row.querySelector('td[align="left"]');
          if (leftCell && leftCell.textContent) {
            hints.push(leftCell.textContent.trim());
          }
        }
      }

      return hints;
    } catch (error) {
      this.logger.error('Failed to extract password hints', error);
      return [];
    }
  }
}
