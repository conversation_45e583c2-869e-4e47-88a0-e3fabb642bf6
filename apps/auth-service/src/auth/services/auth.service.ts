import {
  forwardRef,
  Inject,
  Injectable,
  Logger,
  UnauthorizedException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { lastValueFrom } from 'rxjs';
import * as https from 'https';
import { LoginDto } from '../dto/login.dto';
import { ResetPasswordDto } from '../dto/reset-password.dto';
import { JwtTokenService } from './jwt-token.service';
import { IsamService } from './isam.service';
import { StateIdParserService } from './stateIdParserService';
import { parseSetCookieHeaders } from '../utils/cookie.utils';
import { CurrentUser } from '@types';
import { IsamErrorCodes } from '../types/isam.types';
import { AuthResponseCode } from '../types/auth-response.types';
import { AuthResponseDto } from '../dto/auth-response.dto';
import { AUTH_MESSAGES } from '../constants/auth-messages.constants';
import { I18nLookupService, RedisCacheService } from '@modules';
import { UserLanguage } from '@enums';
import { CaptchaService } from './captcha.service';
import { buildCaptchaResponse, buildResponse } from '../utils/response.utils';
import { UserDetailsService } from '@/auth/services/user-details.service';
import { AuthenticationException } from '@/auth/exceptions/authentication.exception';
import { TerminationService } from './termination.service';
import { ResetPasswordService } from './reset-password.service';
import { TermsAcceptanceService } from './terms-acceptance.service';
import { OtpActivationService } from './otp-activation.service';

@Injectable()
export class AuthService {
  // Constants for security and logic thresholds
  private readonly MAX_ATTEMPTS: number;
  private readonly CAPTCHA_THRESHOLD: number;
  private readonly ATTEMPT_TTL: number;
  private readonly REDIS_TOKEN_TTL: number;

  private readonly agent;
  private readonly logger: Logger;

  readonly baseUrl: string;
  readonly loginBaseUrl: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
    private readonly jwtTokenService: JwtTokenService,
    private readonly isamService: IsamService,
    private readonly stateIdParserService: StateIdParserService,
    private readonly i18nService: I18nLookupService,
    private readonly redisCacheService: RedisCacheService,
    private readonly captchaService: CaptchaService,
    private readonly userDetailsService: UserDetailsService,
    private readonly terminationService: TerminationService,
    private readonly resetPasswordService: ResetPasswordService,
    private readonly termsAcceptanceService: TermsAcceptanceService,
    @Inject(forwardRef(() => OtpActivationService))
    private readonly otpActivationService: OtpActivationService,
  ) {
    this.logger = new Logger(AuthService.name);
    this.agent = new https.Agent({ rejectUnauthorized: false });
    this.MAX_ATTEMPTS = this.configService.get<number>('AUTH_MAX_ATTEMPTS', 5);
    this.CAPTCHA_THRESHOLD = this.configService.get<number>(
      'AUTH_CAPTCHA_THRESHOLD',
      3,
    );
    this.ATTEMPT_TTL = this.configService.get<number>(
      'AUTH_ATTEMPT_TTL',
      24 * 60 * 60,
    ); // 24 hours in seconds
    this.REDIS_TOKEN_TTL = this.configService.get<number>(
      'REDIS_TOKEN_TTL',
      600, // Default 10 minutes in seconds
    );
    this.baseUrl = this.configService.getOrThrow('ISAM_BASE_URL');
    this.loginBaseUrl = this.configService
      .getOrThrow('ISAM_BASE_URL')
      .replace(/\/(FBCCBB|FBCCSIT)$/, '');
  }

  /**
   * User login endpoint.
   * Handles both normal and termination login flows.
   */
  async login(
    loginDto: LoginDto,
    terminationHeader: string,
    language: UserLanguage = UserLanguage['en-US'],
  ): Promise<AuthResponseDto> {
    if (terminationHeader) {
      return this.terminationService.handleTermination(
        terminationHeader,
        language,
      );
    }
    return this.handleNormalLogin(loginDto, language);
  }

  /**
   * Logout the current user.
   * Always returns success to the client, regardless of ISAM response.
   * As we want to clear their local session
   */
  async logout(
    currentUser: CurrentUser,
    userLanguage: UserLanguage = UserLanguage['en-US'],
  ): Promise<AuthResponseDto> {
    this.logger.log('User logging out');
    try {
      // First call the REST portal logout endpoint
      const restPortalLogoutUrl = this.baseUrl + '/restportal/fbcclogout/';

      // Build the required payload with username and company from token
      const logoutPayload = {
        userData: {
          company: currentUser.company || null,
          username: currentUser.username || null,
          userSelectedLanguage: null,
        },
        requestData: null,
      };

      // Extract cookies from current user
      const cookies = currentUser.cookies || '';

      // Call REST portal logout endpoint
      const restportalResponse = await lastValueFrom(
        this.httpService.post(restPortalLogoutUrl, logoutPayload, {
          headers: {
            'Content-Type': 'application/json',
            Cookie: cookies,
          },
          httpsAgent: this.agent,
        } as any),
      );
      this.logger.log('REST portal logout successful', restportalResponse);

      // Then call the traditional ISAM logout endpoint
      const isamLogoutUrl = `${this.loginBaseUrl}/pkmslogout`;
      await lastValueFrom(
        this.httpService.get(isamLogoutUrl, {
          headers: { Cookie: cookies },
          httpsAgent: this.agent,
        } as any),
      );
      this.logger.log('ISAM logout successful');
    } catch (error) {
      this.logger.error('Logout failed', error);
    }
    return {
      responseCode: AuthResponseCode.SUCCESS,
      title: this.i18nService.translate(
        AUTH_MESSAGES.LOGIN.LOGOUT_SUCCESS + '.title',
        userLanguage,
      ),
      message: this.i18nService.translate(
        AUTH_MESSAGES.LOGIN.LOGOUT_SUCCESS + '.body',
        userLanguage,
      ),
    };
  }

  /**
   * Handle a development login process
   */
  async devLogin(
    loginDto: LoginDto,
    language: UserLanguage = UserLanguage['en-US'],
  ): Promise<AuthResponseDto> {
    try {
      this.logger.log('Development login initiated');

      // Create custom payload with actual login credentials
      const customPayload = this.isamService.createRestPortalPayload(
        {
          username: loginDto.username,
          company: loginDto.company,
          password: loginDto.password,
        },
        language,
      );

      // Get dev login URL from config
      const devLoginUrl =
        this.configService.get<string>('FCC_URL') + '/restportal/login';

      // Call REST portal authentication with mock response, custom payload, and custom URL
      const isamRestPortalResponse =
        await this.isamService.performRestPortalAuthentication(
          {
            statusCode: '302',
            status: 302,
            headers: { 'set-cookie': [] },
          },
          language,
          customPayload,
          devLoginUrl,
        );

      // Generate a unique stateId for dev environment
      const stateId = `dev-${Date.now()}`;

      // Extract headers from response
      const isamRestPortalResponseHeaders = this.extractHeadersAndCookies(
        isamRestPortalResponse,
      );

      // Parse headers
      const parsedHeaders = parseSetCookieHeaders(
        isamRestPortalResponseHeaders,
      );

      const userDetails =
        await this.userDetailsService.fetchUserDetails(parsedHeaders);

      // Generate JWT token
      const jwtToken = await this.jwtTokenService.generateToken(
        parsedHeaders,
        stateId,
        {
          userId: userDetails.userId,
          cif: userDetails.customerReferences.at(0).ID,
          industry: '104',
          segment: 'business_banking',
          username: loginDto.username,
          company: loginDto.company,
        },
      );

      // Return authentication result
      return {
        responseCode: AuthResponseCode.SUCCESS,
        title: this.i18nService.translate(
          AUTH_MESSAGES.LOGIN.SUCCESS + '.title',
          UserLanguage['en-US'],
        ),
        message: this.i18nService.translate(
          AUTH_MESSAGES.LOGIN.SUCCESS + '.body',
          UserLanguage['en-US'],
        ),
        token: jwtToken,
        userData: JSON.parse(isamRestPortalResponse.body),
      };
    } catch (error) {
      this.logger.error('Development login failed', error);
      return {
        responseCode: AuthResponseCode.INVALID_CREDENTIALS,
        title: this.i18nService.translate(
          AUTH_MESSAGES.LOGIN.INVALID_CREDENTIALS + '.title',
          UserLanguage['en-US'],
        ),
        message: this.i18nService.translate(
          AUTH_MESSAGES.LOGIN.INVALID_CREDENTIALS + '.body',
          UserLanguage['en-US'],
        ),
      };
    }
  }

  /**
   * Refresh an existing JWT token
   */
  async refreshToken(
    authorizationHeader: string,
  ): Promise<{ token: string; nearExpiry: boolean }> {
    this.logger.log('Token refresh requested');
    if (!authorizationHeader) {
      throw new UnauthorizedException(
        'Authorization header is missing',
        'TOKEN_MISSING',
      );
    }
    try {
      // Extract token from authorization header
      const token = authorizationHeader.replace(/^Bearer\s+/i, '');

      // Generate a new token
      const newToken = await this.jwtTokenService.generateRefreshToken(token);

      // Check if the new token is near expiry (this would indicate ISAM session is about to expire)
      const nearExpiry = await this.jwtTokenService.isTokenNearExpiry(newToken);
      return { token: newToken, nearExpiry };
    } catch (error) {
      this.logger.error('Token refresh failed', error);
      if (error instanceof UnauthorizedException) throw error;
      throw new UnauthorizedException(
        'Failed to refresh token',
        IsamErrorCodes.INVALID_CREDENTIALS,
      );
    }
  }

  /**
   * Reset user credentials
   */
  async resetPassword(
    resetPasswordDto: ResetPasswordDto,
    resetPasswordToken: string,
    language: UserLanguage = UserLanguage['en-US'],
  ): Promise<AuthResponseDto> {
    return this.resetPasswordService.resetPassword(
      resetPasswordDto,
      resetPasswordToken,
      language,
    );
  }

  /**
   * Handle terms and conditions acceptance
   */
  async acceptTerms(
    currentUser: CurrentUser,
    language: UserLanguage = UserLanguage['en-US'],
  ): Promise<AuthResponseDto> {
    return this.termsAcceptanceService.acceptTerms(currentUser, language);
  }

  /**
   * Handles the normal login flow, including
   * a Captcha challenge after threshold
   * ISAM authentication and error handling
   */
  private async handleNormalLogin(
    loginDto: LoginDto,
    language: UserLanguage,
  ): Promise<AuthResponseDto> {
    try {
      // Validate if captcha is needed
      const captchaResponse = await this.validateCaptchaIfNeeded(
        loginDto,
        language,
      );
      if (captchaResponse) {
        return captchaResponse;
      }

      // Proceed with ISAM authentication
      const { stateId, response: portalResponse } =
        await this.stateIdParserService.extractStateId();

      const isamResponse = await this.isamService.performAuthentication(
        loginDto,
        { stateId, response: portalResponse },
      );

      // ISAM: Account locked
      if (this.hasLockedAccountError(isamResponse.body)) {
        this.logger.warn('Authentication failed: Locked Account');
        return buildResponse(
          this.i18nService,
          AuthResponseCode.LOCKED_ACCOUNT,
          AUTH_MESSAGES.LOGIN.LOCKED_ACCOUNT,
          language,
        );
      }

      // ISAM: Incorrect credentials
      if (this.hasIncorrectCredentialsError(isamResponse.body)) {
        this.logger.warn('Authentication failed: Incorrect credentials');

        // Increment failed attempts
        const attemptsKey = this.captchaService.createHashedLoginAttemptsKey(
          loginDto.username,
        );
        await this.captchaService.incrementFailedAttempts(attemptsKey);

        // Get current attempts count to determine if captcha is needed
        const currentAttempts =
          await this.captchaService.getFailedAttempts(attemptsKey);

        // Generate captcha if threshold reached
        if (
          currentAttempts >=
          this.configService.get<number>('AUTH_CAPTCHA_THRESHOLD')
        ) {
          const { image, token } = await this.captchaService.generateCaptcha();
          return buildCaptchaResponse(
            this.i18nService,
            AuthResponseCode.CAPTCHA_REQUIRED,
            AUTH_MESSAGES.LOGIN.INVALID_CREDENTIALS_CAPTCHA,
            language,
            image,
            token,
          );
        }

        // Otherwise return standard invalid credentials response
        return buildResponse(
          this.i18nService,
          AuthResponseCode.INVALID_CREDENTIALS,
          AUTH_MESSAGES.LOGIN.INVALID_CREDENTIALS,
          language,
        );
      }

      // Reset failed attempts on successful authentication
      await this.captchaService.resetFailedAttempts(
        this.captchaService.createHashedLoginAttemptsKey(loginDto.username),
      );

      // Check if the response contains an error message indicating expired
      if (this.hasPasswordExpiredError(isamResponse.body)) {
        this.logger.warn('Authentication failed: Password Expired');

        // Generate reset token
        const resetPasswordToken = await this.extractResetInfo(
          portalResponse,
          isamResponse,
          loginDto,
        );

        return {
          ...buildResponse(
            this.i18nService,
            AuthResponseCode.EXPIRED_PASSWORD,
            AUTH_MESSAGES.LOGIN.EXPIRED_PASSWORD,
            language,
          ),
          resetPasswordToken,
        };
      }

      // ISAM: Active session
      if (this.isamService.hasActiveSession(isamResponse.body)) {
        const activeSessionResponse =
          await this.terminationService.extractTerminationInfo(
            portalResponse,
            isamResponse,
            loginDto,
          );
        return {
          responseCode: AuthResponseCode.ACTIVE_SESSION,
          title: this.i18nService.translate(
            AUTH_MESSAGES.LOGIN.ACTIVE_SESSION + '.title',
            language,
          ),
          message: this.i18nService.translate(
            AUTH_MESSAGES.LOGIN.ACTIVE_SESSION + '.body',
            language,
          ),
          terminationToken: activeSessionResponse.terminationToken,
        };
      }

      // No active session - complete authentication
      const authResult = await this.completeAuthentication(
        portalResponse,
        isamResponse,
        stateId,
        language,
        loginDto,
      );

      // If the result already has a responseCode, it means an error occurred
      if (authResult.responseCode) {
        return authResult;
      }

      // Check if this is OTP activation response
      if (authResult.otpActivationRequired) {
        return {
          ...authResult,
          responseCode: AuthResponseCode.OTP_ACTIVATION_REQUIRED,
          title: this.i18nService.translate(
            AUTH_MESSAGES.LOGIN.OTP_ACTIVATION_REQUIRED + '.title',
            language,
          ),
          message: this.i18nService.translate(
            AUTH_MESSAGES.LOGIN.OTP_ACTIVATION_REQUIRED + '.body',
            language,
          ),
          otpActivationToken: authResult.otpActivationToken,
          tokenType: authResult.tokenType,
        };
      }

      // Check if this is a term acceptance response
      if (authResult.termsRequired) {
        return {
          ...authResult,
          responseCode: AuthResponseCode.TERMS_REQUIRED,
          title: this.i18nService.translate(
            AUTH_MESSAGES.LOGIN.TERMS_REQUIRED + '.title',
            language,
          ),
          message: this.i18nService.translate(
            AUTH_MESSAGES.LOGIN.TERMS_REQUIRED + '.body',
            language,
          ),
          termsAndConditionToken: authResult.token, // Changed from 'token' to 'termsAndConditionToken'
          terms: authResult.terms,
          tokenType: authResult.tokenType,
        };
      }

      // Return a standard authentication result
      return {
        ...buildResponse(
          this.i18nService,
          AuthResponseCode.SUCCESS,
          AUTH_MESSAGES.LOGIN.SUCCESS,
          language,
        ),
        token: authResult.token,
        userData: authResult.userData,
        userDetails: authResult.userDetails,
        segment: authResult.segment,
      };
    } catch (error) {
      this.logger.error('Login failed', error);
      return buildResponse(
        this.i18nService,
        AuthResponseCode.SYSTEM_ERROR,
        AUTH_MESSAGES.LOGIN.SYSTEM_ERROR,
        language,
      );
    }
  }

  /**
   * Check if the HTML response contains an error message indicating incorrect credentials.
   */
  private hasIncorrectCredentialsError(html: string): boolean {
    return html.includes('Your user or password is incorrect');
  }

  /**
   * Check if the HTML response contains an error message indicating account lock
   */
  private hasLockedAccountError(html: string): boolean {
    return html.includes('Your account has been locked');
  }

  /**
   * Check if the HTML response contains an error message indicating credentials expired
   */
  private hasPasswordExpiredError(html: string): boolean {
    return html.includes('Your password is expired');
  }

  /**
   * Extract headers and cookies from response
   */
  private extractHeadersAndCookies(response: any): string[] {
    return response.headers['set-cookie'] || [];
  }

  /**
   * Complete the authentication process after successful login
   */
  private async completeAuthentication(
    portalResponse: any,
    isamResponse: any,
    stateId: string,
    language: UserLanguage,
    loginDto: LoginDto,
  ): Promise<any> {
    try {
      // hit the third call.
      const isamRestPortalResponse =
        await this.isamService.performRestPortalAuthentication(
          isamResponse,
          language,
        );

      const portalHeaders = this.extractHeadersAndCookies(portalResponse);
      const isamAuthenticationHeaders =
        this.extractHeadersAndCookies(isamResponse);
      const isamRestPortalResponseHeaders = this.extractHeadersAndCookies(
        isamRestPortalResponse,
      );
      const collectedHeaders = [
        ...portalHeaders,
        ...isamAuthenticationHeaders,
        ...isamRestPortalResponseHeaders,
      ];
      const parsedHeaders = parseSetCookieHeaders(collectedHeaders);

      // Parse the response body
      const userData = JSON.parse(isamRestPortalResponse.body);

      // Check if this is a token registration step one response
      const otpResponse =
        await this.otpActivationService.checkAndHandleOtpActivationRequired(
          userData,
          parsedHeaders,
          stateId,
          language,
        );
      if (otpResponse) {
        return {
          otpActivationToken: otpResponse.otpActivationToken,
          otpActivationRequired: true,
          tokenType: otpResponse.tokenType,
          otpActivationStep: otpResponse.otpActivationStep,
          userData: userData,
        };
      }

      // Check if this is a terms acceptance response
      if (userData.mode === 'accept_terms' && userData.objectData?.tandctext) {
        this.logger.log('Terms and conditions acceptance required');

        // Generate JWT token without userId/CIF for terms acceptance (will be added after terms acceptance)
        const jwtToken = await this.jwtTokenService.generateToken(
          parsedHeaders,
          stateId,
        );

        return {
          token: jwtToken,
          termsRequired: true,
          terms: userData.objectData.tandctext,
          tokenType: userData.objectData.tokentype || 'NO_REAUTH',
        };
      }

      try {
        // Check user details and eligibility
        const userDetails = await this.userDetailsService.checkEligibility(
          parsedHeaders,
          language,
        );

        // Extract userId from userDetails for caching
        const userId = userDetails.userDetails?.userId;

        // Extract CIF from customerReferences (use first one just in case)
        const cif = userDetails.userDetails?.customerReferences?.at(0)?.name; //name? use ID instead

        // Generate JWT token with userId and CIF with the JWT payload data
        const jwtToken = await this.jwtTokenService.generateToken(
          parsedHeaders,
          stateId,
          {
            userId,
            cif,
            industry: userDetails?.t24CompanyProfile?.industry,
            segment: userDetails?.segment?.id,
            username: loginDto.username,
            company: loginDto.company,
            fullName: userData?.firstName + ' ' + userData?.lastName,
          },
        );

        // Return a standard authentication result
        return {
          token: jwtToken,
          userData: userData,
          ...userDetails,
        };
      } catch (error) {
        // Handle AuthenticationException specifically
        if (error instanceof AuthenticationException) {
          this.logger.warn('User eligibility check failed', error);

          // Create a mock user with the parsed cookies to logout
          try {
            const mockUser = { cookies: parsedHeaders } as CurrentUser;
            await this.logout(mockUser);
            this.logger.log(
              'Successfully logged out user after eligibility check failure',
            );
          } catch (logoutError) {
            this.logger.error(
              'Failed to logout after eligibility check failure',
              logoutError,
            );
            // Continue with the error response even if logout fails
          }

          return {
            responseCode: error.responseCode,
            title: this.i18nService.translate(
              error.messageKey + '.title',
              language,
            ),
            message: this.i18nService.translate(
              error.messageKey + '.body',
              language,
            ),
            details: error.details,
          };
        }
        // Re-throw other errors
        throw error;
      }
    } catch (error) {
      this.logger.error('Authentication completion failed', error);
      throw error;
    }
  }

  /**
   * Extract reset information and generate token
   */
  private async extractResetInfo(
    portalResponse: any,
    isamResponse: any,
    loginDto: LoginDto,
  ): Promise<string> {
    try {
      const resetStateId = this.stateIdParserService.parseStateIdFromHtml(
        isamResponse.body,
      );
      const portalHeaders = this.extractHeadersAndCookies(portalResponse);
      const isamAuthenticationHeaders =
        this.extractHeadersAndCookies(isamResponse);
      const collectedHeaders = [...portalHeaders, ...isamAuthenticationHeaders];

      // Parse the collected headers into a cookie string
      const parsedCookies = parseSetCookieHeaders(collectedHeaders);

      return await this.jwtTokenService.generateToken(
        parsedCookies,
        resetStateId,
        {
          username: loginDto.username,
          company: loginDto.company,
        },
      );
    } catch (error) {
      this.logger.error('Failed to extract reset info', error);
      throw new UnauthorizedException(
        'Failed to generate reset token',
        IsamErrorCodes.EXPIRED_PASSWORD,
      );
    }
  }

  /**
   * Validate reset token
   */
  private async validateResetToken(token: string): Promise<any> {
    try {
      // Extract token from authorization header
      const actualToken = token.replace(/^Bearer\s+/i, '');
      return await this.jwtTokenService.verifyToken(actualToken);
    } catch (error) {
      this.logger.error('Invalid reset token', error);
      throw new UnauthorizedException(
        'Invalid reset token',
        'INVALID_TOKEN',
      );
    }
  }
  /**
   * Handle captcha validation if needed
   */
  private async validateCaptchaIfNeeded(
    loginDto: LoginDto,
    language: UserLanguage,
  ): Promise<AuthResponseDto | null> {
    try {
      this.logger.debug('Validating captcha challenge if needed');
      return await this.captchaService.validateCaptchaChallenge(
        loginDto,
        language,
      );
    } catch (error) {
      this.logger.error('Captcha validation failed:', error);
      return buildResponse(
        this.i18nService,
        AuthResponseCode.SYSTEM_ERROR,
        AUTH_MESSAGES.LOGIN.SYSTEM_ERROR,
        language,
      );
    }
  }
}
