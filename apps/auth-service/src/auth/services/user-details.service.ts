import { Injectable, Logger } from '@nestjs/common';
import { EsbRequestsEnum, FccRequestsEnum, UserLanguage } from '@enums';
import { ApiAdapterClientService } from '@modules';
import { EsbCustomerDetailsDto, UserDetailsDto } from '@dtos';
import { AuthResponseCode } from '@/auth/types/auth-response.types';
import { AUTH_MESSAGES } from '@/auth/constants/auth-messages.constants';
import { IndustryLookupService } from '../../../../../packages/modules/src/lookups/services/industry.service';
import { AuthenticationException } from '@/auth/exceptions/authentication.exception';

@Injectable()
export class UserDetailsService {
  private readonly logger: Logger;

  constructor(
    private readonly apiAdapterService: ApiAdapterClientService,
    private readonly industryCodeService: IndustryLookupService,
  ) {
    this.logger = new Logger(UserDetailsService.name);
  }

  /**
   * Fetch user details by ID
   */
  async fetchUserDetails(cookies: any) {
    try {
      this.logger.log(`Fetching user details`);
      console.log(cookies);
      return await this.apiAdapterService.fccRequest<UserDetailsDto>({
        requestId: FccRequestsEnum.USER_DETAILS,
        user: {
          cookies: cookies,
        },
        options: {
          cacheEnabled: false,
        },
      });
    } catch (error) {
      this.logger.error('Failed to fetch user details', error);
      throw error;
    }
  }

  async checkEligibility(cookies: any, language: UserLanguage) {
    const userDetails: UserDetailsDto = await this.fetchUserDetails(cookies);
    console.log(userDetails);
    this.checkCifCounts(userDetails, language);
    this.checkEntityCounts(userDetails, language);
    const t24CompanyProfile =
      await this.apiAdapterService.esbRequest<EsbCustomerDetailsDto>({
        requestId: EsbRequestsEnum.CUSTOMER_DETAILS,
        payload: {
          userId: userDetails.customerReferences.at(0).name,
        },
      });
    const segment = this.checkIndustryCode(t24CompanyProfile, language);
    return {
      userDetails,
      segment,
      t24CompanyProfile,
    };
  }

  private checkEntityCounts(
    userDetails: UserDetailsDto,
    language: UserLanguage,
  ) {
    if (userDetails?.entities?.length > 1) {
      throw new AuthenticationException(
        AuthResponseCode.NOT_ELIGIBLE,
        AUTH_MESSAGES.LOGIN.NOT_ELIGIBLE,
        language,
      );
    }
  }

  private checkCifCounts(userDetails: UserDetailsDto, language: UserLanguage) {
    if (userDetails?.customerReferences.length > 1) {
      throw new AuthenticationException(
        AuthResponseCode.NOT_ELIGIBLE,
        AUTH_MESSAGES.LOGIN.NOT_ELIGIBLE,
        language,
      );
    }
  }

  private checkIndustryCode(
    t24CompanyProfile: EsbCustomerDetailsDto,
    language: UserLanguage,
  ) {
    const industryCode = this.industryCodeService.getIndustry(
      t24CompanyProfile.industry,
    );
    console.log('industryCode: ', industryCode);
    if (!industryCode) {
      throw new AuthenticationException(
        AuthResponseCode.NOT_ELIGIBLE,
        AUTH_MESSAGES.LOGIN.NOT_ELIGIBLE,
        language,
      );
    }
    return industryCode.segment;
  }
}
