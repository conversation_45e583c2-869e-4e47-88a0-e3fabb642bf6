import { Injectable, Logger, UnauthorizedException, forwardRef, Inject } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { lastValueFrom } from 'rxjs';
import { I18nLookupService, RedisCacheService } from '@modules';
import { ResetPasswordDto } from '../dto/reset-password.dto';
import { AuthResponseDto } from '../dto/auth-response.dto';
import { AuthResponseCode } from '../types/auth-response.types';
import { AUTH_MESSAGES } from '../constants/auth-messages.constants';
import { UserLanguage } from '@enums';
import { JwtTokenService } from './jwt-token.service';
import { parseSetCookieHeaders, updateRedisCache } from '../utils/cookie.utils';
import { buildResponse } from '../utils/response.utils';
import { StateIdParserService } from './stateIdParserService';
import { CurrentUser } from '@types';
import { AxiosRequestConfig } from 'axios';
import * as https from 'https';
import { AuthService } from './auth.service';

@Injectable()
export class ResetPasswordService {
  private readonly logger: Logger;
  private readonly REDIS_TOKEN_TTL: number;
  private readonly loginBaseUrl: string;
  private readonly baseUrl: string;
  private readonly agent: https.Agent;

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
    private readonly jwtTokenService: JwtTokenService,
    private readonly stateIdParserService: StateIdParserService,
    private readonly i18nService: I18nLookupService,
    private readonly redisCacheService: RedisCacheService,
    @Inject(forwardRef(() => AuthService)) private readonly authService: AuthService,
  ) {
    this.logger = new Logger(ResetPasswordService.name);
    this.agent = new https.Agent({ rejectUnauthorized: false });
    this.REDIS_TOKEN_TTL = this.configService.get<number>('REDIS_TOKEN_TTL', 600);
    this.baseUrl = this.configService.getOrThrow('ISAM_BASE_URL');
    this.loginBaseUrl = this.configService
      .getOrThrow('ISAM_BASE_URL')
      .replace(/\/(FBCCBB|FBCCSIT)$/, '');
  }

  /**
   * Reset user credentials
   */
  async resetPassword(
    resetPasswordDto: ResetPasswordDto,
    resetPasswordToken: string,
    language: UserLanguage = UserLanguage['en-US'],
  ): Promise<AuthResponseDto> {
    try {
      if (!resetPasswordToken) {
        return buildResponse(
          this.i18nService,
          AuthResponseCode.TOKEN_ERROR,
          AUTH_MESSAGES.TOKEN.MISSING,
          language,
        );
      }

      // Validate credentials match
      if (!this.validatePasswordsMatch(resetPasswordDto)) {
        return buildResponse(
          this.i18nService,
          AuthResponseCode.SYSTEM_ERROR,
          AUTH_MESSAGES.PASSWORD.MISMATCH,
          language,
        );
      }

      // Validate if new credential same as old credential
      if (this.isNewPasswordSameAsOld(resetPasswordDto)) {
        return buildResponse(
          this.i18nService,
          AuthResponseCode.SYSTEM_ERROR,
          AUTH_MESSAGES.PASSWORD.INVALID_NEW_PASSWORD,
          language,
        );
      }

      // Get token data
      const tokenData = await this.jwtTokenService.getTokenData(resetPasswordToken);
      if (!tokenData) {
        return buildResponse(
          this.i18nService,
          AuthResponseCode.TOKEN_ERROR,
          AUTH_MESSAGES.TOKEN.INVALID,
          language,
        );
      }

      // Submit reset request
      const resetResponse = await this.submitPasswordReset(resetPasswordDto, tokenData);

      // Process response
      return this.processResetResponse(resetResponse, tokenData, language);
    } catch (error) {
      this.logger.error('Password reset failed', error);
      if (error instanceof UnauthorizedException) {
        return buildResponse(
          this.i18nService,
          AuthResponseCode.TOKEN_ERROR,
          AUTH_MESSAGES.TOKEN.INVALID,
          language,
        );
        // this case isam changed the credentials, but it fails for some reason, we need to handle it as success response for now
      } else if (error.isAxiosError) {

        return buildResponse(
          this.i18nService,
          AuthResponseCode.SUCCESS,
          AUTH_MESSAGES.PASSWORD.RESET_SUCCESS,
          language,
        );
      }
      return buildResponse(
        this.i18nService,
        AuthResponseCode.SYSTEM_ERROR,
        AUTH_MESSAGES.PASSWORD.RESET_FAILED,
        language,
      );
    }
  }

  /**
   * Validate that credentials match
   */
  private validatePasswordsMatch(resetPasswordDto: ResetPasswordDto): boolean {
    return resetPasswordDto.newPassword === resetPasswordDto.confirmPassword;
  }

  /**
   * Validate that new credentials != old credentials
   */
  private isNewPasswordSameAsOld(resetPasswordDto: ResetPasswordDto): boolean {
    return resetPasswordDto.newPassword === resetPasswordDto.oldPassword;
  }


  /**
   * Submit reset request to ISAM
   */
  private async submitPasswordReset(
    resetPasswordDto: ResetPasswordDto,
    tokenData: { stateId: string; cookies: string; jti: string },
  ) {
    const { stateId, cookies } = tokenData;

    // Construct the reset credentials URL
    const resetPasswordUrl = `${this.loginBaseUrl}/mga/sps/authsvc?StateId=${stateId}`;

    // Prepare form data for credentials reset
    const formData = new URLSearchParams();
    formData.append('operation', 'verify');
    formData.append('password', resetPasswordDto.oldPassword);
    formData.append('newPass', resetPasswordDto.newPassword);
    formData.append('confPass', resetPasswordDto.confirmPassword);
    formData.append('submit', 'login');

    const requestConfig = {
      headers: {
        Accept: 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'Accept-Language': 'en-GB,en-US;q=0.9,en;q=0.8',
        'Cache-Control': 'no-cache',
        Connection: 'keep-alive',
        'Content-Type': 'application/x-www-form-urlencoded',
        Cookie: cookies,
        Host: 'isam-digital-test.hodomain.local:448',
        Origin: 'https://isam-digital-test.hodomain.local:448',
        Referer: 'https://isam-digital-test.hodomain.local:448',
      },
      httpsAgent: this.agent,
    };

    // Send credentials reset request
    return await lastValueFrom(
      this.httpService.post(
        resetPasswordUrl,
        formData.toString(),
        requestConfig,
      ),
    );
  }

  private async processResetResponse(
    resetResponse: any,
    tokenData: { stateId: string; cookies: string; jti: string },
    language: UserLanguage,
  ): Promise<AuthResponseDto> {
    const { stateId, jti } = tokenData;

    // Check for specific responses
    if (this.isInvalidOldPassword(resetResponse.data)) {
      return buildResponse(
        this.i18nService,
        AuthResponseCode.INVALID_CREDENTIALS,
        AUTH_MESSAGES.PASSWORD.INVALID_OLD_PASSWORD,
        language,
      );
    }

    if (this.isPasswordComplexityError(resetResponse.data)) {
      return buildResponse(
        this.i18nService,
        AuthResponseCode.SYSTEM_ERROR,
        AUTH_MESSAGES.PASSWORD.COMPLEXITY,
        language,
      );
    }

    if (resetResponse.status === 302 || resetResponse.status === 200) {
      // Extract cookies from reset response
      const resetResponseCookies = resetResponse.headers['set-cookie'] || [];

      // Combine existing cookies with new cookies from reset response
      const combinedCookies = parseSetCookieHeaders(resetResponseCookies);

      // Check if user is already logged in from another client, which mean the credentials reset flow passed
      if (this.isAlreadyLoggedIn(resetResponse.data) || this.isRedirectToPortal(resetResponse.data)) {
        // Call logout to terminate the session asynchronously
        // Don't await as we don't need to wait for the response
        void this.performLogout(combinedCookies, tokenData);
        return buildResponse(
          this.i18nService,
          AuthResponseCode.SUCCESS,
          AUTH_MESSAGES.PASSWORD.RESET_SUCCESS,
          language,
        );
      }
      // Extract new stateId if present in the response
      let newStateId = stateId;
      try {
        const extractedStateId = this.stateIdParserService.parseStateIdFromHtml(resetResponse.data);
        if (extractedStateId) {
          newStateId = extractedStateId;
        }
      } catch (error) {
        this.logger.error('Could not extract new stateId from response', error);
        return buildResponse(
          this.i18nService,
          AuthResponseCode.SYSTEM_ERROR,
          AUTH_MESSAGES.PASSWORD.ISAM_RESET_FAILED,
          language,
        );
      }

      // Update Redis with the new cookies and stateId
      await updateRedisCache(jti, combinedCookies, newStateId, this.redisCacheService, this.REDIS_TOKEN_TTL);

      // Call logout to terminate the session asynchronously
      // Don't await as we don't need to wait for the response
      void this.performLogout(combinedCookies, tokenData);

      return buildResponse(
        this.i18nService,
        AuthResponseCode.SUCCESS,
        AUTH_MESSAGES.PASSWORD.RESET_SUCCESS,
        language,
      );
    }

    return buildResponse(
      this.i18nService,
      AuthResponseCode.SYSTEM_ERROR,
      AUTH_MESSAGES.PASSWORD.RESET_FAILED,
      language,
    );
  }


  /**
   * Check if response indicates invalid old credentials
   */
  private isInvalidOldPassword(responseData: string): boolean {
    return responseData && responseData.includes('errorMessage = "Invalid old password"');
  }

  /**
   * Check if response indicates complexity error
   */
  private isPasswordComplexityError(responseData: string): boolean {
    return responseData && responseData.includes('Password does not meet complexity requirements');
  }

  /**
   * Check if response indicates user is already logged in from another client
   */
  private isAlreadyLoggedIn(responseData: string): boolean {
    return responseData && responseData.includes('You are already logged in from another client');
  }

  private isRedirectToPortal(responseData: string): boolean {
    return responseData && (
      responseData.includes('window.location.href="../FBCCBB/portal') ||
      responseData.includes('window.location.href="../FBCCSIT/portal')
    );
  }

  /**
   * Logout the user after credentials reset using auth service logout method
   */
  private async performLogout(cookies: string, tokenData: any): Promise<void> {
    try {
      // Create a CurrentUser object with the available data
      const currentUser: CurrentUser = {
        cookies,
        jti: tokenData.jti,
        username: tokenData.username,
        company: tokenData.company,
      };

      // Use the auth service logout method which handles both REST portal and ISAM logout
      await this.authService.logout(currentUser);
      this.logger.log('User logged out after password reset using auth service');
    } catch (error) {
      this.logger.error('Logout after password reset failed', error);
    }
  }
}
