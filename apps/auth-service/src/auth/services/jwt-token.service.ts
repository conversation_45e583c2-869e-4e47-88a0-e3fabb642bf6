import { Injectable, UnauthorizedException, Logger } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { v4 as uuidv4 } from 'uuid';
import { RedisCacheService } from '@modules';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class JwtTokenService {
  private readonly TOKEN_EXPIRY: number;
  private readonly REDIS_TOKEN_TTL: number;
  private readonly JWT_SECRET: string;
  private readonly logger = new Logger(JwtTokenService.name);

  constructor(
    private jwtService: JwtService,
    private redisCacheService: RedisCacheService,
    private configService: ConfigService,
  ) {
    this.TOKEN_EXPIRY = this.configService.get<number>('jwt.tokenExpiry', 540);
    this.REDIS_TOKEN_TTL = this.configService.get<number>(
      'jwt.redisTokenTtl',
      600,
    );
    this.JWT_SECRET = this.configService.get<string>('JWT_SECRET');
  }

  /**
   * Generate a JWT token with cookies and stateId
   */
  async generateToken(
    cookies: string,
    stateId: string,
    additionalData: Record<string, any> = {},
  ): Promise<string> {
    try {
      // Generate a unique token ID
      const jti = uuidv4();

      // Create payload with cookies, stateId and any additional data
      const payload = {
        cookies,
        stateId,
        jti,
        ...additionalData,
      };

      // Sign the token
      const token = this.jwtService.sign(payload, {
        expiresIn: this.TOKEN_EXPIRY,
        secret: this.JWT_SECRET,
      });

      // Store token in Redis
      await this.redisCacheService.set(jti, payload, {
        ttl: this.REDIS_TOKEN_TTL,
      });

      return token;
    } catch (error) {
      this.logger.error('Failed to generate token', error);
      throw new Error('Failed to generate token');
    }
  }

  /**
   * Generate a refresh token
   */
  async generateRefreshToken(token: string): Promise<string> {
    try {
      // Verify and decode the original token
      const decoded = await this.decodeAndValidateToken(token);
      if (!decoded) {
        throw new UnauthorizedException(
          'Invalid token for refresh',
          'INVALID_TOKEN',
        );
      }

      // Check if the token is in Redis
      const jti = decoded.jti;
      const storedPayload = await this.redisCacheService.get(jti);

      if (!storedPayload) {
        throw new UnauthorizedException(
          'Token has been revoked or expired',
          'TOKEN_REVOKED',
        );
      }

      // Generate a new token with the same payload but new jti
      const newJti = uuidv4();
      const payload = {
        cookies: (storedPayload as any).cookies,
        stateId: (storedPayload as any).stateId,
        jti: newJti,
        userId: (storedPayload as any).userId,
        cif: (storedPayload as any).cif,
        username: (storedPayload as any).username,
        company: (storedPayload as any).company,
        industry: (storedPayload as any).industry,
        segment: (storedPayload as any).segment,
      };

      const newToken = this.jwtService.sign(payload, {
        expiresIn: this.TOKEN_EXPIRY,
        secret: this.JWT_SECRET,
      });

      // Store the new token in Redis
      await this.redisCacheService.set(newJti, payload, {
        ttl: this.REDIS_TOKEN_TTL,
      });

      // Invalidate the old token
      await this.redisCacheService.del(jti);

      return newToken;
    } catch (error) {
      this.handleTokenValidationError(error);
    }
  }

  /**
   * Check if a token is about to expire
   * Returns true if token expires within the next minute
   */
  async isTokenNearExpiry(token: string): Promise<boolean> {
    try {
      // Remove Bearer prefix if present
      token = this.stripBearerPrefix(token);

      // Decode token to get expiration time
      const decoded = this.jwtService.decode(token);
      if (!decoded || typeof decoded !== 'object' || !decoded.exp) {
        return false;
      }

      // Get current time and expiration time
      const currentTime = Math.floor(Date.now() / 1000);
      const expiryTime = decoded.exp;

      // Check if token will expire within the next minute (60 seconds)
      return expiryTime - currentTime < 60;
    } catch (error) {
      return false;
    }
  }

  /**
   * Verify a JWT token and return the decoded payload
   */
  async verifyToken(token: string): Promise<any> {
    try {
      return this.jwtService.verify(token);
    } catch (error) {
      return null;
    }
  }

  /**
   * Decode and verify a JWT token and validate it against Redis
   */
  async decodeAndValidateToken(token: string): Promise<any> {
    try {
      // Validate token exists
      if (!token) {
        throw new UnauthorizedException(
          'Token is null or undefined',
          'TOKEN_MISSING',
        );
      }

      // Remove Bearer prefix if present
      token = this.stripBearerPrefix(token);

      // Verify token
      const decoded = this.verifyJwtToken(token);

      // Validate JTI claim
      const jti = decoded?.jti;
      if (!jti) {
        throw new UnauthorizedException(
          'Token is missing jti claim',
          'TOKEN_MISSING_JTI',
        );
      }

      // Validate token in Redis
      await this.validateTokenInRedis(jti);

      return decoded;
    } catch (error) {
      this.handleTokenValidationError(error);
    }
  }

  /**
   * Just decode token without validation (optional utility)
   */
  decodeToken(token: string): any {
    try {
      return this.jwtService.decode(token, { json: true });
    } catch (e) {
      return null;
    }
  }

  /**
   * Strip Bearer prefix from token
   */
  stripBearerPrefix(token: string): string {
    return token.replace(/^Bearer\s+/i, '');
  }

  /**
   * Verify JWT token
   */
  private verifyJwtToken(token: string): any {
    try {
      return this.jwtService.verify(token, { ignoreExpiration: false });
    } catch (jwtError) {
      // Specific handling for JWT verification errors
      if (jwtError.name === 'TokenExpiredError') {
        throw new UnauthorizedException('Token has expired', 'TOKEN_EXPIRED');
      } else if (jwtError.name === 'JsonWebTokenError') {
        throw new UnauthorizedException(
          `JWT error: ${jwtError.message}`,
          'TOKEN_INVALID',
        );
      } else {
        throw new UnauthorizedException(
          `Token verification failed: ${jwtError.message}`,
          'TOKEN_VERIFICATION_FAILED',
        );
      }
    }
  }

  /**
   * Validate token in Redis
   */
  private async validateTokenInRedis(jti: string): Promise<void> {
    try {
      const storedPayload = await this.redisCacheService.get(jti);
      if (!storedPayload) {
        throw new UnauthorizedException(
          'Token has been revoked or expired in Redis',
          'TOKEN_REVOKED',
        );
      }
    } catch (redisError) {
      throw new UnauthorizedException(
        `Redis validation failed: ${redisError.message}`,
        'REDIS_ERROR',
      );
    }
  }

  /**
   * Handle token validation errors
   */
  private handleTokenValidationError(error: any): never {
    // Rethrow UnauthorizedException with additional properties to aid in debugging
    if (error instanceof UnauthorizedException) {
      // Add original error as a cause if available
      error.cause = error.cause || {};
      throw error;
    }

    // For any other types of errors
    throw new UnauthorizedException(
      `Invalid or expired token: ${error.message || 'Unknown error'}`,
      error.name || 'TOKEN_VALIDATION_ERROR',
    );
  }

  /**
   * Get token data from any flow that share token ex: reset, otp activation
   */
  async getTokenData(token: string): Promise<{
    stateId: string;
    cookies: string;
    attributes: any;
    jti: string;
    username?: string;
    company?: string;
  } | null> {
    try {
      // Decode and validate the token to get the JTI
      const decodedToken = await this.validateToken(token);
      const jti = decodedToken.jti;

      // Get the stored payload from Redis
      const storedPayload = jti ? await this.redisCacheService.get(jti) : null;

      // Type assertion for the Redis response
      const typedPayload = storedPayload as {
        stateId?: string;
        cookies?: string;
        jti?: string,
        attributes?: any;
        username?: string;
        company?: string;
      } | null;

      return {
        stateId: typedPayload.stateId,
        cookies: typedPayload.cookies,
        attributes: typedPayload.attributes,
        jti: jti,
        username: typedPayload.username,
        company: typedPayload.company,
      };
    } catch (error) {
      this.logger.error('Failed to get token data', error);
      return null;
    }
  }

  /**
   * Validate token data from any flow that share token ex: reset, otp activation
   */
  private async validateToken(token: string): Promise<any> {
    try {
      // Extract token from authorization header
      const actualToken = token.replace(/^Bearer\s+/i, '');
      return await this.verifyToken(actualToken);
    } catch (error) {
      this.logger.error('Invalid reset password token', error);
      throw new UnauthorizedException(
        'Invalid reset password token',
        'INVALID_TOKEN',
      );
    }
  }
}
