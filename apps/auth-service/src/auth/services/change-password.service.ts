import { Injectable, Logger, Inject, forwardRef } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { lastValueFrom } from 'rxjs';
import { I18nLookupService, RedisCacheService } from '@modules';
import { ChangePasswordDto } from '../dto/change-password.dto';
import { AuthResponseDto } from '../dto/auth-response.dto';
import { AuthResponseCode } from '../types/auth-response.types';
import { AUTH_MESSAGES } from '../constants/auth-messages.constants';
import { UserLanguage } from '@enums';
import { StateIdParserService } from './stateIdParserService';
import { parseSetCookieHeaders, updateRedisCache } from '../utils/cookie.utils';
import { buildResponse } from '../utils/response.utils';
import { CurrentUser } from '@types';
import { AuthService } from './auth.service';
import * as https from 'https';

@Injectable()
export class ChangePasswordService {
  private readonly logger: Logger;
  private readonly loginBaseUrl: string;
  private readonly agent: https.Agent;
  private readonly REDIS_TOKEN_TTL: number;

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
    private readonly stateIdParserService: StateIdParserService,
    private readonly i18nService: I18nLookupService,
    private readonly redisCacheService: RedisCacheService,
    @Inject(forwardRef(() => AuthService))
    private readonly authService: AuthService,
  ) {
    this.logger = new Logger(ChangePasswordService.name);
    this.agent = new https.Agent({ rejectUnauthorized: false });
    this.loginBaseUrl = this.configService
      .getOrThrow('ISAM_BASE_URL')
      .replace(/\/(FBCCBB|FBCCSIT)$/, '');
    this.REDIS_TOKEN_TTL = this.configService.get<number>('REDIS_TOKEN_TTL', 600);
  }

  /**
   * Change user credentials
   */
  async changePassword(
    changePasswordDto: ChangePasswordDto,
    currentUser: CurrentUser,
    language: UserLanguage = UserLanguage['en-US'],
  ): Promise<AuthResponseDto> {
    const userIdentifier = `${currentUser.company}.${currentUser.username}`;
    this.logger.log(`Starting password change flow for user: ${userIdentifier}`);
    
    try {
      this.logger.debug('Step 1: Validating password requirements');
      
      // Validate credentials match
      if (!this.validatePasswordsMatch(changePasswordDto)) {
        this.logger.warn(`Password validation failed: New password and confirmation do not match for user: ${userIdentifier}`);
        return buildResponse(
          this.i18nService,
          AuthResponseCode.SYSTEM_ERROR,
          AUTH_MESSAGES.PASSWORD.MISMATCH,
          language,
        );
      }
      this.logger.debug('Password match validation passed');

      // Validate if new credential same as old credential
      if (this.isNewPasswordSameAsOld(changePasswordDto)) {
        this.logger.warn(`Password validation failed: New password is same as old password for user: ${userIdentifier}`);
        return buildResponse(
          this.i18nService,
          AuthResponseCode.SYSTEM_ERROR,
          AUTH_MESSAGES.PASSWORD.INVALID_NEW_PASSWORD,
          language,
        );
      }
      this.logger.debug('New password vs old password validation passed');

      this.logger.log(`Step 2: Loading change password form for user: ${userIdentifier}`);
      // Step 1: Load the change credentials form to get stateId
      const { stateId, cookies } = await this.loadChangePasswordForm(currentUser);
      this.logger.log(`Successfully loaded change password form with stateId: ${stateId}`);

      this.logger.log(`Step 3: Submitting password change request for user: ${userIdentifier}`);
      // Step 2: Submit the credentials change request
      const changeResponse = await this.submitPasswordChange(
        changePasswordDto,
        stateId,
        cookies,
        currentUser,
      );
      this.logger.log('Password change request submitted successfully');

      this.logger.log(`Step 4: Processing password change response for user: ${userIdentifier}`);
      // Process the response
      return this.processChangePasswordResponse(
        changeResponse,
        currentUser,
        language,
      );
    } catch (error) {
      this.logger.error(`Change password flow failed for user: ${userIdentifier}`, {
        error: error.message,
        stack: error.stack,
        userIdentifier,
        language
      });
      return buildResponse(
        this.i18nService,
        AuthResponseCode.SYSTEM_ERROR,
        AUTH_MESSAGES.PASSWORD.CHANGE_FAILED,
        language,
      );
    }
  }

  /**
   * Step 1: Load change form to get stateId
   */
  private async loadChangePasswordForm(currentUser: CurrentUser): Promise<{
    stateId: string;
    cookies: string;
  }> {
    const changePasswordUrl = `${this.loginBaseUrl}/mga/sps/authsvc?PolicyId=urn:ibm:security:authentication:asf:changePassword`;
    
    // Prepare the user identifier (username + company)
    const userIdentifier = `${currentUser.company}.${currentUser.username}`;
    this.logger.debug(`Preparing to load change password form for user: ${userIdentifier}`);
    this.logger.debug(`Change password URL: ${changePasswordUrl}`);
    
    const formData = new URLSearchParams();
    formData.append('iv-user', userIdentifier);
    this.logger.debug(`Form data prepared with user identifier: ${userIdentifier}`);

    const requestConfig = {
      headers: {
        Accept: 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'en-GB,en-US;q=0.9,en;q=0.8',
        'Cache-Control': 'max-age=0',
        Connection: 'keep-alive',
        'Content-Type': 'application/x-www-form-urlencoded',
        Cookie: currentUser.cookies || '',
        Origin: this.loginBaseUrl,
        Referer: `${this.loginBaseUrl}/FBCCUAT/CUST/portal/screen/CustomerSystemFeaturesScreen?option=PROFILE_MAINTENANCE`,
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'same-origin',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
      },
      httpsAgent: this.agent,
    };

    this.logger.debug('Sending HTTP request to load change password form');
    const response = await lastValueFrom(
      this.httpService.post(changePasswordUrl, formData.toString(), requestConfig),
    );
    this.logger.debug(`Received response from change password form load - Status: ${response.status}`);

    // Extract stateId from response
    this.logger.debug('Extracting stateId from change password form response');
    const stateId = this.stateIdParserService.parseStateIdFromHtml(response.data);
    if (!stateId) {
      this.logger.error('Failed to extract stateId from change password form response');
      throw new Error('Could not extract stateId from change password form');
    }
    this.logger.debug(`Successfully extracted stateId: ${stateId}`);

    // Extract cookies from response and combine with existing cookies
    const responseCookies = response.headers['set-cookie'] || [];
    this.logger.debug(`Received ${responseCookies.length} cookies from change password form response`);
    
    const combinedCookies = parseSetCookieHeaders([
      ...currentUser.cookies?.split(';') || [],
      ...responseCookies,
    ]);
    this.logger.debug('Successfully combined existing and new cookies');

    this.logger.log('Successfully loaded change password form', { 
      stateId,
      userIdentifier: `${currentUser.company}.${currentUser.username}`,
      cookieCount: responseCookies.length
    });

    return {
      stateId,
      cookies: combinedCookies,
    };
  }

  /**
   * Step 2: Submit change request
   */
  private async submitPasswordChange(
    changePasswordDto: ChangePasswordDto,
    stateId: string,
    cookies: string,
    currentUser: CurrentUser,
  ) {
    const userIdentifier = `${currentUser.company}.${currentUser.username}`;
    const changePasswordUrl = `${this.loginBaseUrl}/mga/sps/authsvc?StateId=${stateId}`;
    
    this.logger.debug(`Preparing password change submission for user: ${userIdentifier}`);
    this.logger.debug(`Password change submission URL: ${changePasswordUrl}`);

    const formData = new URLSearchParams();
    formData.append('password', changePasswordDto.oldPassword);
    formData.append('newPass', changePasswordDto.newPassword);
    formData.append('confPass', changePasswordDto.confirmPassword);
    formData.append('operation', 'verify');
    formData.append('Submit', 'Login');
    
    this.logger.debug('Password change form data prepared (passwords masked for security)');
    this.logger.debug(`Form fields: operation=verify, Submit=Login, stateId=${stateId}`);

    const requestConfig = {
      headers: {
        Accept: 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'en-GB,en-US;q=0.9,en;q=0.8',
        'Cache-Control': 'max-age=0',
        Connection: 'keep-alive',
        'Content-Type': 'application/x-www-form-urlencoded',
        Cookie: cookies,
        Origin: this.loginBaseUrl,
        Referer: `${this.loginBaseUrl}/mga/sps/authsvc?PolicyId=urn:ibm:security:authentication:asf:changePassword`,
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'same-origin',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
      },
      httpsAgent: this.agent,
    };

    this.logger.debug('Sending password change request to ISAM');
    const response = await lastValueFrom(
      this.httpService.post(changePasswordUrl, formData.toString(), requestConfig),
    );
    
    this.logger.debug(`Password change request completed - Status: ${response.status}`);
    this.logger.debug('Password change response received, proceeding to analysis');
    
    return response;
  }

  /**
   * Process the change credentials response
   */
  private async processChangePasswordResponse(
    response: any,
    currentUser: CurrentUser,
    language: UserLanguage,
  ): Promise<AuthResponseDto> {
    const userIdentifier = `${currentUser.company}.${currentUser.username}`;
    this.logger.debug(`Processing password change response for user: ${userIdentifier}`);
    this.logger.debug(`Response status: ${response.status}, Content-Type: ${response.headers['content-type']}`);
    
    // Check for specific error responses
    this.logger.debug('Checking for invalid old password error');
    if (this.isInvalidOldPassword(response.data)) {
      this.logger.warn(`Password change failed: Invalid old password for user: ${userIdentifier}`);
      await this.updateUserSessionData(response, currentUser);
      return buildResponse(
        this.i18nService,
        AuthResponseCode.INVALID_CREDENTIALS,
        AUTH_MESSAGES.PASSWORD.INVALID_OLD_PASSWORD,
        language,
      );
    }

    this.logger.debug('Checking for complexity error');
    if (this.isPasswordComplexityError(response.data)) {
      this.logger.warn(`Password change failed: Password complexity requirements not met for user: ${userIdentifier}`);
      await this.updateUserSessionData(response, currentUser);
      return buildResponse(
        this.i18nService,
        AuthResponseCode.SYSTEM_ERROR,
        AUTH_MESSAGES.PASSWORD.COMPLEXITY,
        language,
      );
    }

    this.logger.debug('Checking for change success indicators');
    if (this.isPasswordChangeSuccess(response.data)) {
      this.logger.log(`Password change successful for user: ${userIdentifier}`);
      this.logger.log('Initiating post-change logout process');
      
      // Call logout method asynchronously after successful change
      this.performLogout(currentUser, language).catch(error => {
        this.logger.error(`Failed to logout after change for user: ${userIdentifier}`, {
          error: error.message,
          userIdentifier
        });
      });
      
      return buildResponse(
        this.i18nService,
        AuthResponseCode.SUCCESS,
        AUTH_MESSAGES.PASSWORD.CHANGE_SUCCESS,
        language,
      );
    }

    // If there's an error but credentials might have been changed, update session data
    this.logger.warn(`Password change result unclear for user: ${userIdentifier}, updating session data as precaution`);
    await this.updateUserSessionData(response, currentUser);

    this.logger.error(`Password change failed with unknown error for user: ${userIdentifier}`);
    return buildResponse(
      this.i18nService,
      AuthResponseCode.SYSTEM_ERROR,
      AUTH_MESSAGES.PASSWORD.CHANGE_FAILED,
      language,
    );
  }

  /**
   * Update user session data in Redis with new cookies and stateId
   */
  private async updateUserSessionData(response: any, currentUser: CurrentUser): Promise<void> {
    const userIdentifier = `${currentUser.company}.${currentUser.username}`;
    this.logger.debug(`Updating session data for user: ${userIdentifier}`);
    
    try {
      // Extract new cookies from response
      const responseCookies = response.headers['set-cookie'] || [];
      this.logger.debug(`Found ${responseCookies.length} cookies in response headers`);
      
      if (responseCookies.length === 0) {
        this.logger.debug('No new cookies to update, skipping session data update');
        return; // No new cookies to update
      }

      // Combine existing cookies with new cookies
      const existingCookieCount = currentUser.cookies?.split(';').length || 0;
      const combinedCookies = parseSetCookieHeaders([
        ...currentUser.cookies?.split(';') || [],
        ...responseCookies,
      ]);
      this.logger.debug(`Combined ${existingCookieCount} existing cookies with ${responseCookies.length} new cookies`);

      // Try to extract new stateId if present
      let newStateId: string | null = null;
      this.logger.debug('Attempting to extract new stateId from response');
      try {
        newStateId = this.stateIdParserService.parseStateIdFromHtml(response.data);
        if (newStateId) {
          this.logger.debug(`Successfully extracted new stateId: ${newStateId}`);
        } else {
          this.logger.debug('No stateId found in response');
        }
      } catch (error) {
        this.logger.warn(`Could not extract new stateId from response for user: ${userIdentifier}`, {
          error: error.message,
          userIdentifier
        });
      }

      // Update Redis cache with new session data
      if (currentUser.jti) {
        this.logger.debug(`Updating Redis cache for JTI: ${currentUser.jti}`);
        await updateRedisCache(
          currentUser.jti,
          combinedCookies,
          newStateId || '', // Use empty string if no new stateId
          this.redisCacheService,
          this.REDIS_TOKEN_TTL,
        );
        this.logger.log(`Successfully updated session data in Redis for user: ${userIdentifier}`, {
          jti: currentUser.jti,
          hasNewStateId: !!newStateId,
          cookieCount: responseCookies.length
        });
      } else {
        this.logger.warn(`No JTI found for user: ${userIdentifier}, cannot update Redis cache`);
      }
    } catch (error) {
      this.logger.error(`Failed to update session data for user: ${userIdentifier}`, {
        error: error.message,
        stack: error.stack,
        userIdentifier,
        jti: currentUser.jti
      });
    }
  }

  /**
   * Validate that credentials match
   */
  private validatePasswordsMatch(changePasswordDto: ChangePasswordDto): boolean {
    return changePasswordDto.newPassword === changePasswordDto.confirmPassword;
  }

  /**
   * Validate that new credentials != old credentials
   */
  private isNewPasswordSameAsOld(changePasswordDto: ChangePasswordDto): boolean {
    return changePasswordDto.newPassword === changePasswordDto.oldPassword;
  }

  /**
   * Check if response indicates invalid old credentials
   */
  private isInvalidOldPassword(responseData: string): boolean {
    if (!responseData) {
      return false;
    }
    
    // Check for various invalid old credential error patterns
    const invalidPasswordPatterns = [
      'errorMessage = "Invalid old password"',
      'Invalid old password. For assistance',
      'Invalid old password. For assisitance', // Handle typo in actual response
      'Invalid old password',
      'old password is incorrect',
      'current password is invalid'
    ];
    
    return invalidPasswordPatterns.some(pattern => 
      responseData.toLowerCase().includes(pattern.toLowerCase())
    );
  }

  /**
   * Check if response indicates complexity error
   */
  private isPasswordComplexityError(responseData: string): boolean {
    return responseData && responseData.includes('Password does not meet complexity requirements');
  }

  /**
   * Check if response indicates successful change
   */
  private isPasswordChangeSuccess(responseData: string): boolean {
    if (!responseData) {
      this.logger.debug('Response data is empty, not a success');
      return false;
    }
    
    // Check for various success indicators
    const successPatterns = [
      'Password changed successfully',
      'window.location.href="../FBCCBB/portal',
      'window.location.href="../FBCCSIT/portal',
      // Handle the specific HTML/JavaScript response indicating success
      'window.location.href="../../pkmslogout"',
      'document.cookie = "IV_JCT=%2Fmga; path=/; secure"'
    ];
    
    for (const pattern of successPatterns) {
      if (responseData.includes(pattern)) {
        this.logger.debug(`Password change success detected using pattern: "${pattern}"`);
        return true;
      }
    }
    
    this.logger.debug('No success patterns found in response data');
    return false;
  }

  /**
   * Perform logout after successful credentials change
   */
  private async performLogout(
    currentUser: CurrentUser,
    language: UserLanguage,
  ): Promise<void> {
    const userIdentifier = `${currentUser.company}.${currentUser.username}`;
    
    try {
      this.logger.log(`Performing post-change logout for user: ${userIdentifier}`);
      this.logger.debug('Creating logout user object with current session data');
      
      // Create a CurrentUser object with the required information
      const logoutUser: CurrentUser = {
        ...currentUser,
        username: currentUser.username,
        company: currentUser.company,
        cookies: currentUser.cookies,
        jti: currentUser.jti
      };
      
      this.logger.debug(`Calling auth service logout method for user: ${userIdentifier}`);
      // Call the auth service logout method
      await this.authService.logout(logoutUser, language);
      
      this.logger.log(`Successfully completed post-change logout for user: ${userIdentifier}`);
      this.logger.log('Change flow completed successfully with automatic logout');
    } catch (error) {
      this.logger.error(`Failed to perform post-change logout for user: ${userIdentifier}`, {
        error: error.message,
        stack: error.stack,
        userIdentifier,
        jti: currentUser.jti
      });
      // Don't throw the error to avoid affecting the change response
    }
  }
}
