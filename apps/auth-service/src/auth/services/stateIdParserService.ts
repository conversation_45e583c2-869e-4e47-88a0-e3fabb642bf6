import { <PERSON><PERSON><PERSON> } from "jsdom"
import { ConfigService } from "@nestjs/config"
import { HttpStatus, Injectable, Logger } from "@nestjs/common"
import axios from "axios"

@Injectable()
export class StateIdParserService {
  private readonly baseUrl: string;
  private readonly loginBaseUrl: string;
  private readonly logger = new Logger(StateIdParserService.name);

  constructor(private readonly configService: ConfigService) {
    this.baseUrl = this.configService.getOrThrow("ISAM_BASE_URL");
    this.loginBaseUrl = this.configService.getOrThrow("ISAM_BASE_URL").replace(/\/FBCCBB$/, '');
  }

  /**
   * Extract StateId from the portal HTML response
   */
  async extractStateId(requestHeaders?: any): Promise<any> {
    try {
      const url = `${this.baseUrl}/portal/`;
      const headers = requestHeaders || this.getDefaultHeaders();

      const response = await this.fetchPortalPage(url, headers);
      const stateId = this.parseStateIdFromHtml(response.data);

      return {
        stateId,
        response
      };
    } catch (error) {
      this.logger.error("Error extracting StateId:", error);
      throw error;
    }
  }

  /**
   * Parse StateId from HTML content
   * Handles cases where multiple forms with name="Login_Form" exist,
   * ensuring we extract the StateId from the correct form that contains it.
   */
  public parseStateIdFromHtml(html: string): string {
    try {
      // Step 1: Parse the HTML using JSDOM to create a DOM-like structure
      const dom = new JSDOM(html);
      
      // Step 2: Find ALL forms with name="Login_Form" (there might be multiple)
      // Using querySelectorAll instead of querySelector to get all matching forms
      const forms = dom.window.document.querySelectorAll('form[name="Login_Form"]');

      // Step 3: Validate that at least one Login_Form exists
      if (forms.length === 0) {
        throw new Error("Login form not found in the HTML response");
      }

      // Step 4: Search through all Login_Form elements to find the one with StateId
      // This is necessary because some pages have multiple forms with the same name:
      // - First form (sessionform): action="/mga/mga/sps/authsvc" (NO StateId)
      // - Second form (main form): action="/mga/sps/authsvc?StateId=..." (HAS StateId)
      let targetForm = null;
      let stateIdMatch = null;
      
      for (let i = 0; i < forms.length; i++) {
        const form = forms[i];
        
        // Step 5: Get the action URL, checking both lowercase and uppercase variants
        // Some HTML uses 'action' while others use 'ACTION'
        const actionUrl = form.getAttribute("action") || form.getAttribute("ACTION");
        
        if (actionUrl) {
          // Step 6: Use regex to search for StateId parameter in the action URL
          // Pattern matches: StateId=<value> where <value> continues until & or end of string
          const match = actionUrl.match(/StateId=([^&]+)/);
          
          // Step 7: If StateId is found in this form, this is our target form
          if (match && match[1]) {
            targetForm = form;
            stateIdMatch = match;
            break; // Stop searching once we find the form with StateId
          }
        }
      }

      // Step 8: Validate that we found a form containing StateId
      if (!targetForm || !stateIdMatch) {
        throw new Error("StateId not found in any Login_Form action URL");
      }

      // Step 9: Return the extracted StateId value (captured group 1 from regex)
      return stateIdMatch[1];
    } catch (error) {
      this.logger.error("Error parsing StateId from HTML:", error);
      throw error;
    }
  }

  /**
   * Get default headers for requests
   */
  private getDefaultHeaders() {
    return {
      "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
      "Accept-Language": "en-GB,en-US;q=0.9,en;q=0.8",
      "Cache-Control": "no-cache",
      "Connection": "keep-alive",
      "Pragma": "no-cache",
      "Sec-Fetch-Dest": "document",
      "Sec-Fetch-Mode": "navigate",
      "Sec-Fetch-Site": "none",
      "Sec-Fetch-User": "?1",
      "Upgrade-Insecure-Requests": "1",
      "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
      "sec-ch-ua": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"",
      "sec-ch-ua-mobile": "?0",
      "sec-ch-ua-platform": "macOS"
    }
      ;
  }

  /**
   * Fetch portal page
   */
  private async fetchPortalPage(url: string, headers: any) {
    const response = await axios.get(url, { headers });

    if (response.status !== HttpStatus.OK) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    return response;
  }
}
