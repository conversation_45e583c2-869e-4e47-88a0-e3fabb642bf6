import { Injectable, Logger } from '@nestjs/common';
import * as svgCaptcha from 'svg-captcha';
import { I18nLookupService, RedisCacheService } from '@modules';
import * as crypto from 'crypto';
import { ConfigService } from '@nestjs/config';
import { AuthResponseCode } from '@/auth/types/auth-response.types';
import { AuthResponseDto } from '@/auth/dto/auth-response.dto';
import { UserLanguage } from '@enums';
import { LoginDto } from '@/auth/dto/login.dto';
import { AUTH_MESSAGES } from '@/auth/constants/auth-messages.constants';
import { buildCaptchaResponse, buildResponse } from '../utils/response.utils';
import { CaptchaConfig } from '../interfaces/captcha-config.interface';

@Injectable()
export class CaptchaService {
  private readonly config: CaptchaConfig;
  private readonly REDIS_CAPTCHA_PREFIX = 'captcha:';
  private readonly REDIS_LOGIN_ATTEMPTS_PREFIX = 'login_attempts:';
  private readonly logger: Logger;

  constructor(
    private readonly redisCacheService: RedisCacheService,
    private readonly i18nService: I18nLookupService,
    private readonly configService: ConfigService,
  ) {
    this.logger = new Logger(CaptchaService.name);
    
    this.config = {
      ttl: this.configService.get<number>('CAPTCHA_TTL'),
      size: this.configService.get<number>('CAPTCHA_SIZE'),
      noise: this.configService.get<number>('CAPTCHA_NOISE'),
      color: this.configService.get<boolean>('CAPTCHA_COLOR'),
      maxAttempts: this.configService.get<number>('AUTH_MAX_ATTEMPTS'),
      threshold: this.configService.get<number>('AUTH_CAPTCHA_THRESHOLD'),
      attemptsExpiry: this.configService.get<number>('AUTH_ATTEMPT_TTL'),
    };

    this.logger.log('Captcha service initialized with config:', this.config);
  }

  /**
   * Generate a new captcha and store its solution in Redis.
   * @returns SVG image and an encrypted token for validation.
   */
  async generateCaptcha(): Promise<{ image: string; token: string }> {
    try {
      const captcha = svgCaptcha.create({
        size: this.config.size,
        noise: this.config.noise,
        color: this.config.color,
      });

      const token = crypto.randomBytes(32).toString('hex');
      const key = `${this.REDIS_CAPTCHA_PREFIX}${token}`;

      await this.redisCacheService.set(key, captcha.text, {
        ttl: this.config.ttl,
      });

      this.logger.debug(`Generated captcha with token: ${token}`);
      return { image: captcha.data, token };
    } catch (error) {
      this.logger.error('Failed to generate captcha:', error);
      throw error;
    }
  }

  /**
   * Validate a captcha solution using the token.
   * @returns true if valid, false otherwise.
   */
  async validateCaptcha(token: string, userInput: string): Promise<boolean> {
    try {
      const key = `${this.REDIS_CAPTCHA_PREFIX}${token}`;
      const solution = await this.redisCacheService.get(key);

      if (!solution) {
        this.logger.warn(`No captcha found for token: ${token}`);
        return false;
      }

      const isValid = (solution as string) === userInput;
      await this.redisCacheService.del(key);

      this.logger.debug(
        `Captcha validation result for token ${token}: ${isValid}`,
      );
      return isValid;
    } catch (error) {
      this.logger.error('Failed to validate captcha:', error);
      return false;
    }
  }

  /**
   * Creates a hashed key for tracking login attempts
   */
  createHashedLoginAttemptsKey(username: string): string {
    return `${this.REDIS_LOGIN_ATTEMPTS_PREFIX}${crypto
      .createHash('sha256')
      .update(username)
      .digest('hex')}`;
  }

  /**
   * Get the number of failed login attempts for a user
   */
  async getFailedAttempts(key: string): Promise<number> {
    try {
      const attempts = await this.redisCacheService.get(key);
      return attempts ? parseInt(<string>attempts, 10) : 0;
    } catch (error) {
      this.logger.error('Failed to get failed attempts:', error);
      return 0;
    }
  }

  /**
   * Reset failed login attempts for a user
   */
  async resetFailedAttempts(key: string): Promise<void> {
    try {
      await this.redisCacheService.del(key);
      this.logger.debug(`Reset failed attempts for key: ${key}`);
    } catch (error) {
      this.logger.error('Failed to reset attempts:', error);
    }
  }

  /**
   * Increment failed login attempts for a user
   */
  async incrementFailedAttempts(key: string): Promise<void> {
    try {
      const attempts = await this.getFailedAttempts(key);
      await this.redisCacheService.set(key, (attempts + 1).toString(), {
        ttl: this.config.attemptsExpiry,
      });
      this.logger.debug(
        `Incremented failed attempts for key ${key} to ${attempts + 1}`,
      );
    } catch (error) {
      this.logger.error('Failed to increment attempts:', error);
    }
  }

  /**
   * Build a response with captcha challenge
   */
  buildCaptchaResponse(
    code: AuthResponseCode,
    message: string,
    language: UserLanguage,
    captchaImage: string,
    captchaToken: string,
  ): AuthResponseDto {
    return {
      ...buildResponse(this.i18nService, code, message, language),
      captchaImage,
      captchaToken,
    } as any;
  }

  /**
   * Validates captcha challenge and handles failed attempts
   * @returns AuthResponseDto if validation fails, null if validation succeeds
   */
  async validateCaptchaChallenge(
    loginDto: LoginDto,
    language: UserLanguage,
  ): Promise<AuthResponseDto | null> {
    try {
      const username = loginDto.username;
      const redisKey = this.createHashedLoginAttemptsKey(username);
      const userAttempts = await this.getFailedAttempts(redisKey);

      // If account is locked, reset attempts
      if (userAttempts >= this.config.maxAttempts) {
        await this.resetFailedAttempts(redisKey);
        return buildResponse(
          this.i18nService,
          AuthResponseCode.ACCOUNT_LOCKED,
          AUTH_MESSAGES.LOGIN.LOCKED_ACCOUNT,
          language,
        );
      }

      // Validate captcha if threshold reached
      if (userAttempts >= this.config.threshold) {
        const captchaValid =
          loginDto.captchaToken && loginDto.captchaText
            ? await this.validateCaptcha(
                loginDto.captchaToken,
                loginDto.captchaText,
              )
            : false;

        if (!captchaValid) {
          const { image, token } = await this.generateCaptcha();
          return buildCaptchaResponse(
            this.i18nService,
            AuthResponseCode.CAPTCHA_REQUIRED,
            AUTH_MESSAGES.LOGIN.INVALID_CAPTCHA,
            language,
            image,
            token,
          );
        }
      }

      return null;
    } catch (error) {
      this.logger.error('Failed to validate captcha challenge:', error);
      return buildResponse(
        this.i18nService,
        AuthResponseCode.SYSTEM_ERROR,
        AUTH_MESSAGES.LOGIN.SYSTEM_ERROR,
        language,
      );
    }
  }
}
