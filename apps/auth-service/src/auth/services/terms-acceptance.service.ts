import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { I18nLookupService } from '@modules';
import { JwtTokenService } from './jwt-token.service';
import { IsamService } from './isam.service';
import { AuthResponseDto } from '../dto/auth-response.dto';
import { AuthResponseCode } from '../types/auth-response.types';
import { AUTH_MESSAGES } from '../constants/auth-messages.constants';
import { buildResponse } from '../utils/response.utils';
import { parseSetCookieHeaders } from '../utils/cookie.utils';
import { CurrentUser } from '@types';
import { UserLanguage } from '@enums';
import { OtpActivationService } from './otp-activation.service';
import { UserDetailsService } from '@/auth/services/user-details.service';
import { AuthenticationException } from '@/auth/exceptions/authentication.exception';

@Injectable()
export class TermsAcceptanceService {
  private readonly logger = new Logger(TermsAcceptanceService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly i18nService: I18nLookupService,
    private readonly jwtTokenService: JwtTokenService,
    private readonly isamService: IsamService,
    private readonly otpActivationService: OtpActivationService,
    private readonly userDetailsService: UserDetailsService,
  ) {
  }

  /**
   * Handle terms and conditions acceptance
   */
  async acceptTerms(
    currentUser: CurrentUser,
    language: UserLanguage = UserLanguage['en-US'],
  ): Promise<AuthResponseDto> {
    try {
      // Create custom payload for terms acceptance
      const customPayload = this.isamService.createRestPortalPayload(undefined, language);
      customPayload.requestData.tandcflag = 'true';
      customPayload.requestData.mode = 'accept_terms';

      // Call ISAM service with custom URL and payload
      const loginUrl = `${this.isamService.baseUrl}/restportal/login/`;

      // Simulate a successful initial response to skip first check
      const response = await this.isamService.performRestPortalAuthentication(
        {
          statusCode: '302', // simulation to skip first check
          status: 302, // simulation to skip first check
          headers: { 'set-cookie': [currentUser.cookies] },
        },
        language,
        customPayload,
        loginUrl,
      );

      // Extract data from response
      const responseData = JSON.parse(response.body);

      // Convert user cookies string to array format to combine it later with cookies from request
      const userCookiesArray = currentUser.cookies
        ? currentUser.cookies.split(';').map((cookie) => cookie.trim())
        : [];

      // Get cookies from response
      const responseCookies = this.extractHeadersAndCookies(response);

      // Combine cookies from user and response
      const collectedCookies = [...userCookiesArray, ...responseCookies];

      // Parse the combined cookies
      const parsedCookies = parseSetCookieHeaders(collectedCookies);

      // Check if this is a token registration step one response
      const otpResponse = await this.otpActivationService.checkAndHandleOtpActivationRequired(
        responseData,
        parsedCookies,
        'stateId',
        language,
      );
      if (otpResponse) {
        return otpResponse;
      }

      try {
        // before building the access token we need to checkEligibility
        const userDetails  =
          await this.userDetailsService.checkEligibility(parsedCookies, language);
        const userId = userDetails.userDetails?.userId;
        const cif = userDetails.userDetails?.customerReferences?.at(0)?.name; //name? use ID instead


        // Standard success response for terms acceptance
        // if we reach this line , this mean the user is eligible .
        const token = await this.jwtTokenService.generateToken(
          parsedCookies,
          null,
          {
            userId,
            cif,
            industry: userDetails?.t24CompanyProfile?.industry,
            segment: userDetails?.segment?.id
          },
        );

        return {
          responseCode: AuthResponseCode.SUCCESS,
          title: this.i18nService.translate(
            AUTH_MESSAGES.TERMS.ACCEPTED + '.title',
            language,
          ),
          message: this.i18nService.translate(
            AUTH_MESSAGES.TERMS.ACCEPTED + '.body',
            language,
          ),
          token,
          userData: responseData,
          userDetails: userDetails.userDetails,  // Include user details like normal login
          segment: userDetails.segment,           // Include segment like normal login
        };
      } catch (error) {
        // Handle AuthenticationException specifically
        if (error instanceof AuthenticationException) {
          this.logger.warn('User eligibility check failed', error);
          // Return the error response with proper formatting
          return {
            responseCode: error.responseCode,
            title: this.i18nService.translate(error.messageKey, language),
            // message key will be auth.login.not-eligible.terms
            // to indicate that he accepted the terms but fail as he is not eligible
            message: this.i18nService.translate(error.messageKey + '.terms', language),
            // details: error.details,
          };
        }

        // Log the error for debugging
        this.logger.error('User eligibility check failed with unexpected error', {
          error: error.message,
          stack: error.stack,
        });

        // Re-throw other errors to cascade them up
        throw error;
      }
    } catch (error) {
      this.logger.error('Terms acceptance failed', error);
      return buildResponse(
        this.i18nService,
        AuthResponseCode.SYSTEM_ERROR,
        AUTH_MESSAGES.TERMS.FAILED,
        language,
      );
    }
  }

  /**
   * Extract headers and cookies from response
   */
  private extractHeadersAndCookies(response: any): string[] {
    const headers = response.headers || {};
    const setCookieHeaders = headers['set-cookie'] || [];
    return Array.isArray(setCookieHeaders) ? setCookieHeaders : [setCookieHeaders];
  }
}