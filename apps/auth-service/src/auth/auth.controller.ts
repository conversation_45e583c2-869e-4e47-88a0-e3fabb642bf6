import { Body, Controller, Get, Headers, HttpCode, HttpStatus, Post, UseGuards, Param } from '@nestjs/common';
import { AuthService } from './services/auth.service';
import { LoginDto } from './dto/login.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
import { AuthResponseDto } from './dto/auth-response.dto';
import { getCurrentUser, I18nLookupService, JwtAuthGuard } from '@modules';
import { CurrentUser } from '@types';
import { AuthResponseCode } from './types/auth-response.types';
import { AUTH_MESSAGES } from './constants/auth-messages.constants';
import { parseLanguageHeader } from './utils/language.utils';
import { CaptchaService } from './services/captcha.service';
import { ForgetPasswordService } from './services/forget-password.service';
import { ChangePasswordService } from './services/change-password.service';
import { ForgetPasswordOptionsDto } from './dto/forget-password-options.dto';
import { ForgetPasswordSmsSubmitDto } from './dto/forget-password-sms-submit.dto';
import { SelectForgetPasswordOptionDto } from './dto/select-forget-password-option.dto';
import { ConfirmPhoneNumberDto } from './dto/confirm-phone-number.dto';
import { JwtTokenService } from '@/auth/services/jwt-token.service';
import { OtpActivationService } from './services/otp-activation.service';
import { OtpActivationStep1Dto } from './dto/otp-activation.dto';
import { OtpActivationStep2Dto } from './dto/otp-activation.dto';
import { OtpActivationStep3Dto } from './dto/otp-activation.dto';
import { HardTokenActivationStep1Dto, HardTokenActivationStep2Dto } from './dto/otp-activation.dto';
import { ValidateEmailDto } from '@/auth/dto/validate-email.dto';
import { ForgetPasswordOtpVerifyDto } from '@/auth/dto/forget-password-otp-verify.dto';
import { ForgetPasswordOtpResetDto } from '@/auth/dto/forget-password-otp-reset.dto';

@Controller()
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly i18nService: I18nLookupService,
    private readonly captchaService: CaptchaService,
    private readonly jwtTokenService: JwtTokenService,
    private readonly forgetPasswordService: ForgetPasswordService,
    private readonly changePasswordService: ChangePasswordService,
    private readonly otpActivationService: OtpActivationService,
  ) {
  }

  /**
   * User login endpoint.
   * Captcha fields are handled via LoginDto and returned in response if required.
   */
  @Post('login')
  @HttpCode(HttpStatus.OK)
  async login(
    @Body() loginDto: LoginDto,
    @Headers('authorization') terminationHeader?: string,
    @Headers('accept-language') language?: string,
  ): Promise<AuthResponseDto> {
    // Default to English if no language is provided or if the language is not supported
    const userLanguage = parseLanguageHeader(language);
    return this.authService.login(loginDto, terminationHeader, userLanguage);
  }

  @Post('dev/login')
  @HttpCode(HttpStatus.OK)
  async devLogin(
    @Body() loginDto: LoginDto,
    @Headers('accept-language') language?: string,
  ): Promise<AuthResponseDto> {
    const userLanguage = parseLanguageHeader(language);
    return this.authService.devLogin(loginDto, userLanguage);
  }

  @Post('logout')
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.OK)
  async logout(
    @getCurrentUser() currentUser: CurrentUser,
    @Headers('accept-language') language?: string,
  ): Promise<AuthResponseDto> {
    const userLanguage = parseLanguageHeader(language);
    return this.authService.logout(currentUser, userLanguage);
  }

  @Get('refresh-token')
  @HttpCode(HttpStatus.OK)
  async refreshToken(
    @Headers('authorization') authHeader: string,
    @Headers('accept-language') language?: string,
  ): Promise<AuthResponseDto> {
    const userLanguage = parseLanguageHeader(language);
    const token = authHeader ? this.jwtTokenService.stripBearerPrefix(authHeader) : null;

    try {
      const result = await this.authService.refreshToken(token);
      return {
        responseCode: AuthResponseCode.SUCCESS,
        message: this.i18nService.translate(
          AUTH_MESSAGES.TOKEN.REFRESH_SUCCESS,
          userLanguage,
        ),
        token: result.token,
        nearExpiry: result.nearExpiry,
      };
    } catch (error) {
      return {
        responseCode: AuthResponseCode.TOKEN_ERROR,
        message: this.i18nService.translate(
          AUTH_MESSAGES.TOKEN.REFRESH_FAILED,
          userLanguage,
        ),
      };
    }
  }

  @Post('reset-password')
  @HttpCode(HttpStatus.OK)
  async resetPassword(
    @Body() resetPasswordDto: ResetPasswordDto,
    @Headers('authorization') authHeader: string,
    @Headers('accept-language') language?: string,
  ): Promise<AuthResponseDto> {
    const userLanguage = parseLanguageHeader(language);
    const resetPasswordToken = authHeader ? this.jwtTokenService.stripBearerPrefix(authHeader) : null;

    return this.authService.resetPassword(
      resetPasswordDto,
      resetPasswordToken,
      userLanguage,
    );
  }

  @Post('accept-terms')
  @HttpCode(HttpStatus.OK)
  @UseGuards(JwtAuthGuard)
  async acceptTerms(
    @getCurrentUser() currentUser: CurrentUser,
    @Headers('authorization') token: string,
    @Headers('accept-language') language?: string,
  ): Promise<AuthResponseDto> {
    const userLanguage = parseLanguageHeader(language);
    return this.authService.acceptTerms(currentUser, userLanguage);
  }

  /**
   * Get a new captcha image and token.
   * Used to refresh the captcha.
   */
  @Get('captcha')
  async getCaptcha() {
    const { image, token } = await this.captchaService.generateCaptcha();
    return { image, token };
  }

  /**
   * Get available forget options for a user
   */
  @Post('forget-password/options')
  @HttpCode(HttpStatus.OK)
  async getForgetPasswordOptions(
    @Body() forgetPasswordOptionsDto: ForgetPasswordOptionsDto,
    @Headers('accept-language') language?: string,
  ) {
    const userLanguage = parseLanguageHeader(language);
    return this.forgetPasswordService.getForgetPasswordOptions(
      forgetPasswordOptionsDto,
      userLanguage,
    );
  }

  /**
   * Verify phone number for SMS-based reset
   * This endpoint initiates the SMS sending process
   */
  @Post('forget-password/select-option')
  @HttpCode(HttpStatus.OK)
  async selectOption(
    @Body() selectForgetPasswordOptionDto: SelectForgetPasswordOptionDto,
    @Headers('authorization') authHeader: string,
    @Headers('accept-language') language?: string,
  ): Promise<AuthResponseDto> {
    const userLanguage = parseLanguageHeader(language);
    const token1 = authHeader ? this.jwtTokenService.stripBearerPrefix(authHeader) : null;

    return this.forgetPasswordService.selectOption(
      selectForgetPasswordOptionDto,
      token1,
      userLanguage,
    );
  }

  /**
   * Confirm phone number and trigger SMS sending
   */
  @Post('forget-password/confirm-phone')
  @HttpCode(HttpStatus.OK)
  async confirmPhoneNumber(
    @Body() confirmPhoneDto: ConfirmPhoneNumberDto,
    @Headers('authorization') authHeader: string,
    @Headers('accept-language') language?: string,
  ): Promise<AuthResponseDto> {
    const userLanguage = parseLanguageHeader(language);
    const token2 = authHeader ? this.jwtTokenService.stripBearerPrefix(authHeader) : null;

    return this.forgetPasswordService.confirmPhoneNumber(
      confirmPhoneDto,
      token2,
      userLanguage,
    );
  }

  @Post('forget-password/validate-email')
  @HttpCode(HttpStatus.OK)
  async validateEmail(
    @Body() validateEmailDto: ValidateEmailDto,
    @Headers('authorization') authHeader: string,
    @Headers('accept-language') language?: string,
  ): Promise<AuthResponseDto> {
    const userLanguage = parseLanguageHeader(language);
    const token2 = authHeader ? this.jwtTokenService.stripBearerPrefix(authHeader) : null;

    return this.forgetPasswordService.validateEmail(
      validateEmailDto,
      token2,
      userLanguage,
    );
  }

  @Post('forget-password/submit-new-password')
  @HttpCode(HttpStatus.OK)
  async submitNewPassword(
    @Body() submitSmsDto: ForgetPasswordSmsSubmitDto,
    @Headers('authorization') authHeader: string,
    @Headers('accept-language') language?: string,
  ): Promise<AuthResponseDto> {
    const userLanguage = parseLanguageHeader(language);
    const token3 = authHeader ? this.jwtTokenService.stripBearerPrefix(authHeader) : null;

    return this.forgetPasswordService.submitNewPassword(
      submitSmsDto,
      token3,
      userLanguage,
    );
  }

  /**
   * OTP Activation Step 1 - Submit national ID and phone number
   */
  @Post('otp-activation/step1')
  @HttpCode(HttpStatus.OK)
  async otpActivationStep1(
    @Body() otpStep1Dto: OtpActivationStep1Dto,
    @Headers('authorization') authHeader: string,
    @Headers('accept-language') language?: string,
  ): Promise<AuthResponseDto> {
    const userLanguage = parseLanguageHeader(language);
    const token = authHeader ? this.jwtTokenService.stripBearerPrefix(authHeader) : null;
    return this.otpActivationService.completeSoftTokenStep1(otpStep1Dto, token, userLanguage);
  }

  /**
   * OTP Activation Step 2 - Process verification token
   */
  @Post('otp-activation/step2')
  @HttpCode(HttpStatus.OK)
  async otpActivationStep2(
    @Body() otpStep2Dto: OtpActivationStep2Dto,
    @Headers('authorization') authHeader: string,
    @Headers('accept-language') language?: string,
  ): Promise<AuthResponseDto> {
    const userLanguage = parseLanguageHeader(language);
    const token = authHeader ? this.jwtTokenService.stripBearerPrefix(authHeader) : null;
    return this.otpActivationService.completeSoftTokenStep2(otpStep2Dto, token, userLanguage);
  }

  /**
   * OTP Activation Step 3 - Submit OTP code
   */
  @Post('otp-activation/step3')
  @HttpCode(HttpStatus.OK)
  async otpActivationStep3(
    @Body() otpStep3Dto: OtpActivationStep3Dto,
    @Headers('authorization') authHeader: string,
    @Headers('accept-language') language?: string,
  ): Promise<AuthResponseDto> {
    const userLanguage = parseLanguageHeader(language);
    const otpActivationToken = authHeader ? this.jwtTokenService.stripBearerPrefix(authHeader) : null;

    return this.otpActivationService.completeSoftTokenStep3(
      otpStep3Dto,
      otpActivationToken,
      userLanguage,
    );
  }

  /**
   * Hard Token Activation Step 1 - Submit hard token OTP data
   */
  @Post('hard-token-activation/step1')
  @HttpCode(HttpStatus.OK)
  async hardTokenActivationStep1(
    @Body() hardTokenDto: HardTokenActivationStep1Dto,
    @Headers('authorization') authHeader: string,
    @Headers('accept-language') language?: string,
  ): Promise<AuthResponseDto> {
    const userLanguage = parseLanguageHeader(language);
    const token = authHeader ? this.jwtTokenService.stripBearerPrefix(authHeader) : null;
    return this.otpActivationService.completeHardTokenStep1(hardTokenDto, token, userLanguage);
  }

  /**
   * Hard Token Activation Step 2 - Submit hard token OTP code
   */
  @Post('hard-token-activation/step2')
  @HttpCode(HttpStatus.OK)
  async hardTokenActivationStep2(
    @Body() hardTokenStep2Dto: HardTokenActivationStep2Dto,
    @Headers('authorization') authHeader: string,
    @Headers('accept-language') language?: string,
  ): Promise<AuthResponseDto> {
    const userLanguage = parseLanguageHeader(language);
    const token = authHeader ? this.jwtTokenService.stripBearerPrefix(authHeader) : null;
    return this.otpActivationService.completeHardTokenStep2(hardTokenStep2Dto, token, userLanguage);
  }

  /**
   * NEW: Verify OTP code for forget flow
   * Payload: { otpCode: "123456" }
   * Header: Authorization: Bearer <token2>
   */
  @Post('forget-password/verify-otp')
  @HttpCode(HttpStatus.OK)
  async verifyOtpForForgetPassword(
    @Body() otpVerifyDto: ForgetPasswordOtpVerifyDto,
    @Headers('authorization') authHeader: string,
    @Headers('accept-language') language?: string,
  ): Promise<AuthResponseDto> {
    const userLanguage = parseLanguageHeader(language);
    const token2 = authHeader ? this.jwtTokenService.stripBearerPrefix(authHeader) : null;

    return this.forgetPasswordService.verifyOtpCode(
      otpVerifyDto,
      token2,
      userLanguage,
    );
  }

  /**
   * NEW: Submit new credentials for OTP forget flow (without temp)
   * Header: Authorization: Bearer <token3>
   */
  @Post('forget-password/otp-reset-password')
  @HttpCode(HttpStatus.OK)
  async submitOtpPasswordReset(
    @Body() otpResetDto: ForgetPasswordOtpResetDto,
    @Headers('authorization') authHeader: string,
    @Headers('accept-language') language?: string,
  ): Promise<AuthResponseDto> {
    const userLanguage = parseLanguageHeader(language);
    const token3 = authHeader ? this.jwtTokenService.stripBearerPrefix(authHeader) : null;

    return this.forgetPasswordService.submitOtpPasswordReset(
      otpResetDto,
      token3,
      userLanguage,
    );
  }

  /**
   * Change credentials endpoint for authenticated users
   */
  @Post('change-password')
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.OK)
  async changePassword(
    @Body() changePasswordDto: ChangePasswordDto,
    @getCurrentUser() currentUser: CurrentUser,
    @Headers('accept-language') language?: string,
  ): Promise<AuthResponseDto> {
    const userLanguage = parseLanguageHeader(language);
    return this.changePasswordService.changePassword(
      changePasswordDto,
      currentUser,
      userLanguage,
    );
  }
}
