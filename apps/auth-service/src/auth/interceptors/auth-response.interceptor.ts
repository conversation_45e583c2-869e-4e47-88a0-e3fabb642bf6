import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
  HttpStatus,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { AuthResponseCode } from '../types/auth-response.types';

@Injectable()
export class AuthResponseInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    return next.handle().pipe(
      map((data) => {
        // Check if the response is an AuthResponseDto with a non-success response code
        if (this.isAuthErrorResponse(data)) {
          const response = context.switchToHttp().getResponse();
          response.status(HttpStatus.BAD_REQUEST);
        }
        return data;
      }),
    );
  }

  private isAuthErrorResponse(data: any): boolean {
    // Check if data has responseCode property and it's not SUCCESS
    return (
      data &&
      typeof data === 'object' &&
      'responseCode' in data &&
      data.responseCode !== AuthResponseCode.SUCCESS
    );
  }
}
