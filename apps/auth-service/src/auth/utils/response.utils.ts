import { I18nLookupService } from '@modules';
import { AuthResponseCode } from '../types/auth-response.types';
import { AuthResponseDto } from '../dto/auth-response.dto';
import { UserLanguage } from '@enums';

/**
 * Build a standard AuthResponseDto.
 */
export const buildResponse = (
  i18nService: I18nLookupService,
  code: AuthResponseCode,
  messageKey: string,
  language: UserLanguage,
): AuthResponseDto => {
  return {
    responseCode: code,
    title: i18nService.translate(messageKey + '.title', language),
    message: i18nService.translate(messageKey + '.body', language),
  };
};

/**
 * Build a response with captcha challenge
 */
export const buildCaptchaResponse = (
  i18nService: I18nLookupService,
  code: AuthResponseCode,
  messageKey: string,
  language: UserLanguage,
  captchaImage: string,
  captchaToken: string,
): {
  responseCode: AuthResponseCode;
  title?: string;
  message: string;
  token?: string;
  userData?: any;
  segment?: any;
  terms?: string;
  tokenType?: string;
  terminationToken?: string;
  resetPasswordToken?: string;
  nearExpiry?: boolean;
  captchaRequired?: boolean;
  passwordHints?: string[];
  userDetails?: any;
  captchaImage: string;
  captchaToken: string
} => {
  return {
    ...buildResponse(i18nService, code, messageKey, language),
    captchaImage,
    captchaToken,
  };
};