import { UserLanguage } from '@enums';

/**
 * Parses the Accept-Language header and returns the corresponding UserLanguage enum value.
 * Defaults to English (en-US) if the language is not supported or not provided.
 * 
 * @param language - The Accept-Language header value
 * @returns The corresponding UserLanguage enum value
 */
export function parseLanguageHeader(language?: string): UserLanguage {
  if (!language) {
    return UserLanguage['en-US'];
  }

  // Check if the language is supported
  const supportedLanguages = Object.values(UserLanguage);
  const matchedLanguage = supportedLanguages.find(
    (supported) => language.toLowerCase().includes(supported.toLowerCase()),
  );

  return <UserLanguage>matchedLanguage || UserLanguage['en-US'];
}