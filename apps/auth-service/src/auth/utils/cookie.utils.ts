import { Logger } from '@nestjs/common';
import { RedisCacheService } from '@modules';

const logger = new Logger('CookieUtils');

/**
 * Parses an array of Set-Cookie headers and returns a consolidated cookie string
 * suitable for use in a Cookie header.
 *
 * @param headersArray - Array of Set-Cookie header strings
 * @returns Consolidated cookie string for use in Cookie header
 */
export function parseSetCookieHeaders(headersArray: string[]): string {
  try {
    const cookieMap: Record<string, string> = {};

    headersArray.forEach(header => {
      const cookies = header.split(';');
      const [keyValue] = cookies[0].split('=');
      const key = keyValue.trim();
      const value = cookies[0].substring(cookies[0].indexOf('=') + 1).trim();

      // If the same key already exists, override it
      cookieMap[key] = value;
    });

    return Object.entries(cookieMap)
      .map(([key, value]) => `${key}=${value}`)
      .join('; ');
  } catch (error) {
    logger.error('Failed to parse Set-Cookie headers', error);
    return '';
  }
}


export async function updateRedisCache(jti: string, cookies: string, stateId: string, redisCacheService: RedisCacheService, REDIS_TOKEN_TTL: number, attributes?: any): Promise<void> {
  if (jti) {
    try {
      const updatedPayload = {
        cookies: cookies,
        stateId: stateId,
        attributes: attributes,
        jti: jti,
      };

      await redisCacheService.set(
        jti,
        updatedPayload,
        { ttl: REDIS_TOKEN_TTL },
      );
      logger.debug(`Updated Redis cache for token ${jti} with new cookies and stateId`);
    } catch (redisError) {
      logger.error('Failed to update Redis', redisError);
    }
  }
}

