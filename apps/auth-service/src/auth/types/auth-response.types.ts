export enum AuthResponseCode {
  SUCCESS = 'SUCCESS',
  SYSTEM_ERROR = 'SYSTEM_ERROR',
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  CAPTCHA_REQUIRED = 'CAPTCHA_REQUIRED',
  ACCOUNT_LOCKED = 'ACCOUNT_LOCKED',
  TOKEN_ERROR = 'TOKEN_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  USER_CANCELLED = 'USER_CANCELLED',
  USER_OR_COMPANY_NOT_FOUND = 'USER_OR_COMPANY_NOT_FOUND',
  INVALID_TEMP_PASSWORD = 'INVALID_TEMP_PASSWORD',
  PASSWORD_COMPLEXITY = 'PASSWORD_COMPLEXITY',
  LOCKED_ACCOUNT = 'LOCKED_ACCOUNT',
  EXPIRED_PASSWORD = 'EXPIRED_PASSWORD',
  ACTIVE_SESSION = 'ACTIVE_SESSION',
  TERMS_REQUIRED = 'TERMS_REQUIRED',
  TOKEN_MISSING = 'TOKEN_MISSING',
  INVALID_TOKEN = 'INVALID_TOKEN',
  TOKEN_EXPIRED_OR_REVOKED = 'TOKEN_EXPIRED_OR_REVOKED',
  TOKEN_MISSING_JTI = 'TOKEN_MISSING_JTI',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  TOKEN_REVOKED = 'TOKEN_REVOKED',
  TOKEN_VERIFICATION_FAILED = 'TOKEN_VERIFICATION_FAILED',
  NOT_ELIGIBLE = 'NOT_ELIGIBLE',
  OTP_ACTIVATION_REQUIRED = 'OTP_ACTIVATION_REQUIRED',
  ISAM_ERROR = 'ISAM_ERROR',
}

export interface AuthResponseBase {
  responseCode: AuthResponseCode;
  message: string;
}

export interface LoginSuccessResponse extends AuthResponseBase {
  token: string;
  userData: any;
}

export interface TermsRequiredResponse extends AuthResponseBase {
  token: string;
  terms: string;
  tokenType: string;
}

export interface ActiveSessionResponse extends AuthResponseBase {
  terminationToken: string;
}

export interface PasswordResetResponse extends AuthResponseBase {
  resetPasswordToken: string;
}

export type LoginResponse =
  | LoginSuccessResponse
  | TermsRequiredResponse
  | ActiveSessionResponse
  | PasswordResetResponse
  | AuthResponseBase;
