/**
 * Information about an active session that requires termination
 */
export interface ActiveSessionResponse {
  terminationToken: string,
  message: string;
}

/**
 * Custom error type for specific ISAM scenarios
 */
export class IsamError extends Error {
  constructor(
    message: string,
    public readonly code: string,
    public readonly statusCode: number,
    public readonly activeSession?: ActiveSessionResponse,
  ) {
    super(message);
    this.name = 'IsamError';
  }
}

/**
 * Enumeration of ISAM error codes for specific scenarios
 */
export enum IsamErrorCodes {
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  LOCKED_ACCOUNT = 'LOCKED_ACCOUNT',
  EXPIRED_PASSWORD = 'EXPIRED_PASSWORD',
  SESSION_TERMINATION_FAILED = 'SESSION_TERMINATION_FAILED',
}