import { Lo<PERSON>, <PERSON>du<PERSON> } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { AuthController } from './auth.controller';
import { AuthService } from './services/auth.service';
import { JwtTokenService } from './services/jwt-token.service';
import { IsamService } from './services/isam.service';
import { StateIdParserService } from './services/stateIdParserService';
import { CaptchaService } from './services/captcha.service';
import { UserDetailsService } from './services/user-details.service';
import { TerminationService } from './services/termination.service';
import { JwtAuthModule } from '@modules';
import { ForgetPasswordService } from './services/forget-password.service';
import { ResetPasswordService } from './services/reset-password.service';
import { ChangePasswordService } from './services/change-password.service';
import { TermsAcceptanceService } from './services/terms-acceptance.service';
import { OtpActivationService } from './services/otp-activation.service';
import { AuthResponseInterceptor } from './interceptors/auth-response.interceptor';

@Module({
  imports: [
    HttpModule.register({
      timeout: 50000,
      maxRedirects: 5,
    }),
    JwtAuthModule.forRoot(), // Import JwtAuthModule to provide JwtAuthService
  ],
  controllers: [AuthController],
  providers: [
    Logger, // Add Logger as a provider
    AuthService,
    JwtTokenService,
    IsamService,
    StateIdParserService,
    CaptchaService,
    ForgetPasswordService,
    UserDetailsService,
    ResetPasswordService,
    ChangePasswordService,
    TerminationService,
    TermsAcceptanceService,
    OtpActivationService,
    // {
    //   provide: APP_INTERCEPTOR,
    //   useClass: AuthResponseInterceptor,
    // },
  ],
  exports: [AuthService, JwtTokenService, TerminationService],
})
export class AuthModule {}