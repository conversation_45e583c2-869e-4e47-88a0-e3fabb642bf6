export const AUTH_MESSAGES = {
  LOGIN: {
    SUCCESS: 'auth.login.success',
    INVALID_CREDENTIALS: 'auth.login.invalid-credentials',
    LOCKED_ACCOUNT: 'auth.login.locked-account',
    EXPIRED_PASSWORD: 'auth.login.expired-password',
    ACTIVE_SESSION: 'auth.login.active-session',
    TERMS_REQUIRED: 'auth.login.terms-required',
    TOKEN_ERROR: 'auth.login.token-error',
    SYSTEM_ERROR: 'auth.login.system-error',
    SESSION_TERMINATED: 'auth.login.session-terminated',
    LOGOUT_SUCCESS: 'auth.login.logout-success',
    INVALID_CAPTCHA: 'auth.login.invalid-captcha',
    INVALID_CREDENTIALS_CAPTCHA: 'auth.login.invalid-credentials-valid-captcha',
    CAPTCHA_REQUIRED: 'auth.login.captcha-required',
    NOT_ELIGIBLE: 'auth.login.not-eligible',
    OTP_ACTIVATION_REQUIRED: 'auth.login.otp-activation-required',
    OTP_ACTIVATION_FAILED: 'auth.login.otp-activation-failed',
    OTP_ACTIVATION_ISAM_ERROR: 'auth.login.otp-activation-isam-error',
    OTP_ACTIVATION_SUCCESS: 'auth.login.otp-activation-success',
    INVALID_OTP_CODE: 'auth.login.otp-activation-invalid-OTP-code',


  },
  PASSWORD: {
    RESET_SUCCESS: 'auth.password.reset-success',
    RESET_FAILED: 'auth.password.reset-failed',
    ISAM_RESET_FAILED: 'auth.password.reset-failed-isam',
    CHANGE_SUCCESS: 'auth.password.change-success',
    CHANGE_FAILED: 'auth.password.change-failed',
    MISMATCH: 'auth.password.mismatch',
    COMPLEXITY: 'auth.password.complexity',
    INVALID_OLD_PASSWORD: 'auth.password.invalid-old-password',
    INVALID_NEW_PASSWORD: 'auth.password.invalid-new-password',
  },
  TOKEN: {
    MISSING: 'auth.token.missing',
    INVALID: 'auth.token.invalid',
    REFRESH_FAILED: 'auth.token.refresh-failed',
    REFRESH_SUCCESS: 'auth.token.refresh-success',
  },
  TERMS: {
    ACCEPTED: 'auth.terms.accepted',
    FAILED: 'auth.terms.failed',
  },
  FORGET_PASSWORD: {
    OPTIONS_SUCCESS: 'auth.forget-password.options-success',
    OPTIONS_FAILED: 'auth.forget-password.options-failed',
    CAPTCHA_REQUIRED: 'auth.forget-password.captcha-required',
    INVALID_CAPTCHA: 'auth.forget-password.invalid-captcha',
    USER_OR_COMPANY_NOT_FOUND: 'auth.forget-password.user-or-company-not-found',
    INVALID_TOKEN: 'auth.forget-password.invalid-token',
    RESET_SUCCESS: 'auth.forget-password.reset-success',
    RESET_FAILED: 'auth.reset-password.failed',
    INVALID_TEMP_PASSWORD: 'auth.forget-password.invalid-temp-password',
    CONFIRM_NUMBER: 'auth.forget-password.confirm-number',
    OPTION_SELECTED: 'auth.forget-password.option-selected',
    OPTION_SELECTION_FAILED: 'auth.forget-password.option-selection-failed',
    RETRIEVE_MOBILE_NUMBER_FAILED: 'auth.forget-password.retrieve-mobile-number-failed',
    SMS_SENT: 'auth.forget-password.sms-sent',
    MAIL_SENT: 'auth.forget-password.mail-sent',
    SMS_FAILED: 'auth.forget-password.sms-failed',
    MAIL_FAILED: 'auth.forget-password.mail-failed',
    USER_CANCELLED: 'auth.forget-password.user-cancelled',
    INVALID_OPTION: 'auth.forget-password.invalid-option',
    ACCOUNT_LOCKED: 'auth.forget-password.account-locked',
  },
};
