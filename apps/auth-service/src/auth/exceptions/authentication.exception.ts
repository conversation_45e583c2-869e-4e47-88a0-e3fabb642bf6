import { HttpException, HttpStatus } from '@nestjs/common';
import { AuthResponseCode } from '../types/auth-response.types';
import { UserLanguage } from '@enums';
import { I18nLookupService } from '@modules';

export class AuthenticationException extends HttpException {
  constructor(
    public readonly responseCode: AuthResponseCode,
    public readonly messageKey: string,
    public readonly language: UserLanguage,
    public readonly statusCode: HttpStatus = HttpStatus.UNAUTHORIZED,
    public readonly details?: any,
  ) {
    // If i18n service is available, it will be injected later to translate the message
    super(messageKey, statusCode);
    this.name = 'AuthenticationException';
  }

  /**
   * Format the exception for response
   */
  formatResponse(i18nService?: I18nLookupService): any {
    return {
      responseCode: this.responseCode,
      message: i18nService 
        ? i18nService.translate(this.messageKey, this.language)
        : this.messageKey,
      details: this.details,
    };
  }
}