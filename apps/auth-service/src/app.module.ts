import { Global, Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { AuthModule } from './auth/auth.module';
import defaults from './config/defaults';
import {
  ApiAdapterClient,
  LookupEntity,
  LookupsModule,
  GlobalConfigModule,
  RedisModule,
} from '@modules';

@Global()
@Module({
  imports: [
    GlobalConfigModule.forRoot(defaults),
    RedisModule.forRootAsync(),
    JwtModule.registerAsync({
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get('jwt.secret'),
        signOptions: {
          expiresIn: configService.get('jwt.tokenExpiry') + 's', // Convert to string with 's' suffix
        },
      }),
      global: true,
    } as any),
    AuthModule,
    ApiAdapterClient,
    LookupsModule.forRoot([
      {
        name: LookupEntity.I18N_SERVER,
      },
      {
        name: LookupEntity.INDUSTRY_CODE,
      },
    ]),
  ],
})
export class AppModule {
}
