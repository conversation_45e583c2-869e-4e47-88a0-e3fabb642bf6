import { AppModule } from './app.module';
import { AppInitializer } from '@helpers';

/**
 * Entry point of the application. Instantiates the `Main` class and
 * triggers the initialization of the application.
 */
async function bootstrap() {
  const main = new AppInitializer();
  await main.initialize(AppModule, { prefix: 'v1/cards' }); // Start the application by calling initialize
}

bootstrap(); // Execute the bootstrap function to start the app