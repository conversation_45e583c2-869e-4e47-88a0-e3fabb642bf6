import { Module } from '@nestjs/common';
import { CreditCardsListingModule } from './listing/listing.module';
import { CreditCardsSettlementModule } from './settlement/settlement.module';
import { CreditCardsMovementsModule } from './movements/movements.module';
import { CreditCardsInstallmentsModule } from './installments/installments.module';
import { EStatementModule } from './estatement/estatement.module';

@Module({
  imports: [
    CreditCardsListingModule,
    CreditCardsSettlementModule,
    CreditCardsMovementsModule,
    CreditCardsInstallmentsModule,
    EStatementModule,
  ],
  controllers: [],
  providers: [],
})
export class CreditCardsModule {}
