import { CardOperationEnum, UserLanguage } from '@enums';
import { Injectable } from '@nestjs/common';
import { CurrentUser } from '@types';
import {
  CreditCardListService as GlobalCreditCardListService,
  UsersService,
} from '@modules';

@Injectable()
export class CreditCardsListService {
  constructor(
    private readonly creditCardListService: GlobalCreditCardListService,
    private readonly usersService: UsersService,
  ) {}

  async getCreditCardsList(
    currentUser: CurrentUser,
    userLanguage: UserLanguage,
  ) {
    const defaultCurrency =
      await this.usersService.getDefaultCurrency(currentUser);

    const cards = await this.creditCardListService.geCreditCardsList(
      CardOperationEnum.List,
      currentUser,
      {
        userLanguage,
        calculateEquivalents: true,
        defaultCurrency,
      },
    );

    return cards;
  }
}
