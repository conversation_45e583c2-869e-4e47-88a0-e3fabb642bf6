import { UserLanguage } from '@enums';
import { decryptUserJson } from '@helpers';
import {
  CreditCardDetailsService as CardDetailsService,
  UsersService,
} from '@modules';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { CreditCardId, CurrentUser } from '@types';

@Injectable()
export class CreditCardDetailsService {
  constructor(
    private readonly cardDetailsService: CardDetailsService,
    private readonly config: ConfigService,
    private readonly usersService: UsersService,
  ) {}

  async getCreditCardDetails(
    cardId: string,
    currentUser: CurrentUser,
    userLanguage: UserLanguage,
  ) {
    const cardInfo = decryptUserJson<CreditCardId>(
      cardId,
      this.config.get('aesSecret'),
      currentUser.userId,
    );
    const defaultCurrency =
      await this.usersService.getDefaultCurrency(currentUser);

    const cardDetails = await this.cardDetailsService.getDetails(
      cardInfo.sema,
      cardInfo.id,
      currentUser,
      {
        userLanguage,
        calculateEquivalents: true,
        defaultCurrency,
      },
    );
    return {
      id: encodeURIComponent(cardId),
      ...cardDetails,
      maskedCardNumber: cardDetails ? cardInfo.maskedCardNumber : null,
      companyName: currentUser?.company,
    };
  }
}
