import { Controller, Get, Param, UseGuards } from '@nestjs/common';
import { CreditCardsListService } from './services/list.service';
import { getCurrentUser, getUserLanguage, JwtAuthGuard } from '@modules';
import { CurrentUser } from '@types';
import { CreditCardDetailsService } from './services/details.service';
import { UserLanguage } from '@enums';

@Controller('credit-cards')
@UseGuards(JwtAuthGuard)
export class CreditCardsListingController {
  constructor(
    private readonly creditCardsListService: CreditCardsListService,
    private readonly creditCardDetailsService: CreditCardDetailsService,
  ) {}

  @Get()
  async getCreditCardsList(
    @getCurrentUser() currentUser: CurrentUser,
    @getUserLanguage() userLanguage: UserLanguage,
  ) {
    return this.creditCardsListService.getCreditCardsList(
      currentUser,
      userLanguage,
    );
  }

  @Get(':cardId')
  async getCreditCardDetails(
    @Param('cardId') cardId: string,
    @getCurrentUser() currentUser: CurrentUser,
    @getUserLanguage() userLanguage: UserLanguage,
  ) {
    return this.creditCardDetailsService.getCreditCardDetails(
      cardId,
      currentUser,
      userLanguage,
    );
  }
}
