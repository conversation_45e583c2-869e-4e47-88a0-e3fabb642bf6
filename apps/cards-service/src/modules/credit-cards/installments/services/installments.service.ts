import { EsbInstallmentsListItemDto } from '@dtos';
import { EsbRequestsEnum, UserLanguage } from '@enums';
import {
  applyPaginationOnArray,
  decryptUserJson,
  filterInstallments,
} from '@helpers';
import {
  ApiAdapterClientService,
  InstallmentsFormatterService,
} from '@modules';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { CreditCardId, CurrentUser } from '@types';
import { InstallmentsListQueryDto } from '../installments.dtos';

@Injectable()
export class CreditCardsInstallmentsService {
  constructor(
    private readonly apiClientService: ApiAdapterClientService,
    private readonly config: ConfigService,
    private readonly installmentsFormatterService: InstallmentsFormatterService,
  ) {}

  async getInstallmentsList(
    cardId: string,
    query: InstallmentsListQueryDto,
    currentUser: CurrentUser,
    userLanguage: UserLanguage,
  ) {
    try {
      const cardInfo = decryptUserJson<CreditCardId>(
        cardId,
        this.config.get<string>('aesSecret'),
        currentUser?.userId,
      );

      if (!cardInfo?.id || !cardInfo?.sema) {
        throw new Error('Invalid card information');
      }

      const installments = await this.apiClientService.esbRequest<
        EsbInstallmentsListItemDto[]
      >({
        requestId: EsbRequestsEnum.CREDIT_CARD_INSTALLMENTS,
        payload: {
          partyIdent: cardInfo.sema,
          cardId: cardInfo.id,
        },
      });

      if (!installments || !Array.isArray(installments)) {
        throw new Error('Invalid installments response from ESB');
      }

      const formattedIntallments = this.installmentsFormatterService.format(
        installments,
        { userLanguage },
      );

      const filteredInstallments = filterInstallments(
        formattedIntallments,
        query,
      );

      return {
        counts: filteredInstallments.count,
        rowDetails: applyPaginationOnArray(
          filteredInstallments.installments,
          {
            page: query.first + 1,
            limit: query.rows + 1,
          },
          false,
        ),
      };
    } catch (error) {
      console.error(
        `Error in getInstallmentsList for user=${currentUser?.userId}, cardId=${cardId}:`,
        error,
      );
      return {
        counts: 0,
        rowDetails: [],
      };
    }
  }
}
