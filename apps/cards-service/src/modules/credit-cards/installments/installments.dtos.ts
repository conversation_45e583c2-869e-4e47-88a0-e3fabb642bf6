import { IsOptional, IsIn, IsString, Matches } from 'class-validator';
import { Type } from 'class-transformer';
import { ListDefQueryDto } from '@dtos';
export class InstallmentsListQueryDto extends ListDefQueryDto {
  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  @Matches(/^\d{2}\/\d{2}\/\d{4}$/, {
    message: 'fromDate must be in format dd/mm/yyyy',
  })
  fromDate?: string;

  @IsOptional()
  @Matches(/^\d{2}\/\d{2}\/\d{4}$/, {
    message: 'toDate must be in format dd/mm/yyyy',
  })
  toDate?: string;

  @IsOptional()
  @Type(() => Number)
  fromAmount?: number;

  @IsOptional()
  @Type(() => Number)
  toAmount?: number;

  @IsOptional()
  @IsIn(['asc', 'desc'])
  sort?: 'asc' | 'desc' = 'desc';
}
