import { Controller, Get, Param, Query, UseGuards } from '@nestjs/common';
import { getCurrentUser, getUserLanguage, JwtAuthGuard } from '@modules';
import { CurrentUser } from '@types';
import { CreditCardsInstallmentsService } from './services/installments.service';
import { InstallmentsListQueryDto } from './installments.dtos';
import { UserLanguage } from '@enums';

@Controller('credit-cards/:cardId/installments')
@UseGuards(JwtAuthGuard)
export class CreditCardsIntallmentsController {
  constructor(
    private readonly installmentsService: CreditCardsInstallmentsService,
  ) {}

  @Get()
  async getInstallments(
    @Param('cardId') cardId: string,
    @Query() query: InstallmentsListQueryDto,
    @getCurrentUser() currentUser: CurrentUser,
    @getUserLanguage() userLanguage: UserLanguage,
  ) {
    return this.installmentsService.getInstallmentsList(
      cardId,
      query,
      currentUser,
      userLanguage,
    );
  }
}
