import { Controller, Get, Param, UseGuards } from '@nestjs/common';
import { getCurrentUser, getUserLanguage, JwtAuthGuard } from '@modules';
import { CurrentUser } from '@types';
import { CreditCardStatementService } from './estatement.service';
import { UserLanguage } from '@enums';

@UseGuards(JwtAuthGuard)
@Controller('credit-cards/:cardId/estatement')
export class CreditCardStatementController {
  constructor(private readonly estatementService: CreditCardStatementService) {}

  @Get()
  async getStatementURL(
    @Param('cardId') accountId: string,
    @getCurrentUser() currentUser: CurrentUser,
    @getUserLanguage() userLanguage: UserLanguage,
  ) {
    return this.estatementService.getStatementURL(
      accountId,
      currentUser,
      userLanguage,
    );
  }
}
