import { ApiAdapterClientService, FccClientService } from '@modules';
import { CreditCardId, CurrentUser } from '@types';
import {
  EsbRequestsEnum,
  EstatementRequestsEnum,
  RequestModuleEnum,
  UserLanguage,
} from '@enums';
import { Injectable } from '@nestjs/common';
import { EsbCreditCardDetailsDto } from '@dtos';
import { decryptUserJson } from '@helpers';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class CreditCardStatementService {
  constructor(
    private readonly clientService: ApiAdapterClientService,
    private readonly config: ConfigService,
  ) {}

  async getStatementURL(
    cardId: string,
    user: CurrentUser,
    userLanguage: UserLanguage,
  ): Promise<any> {
    const cardInfo = decryptUserJson<CreditCardId>(
      cardId,
      this.config.get('aesSecret'),
      user.userId,
    );

    const details =
      await this.clientService.esbRequest<EsbCreditCardDetailsDto>({
        requestId: EsbRequestsEnum.CREDIT_CARD_DETAILS,
        payload: {
          partyIdent: cardInfo.sema,
          cardId: cardInfo.id,
        },
      });

    return this.clientService.call<any>({
      module: RequestModuleEnum.ESTATEMENT,
      requestId: EstatementRequestsEnum.ENCRYPTION,
      user,
      payload: {
        account: details.groupAccount,
        accountType: 0,
      },
      options: { language: userLanguage },
    });
  }
}
