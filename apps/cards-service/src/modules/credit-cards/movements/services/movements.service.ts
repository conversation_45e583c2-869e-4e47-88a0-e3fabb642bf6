import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { FccClientService } from '@modules';
import { CreditCardId, CurrentUser } from '@types';
import { FccRequestsEnum } from '@enums';
import { CardsMovementsListRequestDto } from '@dtos';
import { decryptUserJson, validateDateRange } from '@helpers';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class CreditCardsMovementsService {
  private readonly logger = new Logger(CreditCardsMovementsService.name);

  constructor(
    private readonly clientService: FccClientService,
    private readonly config: ConfigService,
  ) {}

  async getMovements(
    cardId: string,
    //query: MovementsListRequestDto,
    query: CardsMovementsListRequestDto,
    currentUser: CurrentUser,
  ): Promise<any> {
    const { accountId, maskedCardNumber } = decryptUserJson<CreditCardId>(
      cardId,
      this.config.get('aesSecret'),
      currentUser.userId,
    );

    console.log(accountId);

    this.logger.log(
      `Fetching credit card movements for accountId: ${accountId}, userId: ${currentUser.userId}`,
    );

    // Validate date range to enforce the 3 months max by FCC
    const dateValidation = validateDateRange(query.dateFrom, query.dateTo);
    if (!dateValidation.isValid) {
      this.logger.warn(
        `Invalid date range for accountId: ${accountId}, error: ${dateValidation.error}`,
      );
      throw new BadRequestException(dateValidation.error);
    }

    const movements = await this.clientService.call<any>({
      requestId: FccRequestsEnum.CREDIT_CARD_MOVEMENTS_LIST,
      payload: {
        ...query,
        accountId,
      },
      user: currentUser,
    });

    this.logger.log(
      `Successfully retrieved ${movements?.rowDetails?.length || 0} movement records for account ${accountId}`,
    );

    const updatedMovements = {
      count: movements?.rowDetails?.length ?? 0,
      rowDetails:
        movements?.rowDetails?.map((item: any) => ({
          ...item,
          maskedCard: maskedCardNumber,
        })) ?? [],
    };

    return updatedMovements;
  }
}
