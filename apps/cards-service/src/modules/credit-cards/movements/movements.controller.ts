import { Controller, Get, Param, Query, UseGuards } from '@nestjs/common';
import { getCurrentUser, JwtAuthGuard } from '@modules';
import { CurrentUser } from '@types';
import { CardsMovementsListRequestDto } from '@dtos';
import { CreditCardsMovementsService } from './services/movements.service';

@Controller('credit-cards/:cardId/movements')
@UseGuards(JwtAuthGuard)
export class CreditCardsMovementsController {
  constructor(private readonly movementsService: CreditCardsMovementsService) {}

  @Get()
  async getCreditCardMovements(
    @Param('cardId') cardId: string,
    @Query() query: CardsMovementsListRequestDto,
    @getCurrentUser() currentUser: CurrentUser,
  ) {
    return this.movementsService.getMovements(cardId, query, currentUser);
  }
}
