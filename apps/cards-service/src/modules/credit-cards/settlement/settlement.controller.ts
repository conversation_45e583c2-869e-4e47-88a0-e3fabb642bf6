import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import { getCurrentUser, getUserLanguage, JwtAuthGuard } from '@modules';
import { CurrentUser } from '@types';
import { UserLanguage } from '@enums';
import { CreditCardSettlementService } from './services/settlement.service';
import { CreditCardSettlementSubmitRequestDto } from './settlement.dtos';

@Controller('credit-cards/settlement')
@UseGuards(JwtAuthGuard)
export class CreditCardsSettlementController {
  constructor(
    private readonly creditCardSettlementService: CreditCardSettlementService,
  ) {}

  @Post('initiate')
  async initiate(@getCurrentUser() currentUser: CurrentUser) {
    return this.creditCardSettlementService.initiate(currentUser);
  }

  @Post('submit')
  async submit(
    @Body() body: CreditCardSettlementSubmitRequestDto,
    @getCurrentUser() currentUser: CurrentUser,
    @getUserLanguage() userLanguage: UserLanguage,
  ) {
    return this.creditCardSettlementService.submit(
      body,
      currentUser,
      userLanguage,
    );
  }
}
