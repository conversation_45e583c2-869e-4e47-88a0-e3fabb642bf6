import { Injectable } from '@nestjs/common';
import { FccClientService, I18nLookupService, RedisTnxService } from '@modules';
import { CreditCardId, CurrentUser } from '@types';
import { FccFtGenericTnxResponseDto } from '@dtos';
import { CurrencyEnum, FccRequestsEnum, UserLanguage } from '@enums';
import { v4 as UUIDV4 } from 'uuid';
import {
  decryptUserJson,
  getTransactionStatusByCode,
  getTransactionSubStatusByCode,
  getTransfersReauthInputs,
} from '@helpers';
import { ConfigService } from '@nestjs/config';
import { CreditCardSettlementSubmitRequestDto } from '../settlement.dtos';

@Injectable()
export class CreditCardSettlementService {
  constructor(
    private readonly clientService: FccClientService,
    private readonly tnxService: RedisTnxService,
    private readonly i18n: I18nLookupService,
    private readonly config: ConfigService,
  ) {}

  async initiate(user: CurrentUser) {
    const idempotencyKey = UUIDV4();
    const ftxRecord = await this.clientService.call<FccFtGenericTnxResponseDto>(
      {
        requestId: FccRequestsEnum.CREDIT_CARD_SETTLEMENT_INITIATE,
        user,
        options: {
          idempotencyKey,
        },
      },
    );

    await this.tnxService.setTnx(user, 'FT:CRD', {
      ...ftxRecord,
      idempotencyKey,
    });

    return {
      refId: ftxRecord.refId,
      tnxId: ftxRecord.tnxId,
      reauth: getTransfersReauthInputs(ftxRecord),
    };
  }

  async submit(
    body: CreditCardSettlementSubmitRequestDto,
    user: CurrentUser,
    userLanguage: UserLanguage,
  ) {
    const { tnxId, refId, idempotencyKey } =
      await this.tnxService.getTnx<FccFtGenericTnxResponseDto>(user, 'FT:CRD');

    const cardInfo = decryptUserJson<CreditCardId>(
      decodeURIComponent(body.cardId),
      this.config.get<string>('aesSecret'),
      user.userId,
    );

    const transaction =
      await this.clientService.call<FccFtGenericTnxResponseDto>({
        requestId: FccRequestsEnum.CREDIT_CARD_SETTLEMENT_SUBMIT,
        payload: {
          tnxId,
          refId,
          beneficiaryAccount: cardInfo.maskedCardNumber,
          beneficiaryActCurCode: body.beneficiaryActCurCode ?? CurrencyEnum.EGP,
          beneficiaryAccountId: cardInfo.accountId,
          ...body,
        },
        user,
        options: {
          idempotencyKey,
        },
      });

    const status = getTransactionStatusByCode(transaction.tnxStatCode);
    const subStatus = getTransactionSubStatusByCode(transaction.subTnxStatCode);

    return {
      refId: transaction.refId,
      status,
      subStatus,
      message: this.i18n.translate(
        `transfers.submit.${subStatus}`,
        userLanguage,
      ),
    };
  }
}
