import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class CreditCardSettlementSubmitRequestDto {
  @IsString()
  @IsNotEmpty()
  cardId: string;

  @IsString()
  @IsNotEmpty()
  applicantActName: string;

  @IsString()
  @IsNotEmpty()
  applicantActNo: string;

  @IsString()
  @IsNotEmpty()
  applicantActCurCode: string;

  @IsString()
  @IsNotEmpty()
  ftAmt: string;

  @IsString()
  @IsOptional()
  ftCurCode: string;

  @IsString()
  @IsOptional()
  beneficiaryActCurCode: string;

  @IsString()
  @IsOptional()
  reauthPassword: string;
}
