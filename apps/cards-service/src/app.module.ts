import { Module } from '@nestjs/common';
import {
  ApiAdapterClient,
  CommonModule,
  GlobalConfigModule,
  JwtAuthModule,
  LookupEntity,
  LookupsModule,
  RedisModule,
} from '@modules';
import defaults from './config/defaults';
import { CreditCardsModule } from './modules/credit-cards/credit-cards.module';

@Module({
  imports: [
    GlobalConfigModule.forRoot(defaults),
    RedisModule.forRootAsync(),
    JwtAuthModule.forRoot(),
    ApiAdapterClient,
    LookupsModule.forRoot([
      {
        name: LookupEntity.I18N_SERVER,
      },
      {
        name: LookupEntity.CARD_PRODUCT,
      },
    ]),
    CommonModule,
    CreditCardsModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
