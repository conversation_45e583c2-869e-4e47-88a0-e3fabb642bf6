import { Module } from '@nestjs/common';
import {
  ApiAdapterClient,
  CommonModule,
  GlobalConfigModule,
  JwtAuthModule,
  LookupEntity,
  LookupsModule,
  RedisModule,
} from '@modules';
import defaults from './config/defaults';
import { BeneficiariesModule } from './modules/beneficiaries/beneficiaries.module';
import { TransfersModule } from './modules/transfers/transfers.module';
import { TransfersDynamicListEnum } from '@enums';
@Module({
  imports: [
    GlobalConfigModule.forRoot(defaults),
    LookupsModule.forRoot([
      {
        name: LookupEntity.I18N_SERVER,
      },
      {
        name: LookupEntity.CONTENT_LIST,
        filter: [TransfersDynamicListEnum.IPN_TRANSFER_REASONS],
      },
    ]),
    RedisModule.forRootAsync(),
    JwtAuthModule.forRoot(),
    CommonModule,
    ApiAdapterClient,
    BeneficiariesModule,
    TransfersModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
