import { Body, Controller, Get, Post, Query, UseGuards } from '@nestjs/common';
import { getCurrentUser, getUserLanguage, JwtAuthGuard } from '@modules';
import { CurrentUser } from '@types';
import { IpnTransferService } from './ipn.service';
import {
  IpnAccountsListQueryDto,
  IpnTransferSimulateRequestDto,
  IpnTransferSubmitRequestDto,
} from './ipn.dtos';
import { UserLanguage } from '@enums';

@Controller('transfer/ipn')
@UseGuards(JwtAuthGuard)
export class IpnTransferController {
  constructor(private readonly ipnTransferService: IpnTransferService) {}

  @Post('/initiate')
  async initiate(
    @getCurrentUser() currentUser: CurrentUser,
    @getUserLanguage() userLanguage: UserLanguage,
  ) {
    return this.ipnTransferService.initiate(currentUser, userLanguage);
  }

  @Post('/submit')
  async submit(
    @Body() body: IpnTransferSubmitRequestDto,
    @getCurrentUser() currentUser: CurrentUser,
    @getUserLanguage() userLanguage: UserLanguage,
  ) {
    return this.ipnTransferService.submit(body, currentUser, userLanguage);
  }

  @Post('/simulate')
  async simulate(
    @Body() body: IpnTransferSimulateRequestDto,
    @getCurrentUser() currentUser: CurrentUser,
  ) {
    return this.ipnTransferService.simulate(body, currentUser);
  }

  @Get('/accounts')
  async getAccounts(
    @Query() query: IpnAccountsListQueryDto,
    @getCurrentUser() currentUser: CurrentUser,
  ) {
    return this.ipnTransferService.getAccounts(query, currentUser);
  }

  @Get('/bank-list')
  async getBankList(
    @getCurrentUser() currentUser: CurrentUser,
    @getUserLanguage() userLanguage: UserLanguage,
  ) {
    return this.ipnTransferService.getBankList(currentUser, userLanguage);
  }
}
