import { Injectable } from '@nestjs/common';
import { BusinessValidationException } from '@exceptions';
import {
  ApiAdapterClientService,
  DynamicListsLookupService,
  I18nLookupService,
  RedisTnxService,
  TransactionDetailsFormatterService,
} from '@modules';
import { CurrentUser } from '@types';
import {
  EsbBankListListItemDto,
  FccFtGenericTnxResponseDto,
  FccGenericTnxResponseDto,
} from '@dtos';
import {
  EsbRequestsEnum,
  FccRequestsEnum,
  ProductCodeEnum,
  TransfersDynamicListEnum,
  UserLanguage,
} from '@enums';
import { v4 as UUIDV4 } from 'uuid';
import {
  IpnAccountsListQueryDto,
  IpnTransferSimulateRequestDto,
  IpnTransferSubmitRequestDto,
} from './ipn.dtos';
import {
  getIpnPaymentMethod,
  getTransactionStatusByCode,
  getTransactionSubStatusByCode,
  getTransfersReauthInputs,
} from '@helpers';
import { IpnConstants, IpnDefaults } from '@constants';

@Injectable()
export class IpnTransferService {
  constructor(
    private readonly clientService: ApiAdapterClientService,
    private readonly tnxService: RedisTnxService,
    private readonly i18n: I18nLookupService,
    private readonly listsLookup: DynamicListsLookupService,
    private readonly detailsFormatterService: TransactionDetailsFormatterService,
  ) {}

  async initiate(user: CurrentUser, userLanguage: UserLanguage) {
    const idempotencyKey = UUIDV4();

    const ftxRecord =
      await this.clientService.fccRequest<FccFtGenericTnxResponseDto>({
        requestId: FccRequestsEnum.IPN_TRANSFER_INITIATE,
        user,
        options: {
          idempotencyKey,
        },
      });
    await this.tnxService.setTnx(user, 'FT:IPN', {
      ...ftxRecord,
      idempotencyKey,
    });

    console.log(
      'LOGGING TRANSACTION REFERENCE',
      ftxRecord.refId,
      ftxRecord.tnxId,
    );

    return {
      refId: ftxRecord.refId,
      tnxId: ftxRecord.tnxId,
      reauth: getTransfersReauthInputs(ftxRecord),
      reasons: this.listsLookup.getValues(
        TransfersDynamicListEnum.IPN_TRANSFER_REASONS,
        userLanguage,
      ),
    };
  }

  async submit(
    body: IpnTransferSubmitRequestDto,
    user: CurrentUser,
    userLanguage: UserLanguage,
  ) {
    const { tnxId, refId, idempotencyKey, companyId } =
      await this.tnxService.getTnx<FccFtGenericTnxResponseDto>(user, 'FT:IPN');

    const payload = this.prepareSubmitPayload(body, user, companyId);

    const transaction = await this.clientService.fccRequest<any>({
      requestId: FccRequestsEnum.IPN_TRANSFER_SUBMIT,
      payload: {
        tnxId,
        refId,
        ...payload,
      },
      user,
      options: {
        idempotencyKey,
      },
    });

    const details =
      await this.clientService.fccRequest<FccGenericTnxResponseDto>({
        requestId: FccRequestsEnum.TRANSACTION_DETAILS,
        payload: {
          eventId: tnxId,
          productCode: ProductCodeEnum.FundTransfer,
        },
        user,
      });

    const status = getTransactionStatusByCode(details.tnxStatCode);
    const subStatus = getTransactionSubStatusByCode(details.subTnxStatCode);

    return {
      refId: details.refId,
      status,
      subStatus,
      details: this.detailsFormatterService.format(
        details,
        ProductCodeEnum.FundTransfer,
        userLanguage,
      ),
      data: transaction?.data,
      message: this.i18n.translate(
        `transfers.submit.${subStatus}`,
        userLanguage,
      ),
    };
  }

  /**
   * Helper method to Prepare the payload for IPN FT submit
   * Applies business logic and injects default values
   * @param body - DTO for submit IPN FT
   * @param companyId - Company ID from initiate response
   * @returns Prepared payload for submit IPN FT
   */
  private prepareSubmitPayload(
    body: IpnTransferSubmitRequestDto,
    user: CurrentUser,
    companyId: string,
  ) {
    const pmtMethod = getIpnPaymentMethod(body.clearingSystem);

    const payload = {
      clearingSystem: body.clearingSystem,
      pmtMethod,

      ...body,

      companyId,
      companyName: user.company,

      applicantName: user.username,
      applicantReference: user.cif,
      applicantActDescription: user.company,

      ftCurCode: IpnDefaults.FT_CUR_CODE,
      applicantActCurCode: IpnDefaults.APPLICANT_ACT_CUR_CODE,

      commissionFor: IpnDefaults.COMMISSION_FOR,
      commissionType: IpnDefaults.COMMISSION_TYPE,
      orderingCustomerBank:
        body.orderingCustomerBank || IpnDefaults.ORDERING_CUSTOMER_BANK_DEFAULT,
      chargeDebitAccount: body.applicantActNo,
      entity: body.entity ?? IpnConstants.DEFAULTS.ENTITY,
      brchCode: body.brchCode ?? IpnConstants.BANK.BRANCH_CODE,
    };

    return payload;
  }

  async simulate(body: IpnTransferSimulateRequestDto, user: CurrentUser) {
    const payload = {
      ...body,
      commissionCode: IpnDefaults.COMMISSION_CODE,
      commissionType: IpnDefaults.COMMISSION_TYPE,
      currency: IpnDefaults.CURRENCY,
      transferType: IpnDefaults.TRANSFER_TYPE,
    };

    const simulationResult = await this.clientService.fccRequest<any>({
      requestId: FccRequestsEnum.IPN_TRANSFER_SIMULATE,
      payload,
      user,
    });

    return simulationResult;
  }

  async getAccounts(query: IpnAccountsListQueryDto, user: CurrentUser) {
    const accountsList = await this.clientService.fccRequest<any>({
      requestId: FccRequestsEnum.IPN_ACCOUNTS_LIST,
      payload: {
        limit: query.limit ? parseInt(query.limit) : undefined,
        offset: query.offset ? parseInt(query.offset) : undefined,
        ccy_codes: query.ccyCodes,
      },
      user,
    });

    return accountsList;
  }

  async getBankList(currentUser: CurrentUser, userLanguage: UserLanguage) {
    const list = await this.clientService.esbRequest<EsbBankListListItemDto[]>({
      requestId: EsbRequestsEnum.IPN_BANK_LIST,
    });

    return list.map((bank) => {
      const { nameAr, nameEn, ...rest } = bank;

      return {
        name: userLanguage === UserLanguage['en-US'] ? nameEn : nameAr,
        ...rest,
      };
    });
  }

  /**
   * Get bank name by ID from bank-list for FCC
   * @param bankId - Bank ID to resolve
   * @returns bank name - en
   * @throws BusinessValidationException with EIPN13004 code for any failure resolving the bank name
   */
  async getBankNameById(
    bankId: string,
    userLanguage: UserLanguage = UserLanguage['en-US'],
  ): Promise<string> {
    try {
      const list = await this.clientService.esbRequest<
        EsbBankListListItemDto[]
      >({
        requestId: EsbRequestsEnum.IPN_BANK_LIST,
      });

      const bank = list.find((bank) => bank.bankId.toString() === bankId);

      if (!bank) {
        throw new BusinessValidationException('EIPN13004', userLanguage);
      }

      return bank.nameEn;
    } catch (error) {
      if (error instanceof BusinessValidationException) {
        throw error;
      }
      // Always return EIPN13004 for any bank resolution failure
      throw new BusinessValidationException('EIPN13004', userLanguage);
    }
  }
}
