import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsNumberString,
  IsEnum,
  IsArray,
} from 'class-validator';
import { Expose } from 'class-transformer';
import {
  IpnClearingSystems,
  IpnCommissionFor,
  IpnCommissionTypes,
} from '@enums';

export class IpnTransferSubmitRequestDto {
  // Beneficiary details
  @Expose()
  @IsString()
  @IsNotEmpty()
  beneficiaryName: string;

  @Expose()
  @IsString()
  @IsNotEmpty()
  beneficiaryAccount: string;

  @Expose()
  @IsString()
  @IsOptional()
  beneficiaryId?: string;

  @Expose()
  @IsEnum(IpnClearingSystems)
  @IsNotEmpty()
  clearingSystem: IpnClearingSystems;

  // Source account details
  @Expose()
  @IsString()
  @IsNotEmpty()
  applicantActNo: string;

  @Expose()
  @IsString()
  @IsNotEmpty()
  applicantActName: string;

  // Transfer details
  @Expose()
  @IsNumberString()
  @IsNotEmpty()
  ftAmt: string;

  @Expose()
  @IsString()
  @IsOptional()
  transferReason?: string;

  @Expose()
  @IsString()
  @IsOptional()
  orderingCustomerBank?: string;

  @Expose()
  @IsString()
  @IsOptional()
  reauthPassword?: string;

  @Expose()
  @IsString()
  @IsOptional()
  token?: string;

  @Expose()
  @IsEnum(IpnCommissionFor)
  @IsOptional()
  commissionFor?: IpnCommissionFor;

  @Expose()
  @IsEnum(IpnCommissionTypes)
  @IsOptional()
  commissionType?: IpnCommissionTypes;

  @Expose()
  @IsString()
  @IsOptional()
  entity?: string;

  @Expose()
  @IsString()
  @IsOptional()
  brchCode?: string;

  @Expose()
  @IsString()
  @IsOptional()
  userLanguage?: string;

  @Expose()
  @IsOptional()
  @IsArray()
  attachments?: string[];
}

export class IpnTransferSimulateRequestDto {
  @Expose()
  @IsString()
  @IsNotEmpty()
  fromAccount: string;

  @Expose()
  @IsNumberString()
  @IsNotEmpty()
  amount: string;

  @Expose()
  @IsString()
  @IsNotEmpty()
  currency: string;

  @Expose()
  @IsString()
  @IsNotEmpty()
  reason: string;

  @Expose()
  @IsString()
  @IsOptional()
  transferType?: string;

  @Expose()
  @IsString()
  @IsOptional()
  commissionCode?: string;

  @Expose()
  @IsString()
  @IsOptional()
  commissionType?: string;

  @Expose()
  @IsNumberString()
  @IsOptional()
  beneficiaryId?: string;
}

export class IpnAccountsListQueryDto {
  @Expose()
  @IsNumberString()
  @IsOptional()
  limit?: string;

  @Expose()
  @IsNumberString()
  @IsOptional()
  offset?: string;

  @Expose()
  @IsString()
  @IsOptional()
  ccyCodes?: string;
}
