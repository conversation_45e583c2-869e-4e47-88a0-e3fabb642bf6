import { Body, Controller, Get, Post, UseGuards } from '@nestjs/common';
import { getCurrentUser, getUserLanguage, JwtAuthGuard } from '@modules';
import { CurrentUser } from '@types';
import { UserReauthenticationDto } from '@dtos';
import { DomesticTransferSubmitRequestDto } from './domestic.dtos';
import { DomesticTransferService } from './domestic.service';
import { UserLanguage } from '@enums';

@Controller('transfer/domestic')
@UseGuards(JwtAuthGuard)
export class DomesticTransferController {
  constructor(
    private readonly domesticTransferService: DomesticTransferService,
  ) {}

  @Post('/initiate')
  async initiate(@getCurrentUser() currentUser: CurrentUser) {
    return this.domesticTransferService.initiate(currentUser);
  }

  @Post('/submit')
  async submit(
    @Body() body: DomesticTransferSubmitRequestDto,
    @getCurrentUser() currentUser: CurrentUser,
    @getUserLanguage() userLanguage: UserLanguage,
  ) {
    return this.domesticTransferService.submit(body, currentUser, userLanguage);
  }
}
