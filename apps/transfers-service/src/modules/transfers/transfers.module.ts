import { Modu<PERSON> } from '@nestjs/common';
import { LocalTransferService } from './local/local.service';
import { LocalTransferController } from './local/local.controller';
import { OwnTransferController } from './own/own.controller';
import { OwnTransferService } from './own/own.service';
import { DomesticTransferController } from './domestic/domestic.controller';
import { DomesticTransferService } from './domestic/domestic.service';
import { InternationalTransferController } from './international/international.controller';
import { InternationalTransferService } from './international/international.service';
import { IpnTransferController } from './ipn/ipn.controller';
import { IpnTransferService } from './ipn/ipn.service';

@Module({
  providers: [
    OwnTransferService,
    LocalTransferService,
    DomesticTransferService,
    InternationalTransferService,
    IpnTransferService,
  ],
  controllers: [
    OwnTransferController,
    LocalTransferController,
    DomesticTransferController,
    InternationalTransferController,
    IpnTransferController,
  ],
  exports: [IpnTransferService],
})
export class TransfersModule {}
