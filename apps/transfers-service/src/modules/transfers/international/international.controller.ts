import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import { getCurrentUser, getUserLanguage, JwtAuthGuard } from '@modules';
import { CurrentUser } from '@types';
import { InternationalTransferService } from './international.service';
import { InternationalTransferSubmitRequestDto } from './international.dtos';
import { UserLanguage } from '@enums';

@Controller('transfer/international')
@UseGuards(JwtAuthGuard)
export class InternationalTransferController {
  constructor(
    private readonly internationalTransferService: InternationalTransferService,
  ) {}

  @Post('/initiate')
  async initiate(@getCurrentUser() currentUser: CurrentUser) {
    return this.internationalTransferService.initiate(currentUser);
  }

  @Post('/submit')
  async submit(
    @Body() body: InternationalTransferSubmitRequestDto,
    @getCurrentUser() currentUser: CurrentUser,
    @getUserLanguage() userLanguage: UserLanguage,
  ) {
    return this.internationalTransferService.submit(
      body,
      currentUser,
      userLanguage,
    );
  }
}
