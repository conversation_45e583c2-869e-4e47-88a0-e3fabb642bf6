import { Injectable } from '@nestjs/common';
import { FccClientService, I18nLookupService, RedisTnxService } from '@modules';
import { CurrentUser } from '@types';
import { FccFtGenericTnxResponseDto } from '@dtos';
import { BicClearingSystems, FccRequestsEnum, UserLanguage } from '@enums';
import { v4 as UUIDV4 } from 'uuid';
import { InternationalTransferSubmitRequestDto } from './international.dtos';
import {
  getTransactionStatusByCode,
  getTransactionSubStatusByCode,
  getTransfersReauthInputs,
} from '@helpers';

@Injectable()
export class InternationalTransferService {
  constructor(
    private readonly clientService: FccClientService,
    private readonly tnxService: RedisTnxService,
    private readonly i18n: I18nLookupService,
  ) {}

  async initiate(user: CurrentUser) {
    const idempotencyKey = UUIDV4();
    const ftxRecord = await this.clientService.call<FccFtGenericTnxResponseDto>(
      {
        requestId: FccRequestsEnum.SWIFT_TRANSFER_INITIATE,
        user,
        options: {
          idempotencyKey,
        },
      },
    );

    await this.tnxService.setTnx(user, 'FT:INTERNATIONAL', {
      ...ftxRecord,
      idempotencyKey,
    });

    return {
      refId: ftxRecord.refId,
      tnxId: ftxRecord.tnxId,
      reauth: getTransfersReauthInputs(ftxRecord),
    };
  }

  async submit(
    body: InternationalTransferSubmitRequestDto,
    user: CurrentUser,
    userLanguage: UserLanguage,
  ) {
    const { tnxId, refId, idempotencyKey } =
      await this.tnxService.getTnx<FccFtGenericTnxResponseDto>(
        user,
        'FT:INTERNATIONAL',
      );

    const { clearingSystem = BicClearingSystems.SWIFT } =
      await this.clientService.call<any>({
        requestId: FccRequestsEnum.CLEARING_SYSTEM,
        payload: {
          accountId: body.beneficiaryAccount,
        },
        user,
      });

    const transaction =
      await this.clientService.call<FccFtGenericTnxResponseDto>({
        requestId: FccRequestsEnum.SWIFT_TRANSFER_SUBMIT,
        payload: {
          tnxId,
          refId,
          clearingSystem,
          ...body,
        },
        user,
        options: {
          idempotencyKey,
        },
      });

    const status = getTransactionStatusByCode(transaction.tnxStatCode);
    const subStatus = getTransactionSubStatusByCode(transaction.subTnxStatCode);

    return {
      refId: transaction.refId,
      status,
      subStatus,
      message: this.i18n.translate(
        `transfers.submit.${subStatus}`,
        userLanguage,
      ),
    };
  }
}
