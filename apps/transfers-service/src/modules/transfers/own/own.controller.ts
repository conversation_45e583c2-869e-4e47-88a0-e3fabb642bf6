import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import { getCurrentUser, getUserLanguage, JwtAuthGuard } from '@modules';
import { CurrentUser } from '@types';
import { UserReauthenticationDto } from '@dtos';
import { OwnTransferService } from './own.service';
import { OwnTransferSubmitRequestDto } from './own.dtos';
import { UserLanguage } from '@enums';

@Controller('transfer/own')
@UseGuards(JwtAuthGuard)
export class OwnTransferController {
  constructor(private readonly ownTransferService: OwnTransferService) {}

  @Post('/initiate')
  async initiate(@getCurrentUser() currentUser: CurrentUser) {
    return this.ownTransferService.initiate(currentUser);
  }

  @Post('/submit')
  async submit(
    @Body() body: OwnTransferSubmitRequestDto,
    @getCurrentUser() currentUser: CurrentUser,
    @getUserLanguage() userLanguage: UserLanguage,
  ) {
    return this.ownTransferService.submit(body, currentUser, userLanguage);
  }
}
