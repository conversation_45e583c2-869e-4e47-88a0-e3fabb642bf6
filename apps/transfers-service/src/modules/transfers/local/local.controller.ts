import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import { LocalTransferService } from '@/modules/transfers/local/local.service';
import { getCurrentUser, getUserLanguage, JwtAuthGuard } from '@modules';
import { CurrentUser } from '@types';
import { LocalTransferSubmitRequestDto } from './local.dto';
import { UserReauthenticationDto } from '@dtos';
import { UserLanguage } from '@enums';

@Controller('transfer/local')
@UseGuards(JwtAuthGuard)
export class LocalTransferController {
  constructor(private readonly localTransferService: LocalTransferService) {}

  @Post('/initiate')
  async initiate(@getCurrentUser() currentUser: CurrentUser) {
    return this.localTransferService.initiate(currentUser);
  }

  @Post('/submit')
  async submit(
    @Body() body: LocalTransferSubmitRequestDto,
    @getCurrentUser() currentUser: CurrentUser,
    @getUserLanguage() userLanguage: UserLanguage,
  ) {
    return this.localTransferService.submit(body, currentUser, userLanguage);
  }
}
