import { TransferSubmitRequestDto } from '@dtos';
import { IsFccDateFormat } from '@helpers';

import { IsNotEmpty, IsString, IsOptional, IsEmail } from 'class-validator';
/**
 * DTO for local (TPT) transfer submit requests
 */
export class LocalTransferSubmitRequestDto extends TransferSubmitRequestDto {
  /**
   * Beneficiary mode is fixed to '02' and injected by service
   * preApproved fields will be injected by service
   */

  @IsEmail()
  @IsOptional()
  beneEmail1?: string;

  @IsEmail()
  @IsOptional()
  beneEmail2?: string;

  @IsString()
  @IsNotEmpty()
  beneficiaryAccount: string;

  @IsString()
  @IsOptional()
  beneficiaryActCurCode: string;

  @IsString()
  @IsOptional()
  beneficiaryName: string;
}
