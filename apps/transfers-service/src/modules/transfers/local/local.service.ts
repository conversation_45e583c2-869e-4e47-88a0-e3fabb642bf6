import { Injectable } from '@nestjs/common';
import { FccClientService, I18nLookupService, RedisTnxService } from '@modules';
import { CurrentUser } from '@types';
import { FccFtGenericTnxResponseDto, UserReauthenticationDto } from '@dtos';
import { FccRequestsEnum, UserLanguage } from '@enums';
import { LocalTransferSubmitRequestDto } from './local.dto';
import { v4 as UUIDV4 } from 'uuid';
import {
  getTransactionStatusByCode,
  getTransactionSubStatusByCode,
  getTransfersReauthInputs,
} from '@helpers';

/**
 * Service handling local (TPT) transfers
 */
@Injectable()
export class LocalTransferService {
  constructor(
    private readonly clientService: FccClientService,
    private readonly tnxService: RedisTnxService,
    private readonly i18n: I18nLookupService,
  ) {}

  /**
   * Initiates a new local transfer
   * @returns Reference and transaction IDs
   */
  async initiate(user: CurrentUser) {
    const idempotencyKey = UUIDV4();
    const ftxRecord = await this.clientService.call<FccFtGenericTnxResponseDto>(
      {
        requestId: FccRequestsEnum.LOCAL_TRANSFER_INITIATE,
        user,
        options: {
          idempotencyKey,
        },
      },
    );

    await this.tnxService.setTnx(user, `FT:TPT`, {
      ...ftxRecord,
      idempotencyKey,
    });

    return {
      refId: ftxRecord.refId,
      tnxId: ftxRecord.tnxId,
      reauth: getTransfersReauthInputs(ftxRecord),
    };
  }

  /**
   * Submits a local (TPT) transfer
   * Injects required fields along with those not exposed to frontend
   * @param body Local transfer request data
   * @returns FCC response
   */
  async submit(
    body: LocalTransferSubmitRequestDto,
    user: CurrentUser,
    userLanguage: UserLanguage,
  ) {
    const { tnxId, refId, idempotencyKey } =
      await this.tnxService.getTnx<FccFtGenericTnxResponseDto>(user, 'FT:TPT');

    const transaction =
      await this.clientService.call<FccFtGenericTnxResponseDto>({
        requestId: FccRequestsEnum.LOCAL_TRANSFER_SUBMIT,
        payload: {
          tnxId,
          refId,
          ...body,
        },
        user,
        options: {
          idempotencyKey,
        },
      });

    const status = getTransactionStatusByCode(transaction.tnxStatCode);
    const subStatus = getTransactionSubStatusByCode(transaction.subTnxStatCode);

    return {
      refId: transaction.refId,
      status,
      subStatus,
      message: this.i18n.translate(
        `transfers.submit.${subStatus}`,
        userLanguage,
      ),
    };
  }
}
