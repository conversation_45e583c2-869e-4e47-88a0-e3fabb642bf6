import {
  BicClearingSystems,
  TransfersSubProductCodes,
  IpnClearingSystems,
} from '@enums';
import { Expose, Type } from 'class-transformer';
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsNumber,
  Max,
  Min,
  IsEnum,
  ValidateIf,
} from 'class-validator';

/**
 * DTO for listing all beneficiaries (query params).
 * Used for optional filtering.
 * Rest of FCC request query params are not exposed and listed for reference.
 */
export class ListBeneficiariesDto {
  @Expose()
  @IsOptional()
  subProductCode?: TransfersSubProductCodes;

  @Expose()
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(1000)
  limit?: number;

  @Expose()
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  @Max(999)
  offset?: number;

  transactionStatus?: string;
  preApproved?: boolean;
  accountId?: string;
  accountNumber?: string;
}

/**
 * DTO for local (TPT) beneficiary details.
 * Used to create or update a local beneficiary.
 */
export class LocalBeneficiaryDto {
  @IsString()
  @IsNotEmpty()
  name!: string;

  @IsString()
  @IsNotEmpty()
  accountNumber!: string;

  @IsString()
  @IsNotEmpty()
  currency!: string;

  @IsString()
  @IsOptional()
  nickname?: string;
}

/**
 * DTO for Domestic (ACH) beneficiary details.
 * Used to create or update a domestic beneficiary.
 */
export class DomesticBeneficiaryDto {
  @IsString()
  @IsNotEmpty()
  name!: string;

  @IsString()
  @IsNotEmpty()
  accountNumber!: string;

  @IsString()
  @IsNotEmpty()
  currency!: string;

  @IsString()
  @IsNotEmpty()
  bicCode!: string;

  @IsString()
  @IsNotEmpty()
  beneficiaryAddress!: string;

  @IsString()
  @IsOptional()
  nickname?: string;

  @IsString()
  @IsOptional()
  intermediaryBankBicCode?: string;

  @IsString()
  @IsOptional()
  bankName?: string;

  @IsString()
  @IsOptional()
  branchAddress?: string;

  @Expose()
  @IsEnum(BicClearingSystems)
  @IsOptional()
  clearingSystem?: BicClearingSystems;
}

/**
 * DTO for International (MT103/Swift) beneficiary details.
 * Used to create or update an international beneficiary.
 */
export class InternationalBeneficiaryDto extends DomesticBeneficiaryDto {
  @IsString()
  @IsNotEmpty()
  country!: string;
}

/**
 * DTO for IPN beneficiary details ( IPNBeneficiaryRequest.java )
 * Used to create or update IPN beneficiaries.
 */
export class IpnBeneficiaryDto {
  /**
   * Not needed when clearing system is IMNUM,IMWALL,IIPA
   */
  @ValidateIf(
    (o) =>
      o.clearingSystem !== IpnClearingSystems.IMNUM &&
      o.clearingSystem !== IpnClearingSystems.IMWALL &&
      o.clearingSystem !== IpnClearingSystems.IIPA,
  )
  @IsString()
  @IsNotEmpty()
  name?: string;

  @IsString()
  @IsOptional()
  currency?: string;

  @IsString()
  @IsOptional()
  nickname?: string;

  @IsString()
  @IsOptional()
  limitAmount?: string;

  @IsString()
  @IsNotEmpty()
  accountNumber!: string;

  @IsString()
  @IsNotEmpty()
  @IsEnum(IpnClearingSystems)
  clearingSystem!: IpnClearingSystems;

  /**
   * Required only when clearing system is BANK_ACCOUNT
   * Bank ID from bank list
   */
  @ValidateIf((o) => o.clearingSystem === IpnClearingSystems.BANK_ACCOUNT)
  @IsString()
  @IsNotEmpty()
  bankId?: string;
}
