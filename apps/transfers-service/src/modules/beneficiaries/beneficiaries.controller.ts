import {
  Body,
  Controller,
  Post,
  Put,
  Param,
  UseGuards,
  Delete,
  Get,
  Query,
} from '@nestjs/common';
import { getCurrentUser, getUserLanguage, JwtAuthGuard } from '@modules';
import { CurrentUser } from '@types';
import { UserLanguage } from '@enums';
import { BeneficiariesService } from './beneficiaries.service';
import {
  LocalBeneficiaryDto,
  DomesticBeneficiaryDto,
  InternationalBeneficiaryDto,
  ListBeneficiariesDto,
  IpnBeneficiaryDto,
} from './beneficiaries.dtos';

@Controller('beneficiaries')
@UseGuards(JwtAuthGuard)
export class BeneficiariesController {
  constructor(private readonly beneficiariesService: BeneficiariesService) {}

  /**
   * List all beneficiaries with optional fitering.
   * @param query Query parameters for filtering beneficiaries
   */
  @Get('/list')
  async list(
    @Query() query: ListBeneficiariesDto,
    @getCurrentUser() currentUser: CurrentUser,
  ) {
    return this.beneficiariesService.getBeneficiariesList(query, currentUser);
  }

  /**
   * Delete a beneficiary by ID.
   * @Param beneficiaryId - ID of the beneficiary to delete.
   */
  @Delete(':beneficiaryId')
  async delete(
    @Param('beneficiaryId') beneficiaryId: string,
    @getCurrentUser() currentUser: CurrentUser,
  ) {
    return this.beneficiariesService.delete(beneficiaryId, currentUser);
  }

  /**
   * List local (TPT) beneficiaries.
   */
  @Get('/local')
  async localBeneficiariesList(@getCurrentUser() currentUser: CurrentUser) {
    return this.beneficiariesService.getLocalBeneficiariesList(currentUser);
  }

  /**
   * Create local (TPT) beneficiary.
   * @Body dto - DTO containing local beneficiary details.
   */
  @Post('/local')
  async createLocalBeneficiary(
    @Body() dto: LocalBeneficiaryDto,
    @getCurrentUser() currentUser: CurrentUser,
  ): Promise<any> {
    const response = await this.beneficiariesService.createLocal(
      dto,
      currentUser,
    );
    return response;
  }

  /**
   * Update local (TPT) beneficiary by ID.
   * @param beneficiaryId - ID of the beneficiary to update.
   * @Body dto - DTO containing local beneficiary fields to update.
   */
  @Put('/local/:beneficiaryId')
  async updateLocalBeneficiary(
    @Param('beneficiaryId') beneficiaryId: string,
    @Body() dto: LocalBeneficiaryDto,
    @getCurrentUser() currentUser: CurrentUser,
  ): Promise<any> {
    const response = await this.beneficiariesService.updateLocal(
      beneficiaryId,
      dto,
      currentUser,
    );
    return response;
  }

  /**
   * List domestic (ACH/MT103) beneficiaries.
   */
  @Get('/domestic')
  async domesticBeneficiariesList(@getCurrentUser() currentUser: CurrentUser) {
    return this.beneficiariesService.getDomesticBeneficiariesList(currentUser);
  }

  /**
   * Create domestic (ACH/MT103) beneficiary.
   * @Body dto - DTO containing Domestic beneficiary details.
   */
  @Post('/domestic')
  async createDomesticBeneficiary(
    @Body() body: DomesticBeneficiaryDto,
    @getCurrentUser() currentUser: CurrentUser,
  ): Promise<any> {
    const response = await this.beneficiariesService.createDomesticBeneficiary(
      body,
      currentUser,
    );
    return response;
  }

  /**
   * Update domestic (ACH/MT103) beneficiary by beneficiaryId.
   * @param beneficiaryId - ID of the beneficiary to update.
   * @Body dto - DTO containing Domestic beneficiary fields to update.
   */
  @Put('/domestic/:beneficiaryId')
  async updateDomesticBeneficiary(
    @Param('beneficiaryId') beneficiaryId: string,
    @Body() body: DomesticBeneficiaryDto,
    @getCurrentUser() currentUser: CurrentUser,
  ): Promise<any> {
    const response = await this.beneficiariesService.updateDomesticBeneficiary(
      beneficiaryId,
      body,
      currentUser,
    );
    return response;
  }

  /**
   * List international (Swift/MT103) beneficiaries.
   */
  @Get('/international')
  async internationalBeneficiariesList(
    @getCurrentUser() currentUser: CurrentUser,
  ) {
    return this.beneficiariesService.getInternationalBeneficiariesList(
      currentUser,
    );
  }

  /**
   * Create international (Swift/MT103) beneficiary.
   * @Body dto - DTO containing International beneficiary details.
   */
  @Post('/international')
  async createInternationalBeneficiary(
    @Body() body: InternationalBeneficiaryDto,
    @getCurrentUser() currentUser: CurrentUser,
  ): Promise<any> {
    const response =
      await this.beneficiariesService.createInternationalBeneficiary(
        body,
        currentUser,
      );
    return response;
  }

  /**
   * Update International (Swift/MT103) beneficiary by beneficiaryId.
   * @param beneficiaryId - ID of the beneficiary to update.
   * @Body dto - DTO containing International beneficiary fields to update.
   */
  @Put('/international/:beneficiaryId')
  async updateInternationalBeneficiary(
    @Param('beneficiaryId') beneficiaryId: string,
    @Body() body: InternationalBeneficiaryDto,
    @getCurrentUser() currentUser: CurrentUser,
  ): Promise<any> {
    const response =
      await this.beneficiariesService.updateInternationalBeneficiary(
        beneficiaryId,
        body,
        currentUser,
      );
    return response;
  }

  /**
   * List all IPN beneficiaries.
   */
  @Get('/ipn')
  async ipnBeneficiariesList(@getCurrentUser() currentUser: CurrentUser) {
    return this.beneficiariesService.getIpnBeneficiariesList(currentUser);
  }

  /**
   * Create IPN beneficiary.
   * Supports IPN clearing systems: IMNUM, IMWALL, IIPA, BANK_ACCOUNT, IBAN, ICARD
   * @Body dto - DTO containing Ipn beneficiary details.
   */
  @Post('/ipn')
  async createIpnBeneficiary(
    @Body() dto: IpnBeneficiaryDto,
    @getCurrentUser() currentUser: CurrentUser,
    @getUserLanguage() userLanguage: UserLanguage,
  ): Promise<any> {
    return await this.beneficiariesService.createIpnBeneficiary(
      dto,
      currentUser,
      userLanguage,
    );
  }

  /**
   * Update IPN beneficiary by ID.
   * @param beneficiaryId - ID of the beneficiary to update.
   * @Body dto - DTO containing IPN beneficiary fields to update.
   */
  @Put('/ipn/:beneficiaryId')
  async updateIpnBeneficiary(
    @Param('beneficiaryId') beneficiaryId: string,
    @Body() dto: IpnBeneficiaryDto,
    @getCurrentUser() currentUser: CurrentUser,
    @getUserLanguage() userLanguage: UserLanguage,
  ): Promise<any> {
    return await this.beneficiariesService.updateIpnBeneficiary(
      beneficiaryId,
      dto,
      currentUser,
      userLanguage,
    );
  }

  /**
   * Delete Ipn beneficiary by ID.
   * @param beneficiaryId - ID of the Ipn beneficiary to delete.
   */
  @Delete('/ipn/:beneficiaryId')
  async deleteIpnBeneficiary(
    @Param('beneficiaryId') beneficiaryId: string,
    @getCurrentUser() currentUser: CurrentUser,
  ): Promise<void> {
    return await this.beneficiariesService.deleteIpnBeneficiary(
      beneficiaryId,
      currentUser,
    );
  }
}
