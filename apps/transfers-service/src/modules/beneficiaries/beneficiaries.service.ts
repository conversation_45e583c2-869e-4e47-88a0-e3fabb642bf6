import { Injectable, Logger } from '@nestjs/common';
import { FccClientService } from '@modules';
import { IpnTransferService } from '../transfers/ipn/ipn.service';
import {
  CurrentUser,
  FCCBeneficiariesList,
  FCCBeneficiariesListItem,
  FCCIpnBeneficiariesList,
  FCCIpnBeneficiaryListItem,
} from '@types';
import {
  LocalBeneficiaryDto,
  DomesticBeneficiaryDto,
  InternationalBeneficiaryDto,
  ListBeneficiariesDto,
  IpnBeneficiaryDto,
} from './beneficiaries.dtos';
import {
  FccRequestsEnum,
  IpnClearingSystems,
  TransfersSubProductCodes,
} from '@enums';
import { BENEFICIARIES_CONSTANTS } from './constants/beneficiaries.constants';

@Injectable()
export class BeneficiariesService {
  private readonly logger = new Logger(BeneficiariesService.name);

  constructor(
    private readonly clientService: FccClientService,
    private readonly ipnTransferService: IpnTransferService,
  ) {}

  /**
   * Helper method to prepare payload for beneficiary create and update methods.
   * Injects default values based on beneficiary type
   * @param dto - DTO containing beneficiary details
   * @param beneficiaryId - Optional beneficiary ID for update
   * @param isIpn - IPN beneficiary type boolean
   * @returns Prepared payload for FCC call
   */
  private preparePayload<T extends object>(
    dto: T,
    beneficiaryId?: string,
    isIpn: boolean = false,
  ): T & {
    beneficiaryId?: string;
    entityShortName: string;
    preApproved: boolean;
    country?: string;
    limitCurrency?: string;
    active?: boolean;
    currency?: string;
    limitAmount?: string;
  } {
    const typeConfig = isIpn
      ? {
          entityShortName:
            BENEFICIARIES_CONSTANTS.IPN.DEFAULT_VALUES.ENTITY_SHORT_NAME,
          preApproved: BENEFICIARIES_CONSTANTS.IPN.DEFAULT_VALUES.PRE_APPROVED,
          active: BENEFICIARIES_CONSTANTS.IPN.DEFAULT_VALUES.ACTIVE,
          country: BENEFICIARIES_CONSTANTS.IPN.COUNTRY_CODE,
          limitCurrency: BENEFICIARIES_CONSTANTS.IPN.CURRENCY_CODE,
        }
      : {
          entityShortName:
            BENEFICIARIES_CONSTANTS.DEFAULT_VALUES.ENTITY_SHORT_NAME,
          preApproved: BENEFICIARIES_CONSTANTS.DEFAULT_VALUES.PRE_APPROVED,
        };

    const ipnDefaults = isIpn
      ? {
          currency: BENEFICIARIES_CONSTANTS.IPN.CURRENCY_CODE,
          limitAmount:
            (dto as any).limitAmount ||
            BENEFICIARIES_CONSTANTS.IPN.DEFAULT_LIMIT_AMOUNT,
        }
      : {};

    return {
      ...(beneficiaryId && { beneficiaryId }),
      ...dto,
      ...typeConfig,
      ...ipnDefaults,
    };
  }

  /**
   * Get list of all beneficiaries with optional filtering.
   * @param query - Query parameters for filtering beneficiaries
   * @returns List of all/filtered beneficiaries
   * @throws Error if FCC client call fails
   */
  async getBeneficiariesList(
    query: ListBeneficiariesDto,
    user: CurrentUser,
  ): Promise<FCCBeneficiariesListItem[]> {
    try {
      this.logger.debug(
        `Fetching beneficiaries list for ${user.userId} with query: ${JSON.stringify(query)}`,
      );

      const { items = [] } =
        await this.clientService.call<FCCBeneficiariesList>({
          requestId: FccRequestsEnum.TRANSFERS_BENEFICIARIES_LIST,
          payload: { ...query },
          user,
        });

      this.logger.debug(
        `Found ${items.length} beneficiaries for ${user.userId}`,
      );
      return items;
    } catch (error) {
      this.logger.error(
        `Failed to fetch beneficiaries list for ${user.userId} with query ${JSON.stringify(query)} with error:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Delete a beneficiary.
   * @param beneficiaryId - ID of the beneficiary to delete
   * @throws Error if deletion fails
   */
  async delete(beneficiaryId: string, user: CurrentUser): Promise<void> {
    try {
      this.logger.debug(
        `Deleting beneficiary with ID: ${beneficiaryId} for user ${user.userId}`,
      );

      return this.clientService.call({
        requestId: FccRequestsEnum.TRANSFERS_BENEFICIARIES_DELETE,
        payload: { beneficiaryId },
        user,
      });
    } catch (error) {
      this.logger.error(
        `Failed to delete beneficiary ${beneficiaryId} for user ${user.userId} with error:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Get list of local beneficiaries (TPT)
   * @returns List of local beneficiaries
   */
  async getLocalBeneficiariesList(
    user: CurrentUser,
  ): Promise<FCCBeneficiariesListItem[]> {
    try {
      this.logger.debug('Fetching local beneficiaries list');

      const items = await this.getBeneficiariesList(
        { subProductCode: TransfersSubProductCodes.TPT },
        user,
      );

      this.logger.debug(`Found ${items.length} local beneficiaries`);
      return items;
    } catch (error) {
      this.logger.error('Failed to fetch local beneficiaries:', error);
      throw error;
    }
  }

  /**
   * Create a local beneficiary
   * @param dto - DTO containing beneficiary details
   */
  async createLocal(dto: LocalBeneficiaryDto, user: CurrentUser) {
    try {
      this.logger.debug('Creating local beneficiary');
      const payload = this.preparePayload(dto);
      const response = await this.clientService.call({
        requestId: FccRequestsEnum.TRANSFERS_BENEFICIARIES_CREATE_TPT,
        payload,
        user,
      });
      this.logger.debug('Successfully created local beneficiary');
      return response;
    } catch (error) {
      this.logger.error('Failed to create local beneficiary:', error);
      throw error;
    }
  }

  /**
   * Update a local beneficiary
   * @param beneficiaryId - The ID of the beneficiary to update
   * @param dto - DTO containing fields to update
   */
  async updateLocal(
    beneficiaryId: string,
    dto: LocalBeneficiaryDto,
    user: CurrentUser,
  ) {
    try {
      this.logger.debug(`Updating local beneficiary: ${beneficiaryId}`);
      const payload = this.preparePayload(dto, beneficiaryId);
      const response = await this.clientService.call({
        requestId: FccRequestsEnum.TRANSFERS_BENEFICIARIES_UPDATE_TPT,
        payload,
        user,
      });
      this.logger.debug(
        `Successfully updated local beneficiary: ${beneficiaryId}`,
      );
      return response;
    } catch (error) {
      this.logger.error(
        `Failed to update local beneficiary ${beneficiaryId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Get list of domestic beneficiaries (MT103) (country: EG)
   * @returns List of domestic beneficiaries
   */
  async getDomesticBeneficiariesList(
    user: CurrentUser,
  ): Promise<FCCBeneficiariesListItem[]> {
    try {
      this.logger.debug('Fetching domestic beneficiaries list');

      const items = await this.getBeneficiariesList(
        { subProductCode: TransfersSubProductCodes.MT103 },
        user,
      );

      const domesticItems = items.filter(
        (item) => item.country === BENEFICIARIES_CONSTANTS.COUNTRY_CODES.EGYPT,
      );
      this.logger.debug(`Found ${domesticItems.length} domestic beneficiaries`);

      return domesticItems;
    } catch (error) {
      this.logger.error('Failed to fetch domestic beneficiaries:', error);
      throw error;
    }
  }

  /**
   * Create a domestic beneficiary
   * Injects Egypt's country code
   * @param dto - DTO containing beneficiary details
   */
  async createDomesticBeneficiary(
    dto: DomesticBeneficiaryDto,
    user: CurrentUser,
  ) {
    try {
      this.logger.debug('Creating domestic beneficiary');
      const payload = this.preparePayload({
        ...dto,
        country: BENEFICIARIES_CONSTANTS.COUNTRY_CODES.EGYPT,
      });
      const response = await this.clientService.call({
        requestId: FccRequestsEnum.TRANSFERS_BENEFICIARIES_CREATE_MT103,
        payload,
        user,
      });
      this.logger.debug('Successfully created domestic beneficiary');
      return response;
    } catch (error) {
      this.logger.error('Failed to create domestic beneficiary:', error);
      throw error;
    }
  }

  /**
   * Update a domestic beneficiary
   * Injects Egypt's country code
   * @param beneficiaryId - ID of the beneficiary to update
   * @param dto - DTO containing fields to update
   */
  async updateDomesticBeneficiary(
    beneficiaryId: string,
    dto: DomesticBeneficiaryDto,
    user: CurrentUser,
  ) {
    try {
      this.logger.debug(`Updating domestic beneficiary: ${beneficiaryId}`);
      const payload = this.preparePayload(
        {
          ...dto,
          country: BENEFICIARIES_CONSTANTS.COUNTRY_CODES.EGYPT,
        },
        beneficiaryId,
      );
      const response = await this.clientService.call({
        requestId: FccRequestsEnum.TRANSFERS_BENEFICIARIES_UPDATE_MT103,
        payload,
        user,
      });
      this.logger.debug(
        `Successfully updated domestic beneficiary: ${beneficiaryId}`,
      );
      return response;
    } catch (error) {
      this.logger.error(
        `Failed to update domestic beneficiary ${beneficiaryId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Get list of international beneficiaries (MT103) (country not EG)
   * @returns List of international beneficiaries
   */
  async getInternationalBeneficiariesList(
    user: CurrentUser,
  ): Promise<FCCBeneficiariesListItem[]> {
    try {
      this.logger.debug('Fetching international beneficiaries list');

      const items = await this.getBeneficiariesList(
        { subProductCode: TransfersSubProductCodes.MT103 },
        user,
      );

      const internationalItems = items.filter(
        (item) => item.country !== BENEFICIARIES_CONSTANTS.COUNTRY_CODES.EGYPT,
      );
      this.logger.debug(
        `Found ${internationalItems.length} international beneficiaries`,
      );

      return internationalItems;
    } catch (error) {
      this.logger.error('Failed to fetch international beneficiaries:', error);
      throw error;
    }
  }

  /**
   * Create an international beneficiary
   * @param dto - DTO containing beneficiary details
   */
  async createInternationalBeneficiary(
    dto: InternationalBeneficiaryDto,
    user: CurrentUser,
  ) {
    try {
      this.logger.debug('Creating international beneficiary');
      const payload = this.preparePayload(dto);
      const response = await this.clientService.call({
        requestId: FccRequestsEnum.TRANSFERS_BENEFICIARIES_CREATE_MT103,
        payload,
        user,
      });
      this.logger.debug('Successfully created international beneficiary');
      return response;
    } catch (error) {
      this.logger.error('Failed to create international beneficiary:', error);
      throw error;
    }
  }

  /**
   * Update an international beneficiary
   * @param beneficiaryId - ID of the beneficiary to update
   * @param dto - DTO containing fields to update
   */
  async updateInternationalBeneficiary(
    beneficiaryId: string,
    dto: InternationalBeneficiaryDto,
    user: CurrentUser,
  ) {
    try {
      this.logger.debug(`Updating international beneficiary: ${beneficiaryId}`);
      const payload = this.preparePayload(dto, beneficiaryId);
      const response = await this.clientService.call({
        requestId: FccRequestsEnum.TRANSFERS_BENEFICIARIES_UPDATE_MT103,
        payload,
        user,
      });
      this.logger.debug(
        `Successfully updated international beneficiary: ${beneficiaryId}`,
      );
      return response;
    } catch (error) {
      this.logger.error(
        `Failed to update international beneficiary ${beneficiaryId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Get list of IPN beneficiaries
   * Uses IPN specific endpoint
   * @returns List of IPN beneficiaries
   */
  async getIpnBeneficiariesList(
    user: CurrentUser,
  ): Promise<FCCIpnBeneficiaryListItem[]> {
    try {
      this.logger.debug(
        `Fetching IPN beneficiaries list for user: ${user.userId}`,
      );

      const { data = [] } =
        await this.clientService.call<FCCIpnBeneficiariesList>({
          requestId: FccRequestsEnum.TRANSFERS_BENEFICIARIES_LIST_IPN,
          payload: undefined,
          user,
        });

      this.logger.debug(
        `Found ${data.length} IPN beneficiaries , user: ${user.userId}`,
      );
      return data;
    } catch (error) {
      this.logger.error(
        `Failed to fetch IPN beneficiaries for user: ${user.userId}: with error:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Create an IPN beneficiary
   * @param dto - DTO containing IPN beneficiary details
   * @returns Created beneficiary response from FCC
   */
  async createIpnBeneficiary(
    dto: IpnBeneficiaryDto,
    user: CurrentUser,
    userLanguage: string = 'en-US',
  ) {
    try {
      // Bank ID to bank name helper
      const resolvedDto = await this.resolveBankName(dto, userLanguage);

      const payload = this.preparePayload(resolvedDto, undefined, true);
      const response = await this.clientService.call({
        requestId: FccRequestsEnum.TRANSFERS_BENEFICIARIES_CREATE_IPN,
        payload,
        user,
      });
      this.logger.log(
        `Successfully created IPN beneficiary - clearingSystem: ${dto.clearingSystem} , user: ${user.userId}`,
      );
      return response;
    } catch (error) {
      this.logger.error(
        `Failed to create IPN beneficiary - clearingSystem: ${dto.clearingSystem} , user: ${user.userId} with error:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Update an IPN beneficiary
   * @param beneficiaryId - ID of the beneficiary to update
   * @param dto - DTO containing fields to update
   * @returns Updated beneficiary response from FCC
   */
  async updateIpnBeneficiary(
    beneficiaryId: string,
    dto: IpnBeneficiaryDto,
    user: CurrentUser,
    userLanguage: string = 'en-US',
  ) {
    try {
      // bank ID to bank name helper
      const resolvedDto = await this.resolveBankName(dto, userLanguage);

      const payload = this.preparePayload(resolvedDto, beneficiaryId, true);
      const response = await this.clientService.call({
        requestId: FccRequestsEnum.TRANSFERS_BENEFICIARIES_UPDATE_IPN,
        payload,
        user,
      });
      this.logger.debug(
        `Successfully updated IPN beneficiary - beneficiaryId: ${beneficiaryId}, clearingSystem: ${dto.clearingSystem} , user: ${user.userId}`,
      );
      return response;
    } catch (error) {
      this.logger.error(
        `Failed to update IPN beneficiary - beneficiaryId: ${beneficiaryId}, clearingSystem: ${dto.clearingSystem} , user: ${user.userId} with error:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Delete an IPN beneficiary
   * Uses IPN specific endpoint to delete an IPN beneficiary
   * @param beneficiaryId - ID of the IPN beneficiary to delete
   * @returns Promise resolving upon deletion
   */
  async deleteIpnBeneficiary(
    beneficiaryId: string,
    user: CurrentUser,
  ): Promise<void> {
    try {
      this.logger.debug(
        `Deleting IPN beneficiary with ID: ${beneficiaryId}, user: ${user.userId}`,
      );

      return this.clientService.call({
        requestId: FccRequestsEnum.TRANSFERS_BENEFICIARIES_DELETE_IPN,
        payload: { beneficiaryId },
        user,
      });
    } catch (error) {
      this.logger.error(
        `Failed to delete IPN beneficiary ${beneficiaryId} for user: ${user.userId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Helper method to get bank name from bank ID in IPN beneficiary create & update
   * @param dto - IPN beneficiary DTO with bankId
   * @returns DTO with bank name
   */
  private async resolveBankName(
    dto: IpnBeneficiaryDto,
    userLanguage: string,
  ): Promise<IpnBeneficiaryDto & { beneficiaryBank?: string }> {
    const { bankId, clearingSystem, ...rest } = dto;

    // Only resolve bank name for BANK_ACCOUNT clearing system
    if (clearingSystem === IpnClearingSystems.BANK_ACCOUNT && bankId) {
      try {
        const beneficiaryBank = await this.ipnTransferService.getBankNameById(
          bankId,
          userLanguage as any,
        );
        return {
          ...rest,
          clearingSystem,
          beneficiaryBank,
        };
      } catch (error) {
        this.logger.error(
          `Failed to resolve bank name for bankId: ${bankId}`,
          error,
        );
        throw error;
      }
    }
    // Return as is for other clearing systems
    return { ...rest, clearingSystem };
  }
}
