{{- $fullName := printf "%s-%s" (include "chart.fullname" .) "secure-mail-service" -}}
apiVersion: apps/v1
kind: Deployment 
metadata:
  name: {{ $fullName }}
  labels:
    {{- include "chart.labels" . | nindent 4 }}
spec:
  {{- if not .Values.secureMailService.autoscaling.enabled }}
  replicas: {{ .Values.secureMailService.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "chart.secureMailService.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.secureMailService.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "chart.secureMailService.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "chart.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.secureMailService.podSecurityContext | nindent 8 }}
      topologySpreadConstraints:
        - maxSkew: 1
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: ScheduleAnyway
          labelSelector:
            matchLabels:
              {{- include "chart.secureMailService.selectorLabels" . | nindent 14 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.secureMailService.securityContext | nindent 12 }}
          image: "{{ .Values.secureMailService.image.repository }}:{{ .Values.secureMailService.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.secureMailService.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.secureMailService.service.port }}
              protocol: TCP
          env:
          {{ include "chart.secureMailService.envVars" . | indent 10 }}
          resources:
            {{- toYaml .Values.secureMailService.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
