{{- $fullName := printf "%s-%s" (include "chart.fullname" .) "secure-mail-service" -}}
{{- if .Values.secureMailService.autoscaling.enabled }}
apiVersion: autoscaling/v2beta1
kind: HorizontalPodAutoscaler
metadata:
  name: {{ $fullName }}
  labels:
    {{- include "chart.labels" . | nindent 4 }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ $fullName }}
  minReplicas: {{ .Values.secureMailService.autoscaling.minReplicas }}
  maxReplicas: {{ .Values.secureMailService.autoscaling.maxReplicas }}
  metrics:
    {{- if .Values.secureMailService.autoscaling.targetCPUUtilizationPercentage }}
    - type: Resource
      resource:
        name: cpu
        targetAverageUtilization: {{ .Values.secureMailService.autoscaling.targetCPUUtilizationPercentage }}
    {{- end }}
    {{- if .Values.secureMailService.autoscaling.targetMemoryUtilizationPercentage }}
    - type: Resource
      resource:
        name: memory
        targetAverageUtilization: {{ .Values.secureMailService.autoscaling.targetMemoryUtilizationPercentage }}
    {{- end }}
{{- end }}
