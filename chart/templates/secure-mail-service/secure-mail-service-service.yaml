{{- $fullName := printf "%s-%s" (include "chart.fullname" .) "secure-mail-service" -}}
apiVersion: v1
kind: Service
metadata:
  name: {{ $fullName }}
  labels:
    {{- include "chart.labels" . | nindent 4 }}
spec:
  type: {{ .Values.secureMailService.service.type }}
  ports:
    - port: {{ .Values.secureMailService.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "chart.secureMailService.selectorLabels" . | nindent 4 }}
