{{- $fullName := printf "%s-%s" (include "chart.fullname" .) "accounts-service" -}}
apiVersion: v1
kind: Service
metadata:
  name: {{ $fullName }}
  labels:
    {{- include "chart.labels" . | nindent 4 }}
spec:
  type: {{ .Values.accountsService.service.type }}
  ports:
    - port: {{ .Values.accountsService.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "chart.accountsService.selectorLabels" . | nindent 4 }}
