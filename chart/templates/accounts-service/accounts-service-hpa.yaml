{{- $fullName := printf "%s-%s" (include "chart.fullname" .) "accounts-service" -}}
{{- if .Values.accountsService.autoscaling.enabled }}
apiVersion: autoscaling/v2beta1
kind: HorizontalPodAutoscaler
metadata:
  name: {{ $fullName }}
  labels:
    {{- include "chart.labels" . | nindent 4 }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ $fullName }}
  minReplicas: {{ .Values.accountsService.autoscaling.minReplicas }}
  maxReplicas: {{ .Values.accountsService.autoscaling.maxReplicas }}
  metrics:
    {{- if .Values.accountsService.autoscaling.targetCPUUtilizationPercentage }}
    - type: Resource
      resource:
        name: cpu
        targetAverageUtilization: {{ .Values.accountsService.autoscaling.targetCPUUtilizationPercentage }}
    {{- end }}
    {{- if .Values.accountsService.autoscaling.targetMemoryUtilizationPercentage }}
    - type: Resource
      resource:
        name: memory
        targetAverageUtilization: {{ .Values.accountsService.autoscaling.targetMemoryUtilizationPercentage }}
    {{- end }}
{{- end }}
