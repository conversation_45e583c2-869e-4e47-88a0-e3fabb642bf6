{{- $fullName := printf "%s-%s" (include "chart.fullname" .) "accounts-service" -}}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ $fullName }}
  labels:
    {{- include "chart.labels" . | nindent 4 }}
spec:
  {{- if not .Values.accountsService.autoscaling.enabled }}
  replicas: {{ .Values.accountsService.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "chart.accountsService.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.accountsService.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "chart.accountsService.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "chart.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.accountsService.podSecurityContext | nindent 8 }}
      topologySpreadConstraints:
        - maxSkew: 1
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: ScheduleAnyway
          labelSelector:
            matchLabels:
              {{- include "chart.accountsService.selectorLabels" . | nindent 14 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.accountsService.securityContext | nindent 12 }}
          image: "{{ .Values.accountsService.image.repository }}:{{ .Values.accountsService.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.accountsService.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.accountsService.service.port }}
              protocol: TCP
          env:
          {{ include "chart.accountsService.envVars" . | indent 10 }}
          resources:
            {{- toYaml .Values.accountsService.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
