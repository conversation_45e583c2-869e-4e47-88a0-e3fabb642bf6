{{- $fullName := printf "%s-%s" (include "chart.fullname" .) "api-adapter-service" -}}
{{- if .Values.apiAdapterService.autoscaling.enabled }}
apiVersion: autoscaling/v2beta1
kind: HorizontalPodAutoscaler
metadata:
  name: {{ $fullName }}
  labels:
    {{- include "chart.labels" . | nindent 4 }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ $fullName }}
  minReplicas: {{ .Values.apiAdapterService.autoscaling.minReplicas }}
  maxReplicas: {{ .Values.apiAdapterService.autoscaling.maxReplicas }}
  metrics:
    {{- if .Values.apiAdapterService.autoscaling.targetCPUUtilizationPercentage }}
    - type: Resource
      resource:
        name: cpu
        targetAverageUtilization: {{ .Values.apiAdapterService.autoscaling.targetCPUUtilizationPercentage }}
    {{- end }}
    {{- if .Values.apiAdapterService.autoscaling.targetMemoryUtilizationPercentage }}
    - type: Resource
      resource:
        name: memory
        targetAverageUtilization: {{ .Values.apiAdapterService.autoscaling.targetMemoryUtilizationPercentage }}
    {{- end }}
{{- end }}
