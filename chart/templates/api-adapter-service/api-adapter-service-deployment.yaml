{{- $fullName := printf "%s-%s" (include "chart.fullname" .) "api-adapter-service" -}}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ $fullName }}
  labels:
    {{- include "chart.labels" . | nindent 4 }}
spec:
  {{- if not .Values.apiAdapterService.autoscaling.enabled }}
  replicas: {{ .Values.apiAdapterService.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "chart.apiAdapterService.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.apiAdapterService.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "chart.apiAdapterService.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "chart.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.apiAdapterService.podSecurityContext | nindent 8 }}
      topologySpreadConstraints:
        - maxSkew: 1
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: ScheduleAnyway
          labelSelector:
            matchLabels:
              {{- include "chart.apiAdapterService.selectorLabels" . | nindent 14 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.apiAdapterService.securityContext | nindent 12 }}
          image: "{{ .Values.apiAdapterService.image.repository }}:{{ .Values.apiAdapterService.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.apiAdapterService.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.apiAdapterService.service.port }}
              protocol: TCP
          env:
          {{ include "chart.apiAdapterService.envVars" . | indent 10 }}
          resources:
            {{- toYaml .Values.apiAdapterService.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
