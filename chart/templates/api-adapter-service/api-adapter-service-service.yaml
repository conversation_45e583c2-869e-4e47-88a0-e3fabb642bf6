{{- $fullName := printf "%s-%s" (include "chart.fullname" .) "api-adapter-service" -}}
apiVersion: v1
kind: Service
metadata:
  name: {{ $fullName }}
  labels:
    {{- include "chart.labels" . | nindent 4 }}
spec:
  type: {{ .Values.apiAdapterService.service.type }}
  ports:
    - port: {{ .Values.apiAdapterService.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "chart.apiAdapterService.selectorLabels" . | nindent 4 }}
