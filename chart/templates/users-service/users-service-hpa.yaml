{{- $fullName := printf "%s-%s" (include "chart.fullname" .) "users-api-service" -}}
{{- if .Values.usersService.autoscaling.enabled }}
apiVersion: autoscaling/v2beta1
kind: HorizontalPodAutoscaler
metadata:
  name: {{ $fullName }}
  labels:
    {{- include "chart.labels" . | nindent 4 }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ $fullName }}
  minReplicas: {{ .Values.usersService.autoscaling.minReplicas }}
  maxReplicas: {{ .Values.usersService.autoscaling.maxReplicas }}
  metrics:
    {{- if .Values.usersService.autoscaling.targetCPUUtilizationPercentage }}
    - type: Resource
      resource:
        name: cpu
        targetAverageUtilization: {{ .Values.usersService.autoscaling.targetCPUUtilizationPercentage }}
    {{- end }}
    {{- if .Values.usersService.autoscaling.targetMemoryUtilizationPercentage }}
    - type: Resource
      resource:
        name: memory
        targetAverageUtilization: {{ .Values.usersService.autoscaling.targetMemoryUtilizationPercentage }}
    {{- end }}
{{- end }}
