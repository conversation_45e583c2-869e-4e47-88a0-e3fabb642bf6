{{- $fullName := printf "%s-%s" (include "chart.fullname" .) "users-api-service" -}}
apiVersion: v1
kind: Service
metadata:
  name: {{ $fullName }}
  labels:
    {{- include "chart.labels" . | nindent 4 }}
spec:
  type: {{ .Values.usersService.service.type }}
  ports:
    - port: {{ .Values.usersService.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "chart.usersService.selectorLabels" . | nindent 4 }}
