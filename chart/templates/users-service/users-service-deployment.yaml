{{- $fullName := printf "%s-%s" (include "chart.fullname" .) "users-api-service" -}}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ $fullName }}
  labels:
    {{- include "chart.labels" . | nindent 4 }}
spec:
  {{- if not .Values.usersService.autoscaling.enabled }}
  replicas: {{ .Values.usersService.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "chart.usersService.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.usersService.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "chart.usersService.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "chart.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.usersService.podSecurityContext | nindent 8 }}
      topologySpreadConstraints:
        - maxSkew: 1
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: ScheduleAnyway
          labelSelector:
            matchLabels:
              {{- include "chart.usersService.selectorLabels" . | nindent 14 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.usersService.securityContext | nindent 12 }}
          image: "{{ .Values.usersService.image.repository }}:{{ .Values.usersService.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.usersService.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.usersService.service.port }}
              protocol: TCP
          env:
          {{ include "chart.usersService.envVars" . | indent 10 }}
          resources:
            {{- toYaml .Values.usersService.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
