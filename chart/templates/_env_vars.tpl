{{- define "chart.accountsService.envVars" }}
- name: ACCOUNTS_SERVICE_URL
  value: {{ .Values.accountsServiceUrl.url | squote }}
- name: API_ADAPTER_SERVICE_URL
  value: {{ .Values.apiAdapterServiceUrl.url | squote }}
- name: LOOKUPS_API_SERVICE_URL
  value: {{ .Values.global.lookupsApiServiceUrl | squote }}
- name: THROTTLING_LIMIT
  value: {{ .Values.accountsService.config.THROTTLING_LIMIT | squote }}
- name: THROTTLING_TTL
  value: {{ .Values.accountsService.config.THROTTLING_TTL | squote }}
- name: JWT_SECRET
  value: {{ .Values.accountsService.config.JWT_SECRET | squote }}
- name: JWT_EXPIRES_IN
  value: {{ .Values.accountsService.config.JWT_EXPIRES_IN | squote }}
- name: REDIS_SENTINEL_NAMESPACE
  value: {{ .Values.redisSentinels.namespace | squote }}
- name: REDIS_SENTINEL_HOST
  value: {{ .Values.redisHosts }}
- name: REDIS_SENTINEL_HOST_LOOKUPS
  value: {{ .Values.redisHostsLookups }}
- name: REDIS_SENTINEL_ENABLED
  value: {{ .Values.redisSentinels.enabled | squote }}
- name: REDIS_SENTINEL_PORT
  value: {{ .Values.redisSentinels.port | squote }}
- name: REDIS_SENTINEL_PASSWORD
  valueFrom:
    secretKeyRef:
      name: {{ index .Values "cache-redis" "redis" "auth" "existingSecret" }}
      key: {{ index .Values "cache-redis" "redis" "auth" "existingSecretPasswordKey" }}
- name: CORS_URLS
  value: {{ toJson .Values.corsUrls | squote }}
{{- end }}


{{- define "chart.apiAdapterService.envVars" }}
- name: API_ADAPTER_SERVICE_URL
  value: {{ .Values.apiAdapterServiceUrl.url }}
- name: LOOKUPS_API_SERVICE_URL
  value: {{ .Values.global.lookupsApiServiceUrl | squote }}
- name: ESB_URL
  value: {{ .Values.apiAdapterService.config.esburl | squote }} 
- name: FCC_URL
  value: {{ .Values.apiAdapterService.config.fccurl | squote }} 
- name: THROTTLING_LIMIT
  value: {{ .Values.apiAdapterService.config.THROTTLING_LIMIT | squote }}
- name: THROTTLING_TTL
  value: {{ .Values.apiAdapterService.config.THROTTLING_TTL | squote }}
- name: JWT_SECRET
  value: {{ .Values.apiAdapterService.config.JWT_SECRET | squote }}
- name: NODE_TLS_REJECT_UNAUTHORIZED
  value: {{ .Values.authService.config.NODE_TLS_REJECT_UNAUTHORIZED | squote }}
- name: FASTIFY_BODYLIMIT
  value: {{ .Values.apiAdapterService.config.FASTIFY_BODYLIMIT | squote }}
- name: JWT_EXPIRES_IN
  value: {{ .Values.apiAdapterService.config.JWT_EXPIRES_IN | squote }}
- name: REDIS_SENTINEL_NAMESPACE
  value: {{ .Values.redisSentinels.namespace | squote }}
- name: REDIS_SENTINEL_HOST
  value: {{ .Values.redisHosts }}
- name: REDIS_SENTINEL_HOST_LOOKUPS
  value: {{ .Values.redisHostsLookups }}
- name: REDIS_SENTINEL_ENABLED
  value: {{ .Values.redisSentinels.enabled | squote }}
- name: REDIS_SENTINEL_PORT
  value: {{ .Values.redisSentinels.port | squote }}
- name: REDIS_SENTINEL_PASSWORD
  valueFrom:
    secretKeyRef:
      name: {{ index .Values "cache-redis" "redis" "auth" "existingSecret" }}
      key: {{ index .Values "cache-redis" "redis" "auth" "existingSecretPasswordKey" }}
- name: CORS_URLS
  value: {{ toJson .Values.corsUrls | squote }}
{{- end }}


{{- define "chart.authService.envVars" }}
- name: AUTH_SERVICE_URL
  value: {{ .Values.authServiceUrl.url }}
- name: API_ADAPTER_SERVICE_URL
  value: {{ .Values.apiAdapterServiceUrl.url }}
- name: LOOKUPS_API_SERVICE_URL
  value: {{ .Values.global.lookupsApiServiceUrl | squote }}
- name: FCC_URL
  value: {{ .Values.authService.config.fccurl | squote }} 
- name: THROTTLING_LIMIT
  value: {{ .Values.authService.config.THROTTLING_LIMIT | squote }}
- name: THROTTLING_TTL
  value: {{ .Values.authService.config.THROTTLING_TTL | squote }}
- name: NODE_TLS_REJECT_UNAUTHORIZED
  value: {{ .Values.authService.config.NODE_TLS_REJECT_UNAUTHORIZED | squote }}
- name: JWT_SECRET
  value: {{ .Values.authService.config.JWT_SECRET| squote }}
- name: JWT_EXPIRES_IN
  value: {{ .Values.authService.config.JWT_EXPIRES_IN| squote }}
- name: REDIS_SENTINEL_NAMESPACE
  value: {{ .Values.redisSentinels.namespace | squote }}
- name: REDIS_SENTINEL_HOST
  value: {{ .Values.redisHosts }}
- name: REDIS_SENTINEL_HOST_LOOKUPS
  value: {{ .Values.redisHostsLookups }}
- name: REDIS_SENTINEL_ENABLED
  value: {{ .Values.redisSentinels.enabled | squote }}
- name: CAPTCHA_TTL
  value: {{ .Values.authService.config.CAPTCHA_TTL| squote }}
- name: CAPTCHA_SIZE
  value: {{ .Values.authService.config.CAPTCHA_SIZE| squote }}
- name: CAPTCHA_NOISE
  value: {{ .Values.authService.config.CAPTCHA_NOISE| squote }}
- name: CAPTCHA_COLOR
  value: {{ .Values.authService.config.CAPTCHA_COLOR| squote }}
- name: AUTH_MAX_ATTEMPTS
  value: {{ .Values.authService.config.AUTH_MAX_ATTEMPTS| squote }}
- name: AUTH_ATTEMPT_TTL
  value: {{ .Values.authService.config.AUTH_ATTEMPT_TTL| squote }}
- name: AUTH_CAPTCHA_THRESHOLD
  value: {{ .Values.authService.config.AUTH_CAPTCHA_THRESHOLD| squote }}
- name: REDIS_SENTINEL_PORT
  value: {{ .Values.redisSentinels.port | squote }}
- name: REDIS_SENTINEL_PASSWORD
  valueFrom:
    secretKeyRef:
      name: {{ index .Values "cache-redis" "redis" "auth" "existingSecret" }}
      key: {{ index .Values "cache-redis" "redis" "auth" "existingSecretPasswordKey" }}
- name: ISAM_BASE_URL
  value: {{ .Values.authService.config.ISAM_BASE_URL | squote }} 
- name: IGNORE_SSL_VALIDATION
  value: {{ .Values.authService.config.IGNORE_SSL_VALIDATION| squote }}
- name: CORS_URLS
  value: {{ toJson .Values.corsUrls | squote }}
{{- end }}


{{- define "chart.sharedService.envVars" }}
- name: SHARED_SERVICE_URL
  value: {{ .Values.sharedServiceUrl.url | squote }}
- name: API_ADAPTER_SERVICE_URL
  value: {{ .Values.apiAdapterServiceUrl.url | squote }}
- name: LOOKUPS_API_SERVICE_URL
  value: {{ .Values.global.lookupsApiServiceUrl | squote }}
- name: THROTTLING_LIMIT
  value: {{ .Values.sharedService.config.THROTTLING_LIMIT | squote }}
- name: THROTTLING_TTL
  value: {{ .Values.sharedService.config.THROTTLING_TTL | squote }}
- name: JWT_SECRET
  value: {{ .Values.sharedService.config.JWT_SECRET | squote }}
- name: JWT_EXPIRES_IN
  value: {{ .Values.sharedService.config.JWT_EXPIRES_IN | squote }}
- name: REDIS_SENTINEL_NAMESPACE
  value: {{ .Values.redisSentinels.namespace | squote }}
- name: REDIS_SENTINEL_HOST
  value: {{ .Values.redisHosts }}
- name: REDIS_SENTINEL_HOST_LOOKUPS
  value: {{ .Values.redisHostsLookups }}
- name: REDIS_SENTINEL_ENABLED
  value: {{ .Values.redisSentinels.enabled | squote }}
- name: REDIS_SENTINEL_PORT
  value: {{ .Values.redisSentinels.port | squote }}
- name: REDIS_SENTINEL_PASSWORD
  valueFrom:
    secretKeyRef:
      name: {{ index .Values "cache-redis" "redis" "auth" "existingSecret" }}
      key: {{ index .Values "cache-redis" "redis" "auth" "existingSecretPasswordKey" }}
- name: CORS_URLS
  value: {{ toJson .Values.corsUrls | squote }}
{{- end }}


{{- define "chart.usersService.envVars" }}
- name: USERS_SERVICE_URL
  value: {{ .Values.usersServiceUrl.url | squote }}
- name: API_ADAPTER_SERVICE_URL
  value: {{ .Values.apiAdapterServiceUrl.url | squote }}
- name: LOOKUPS_API_SERVICE_URL
  value: {{ .Values.global.lookupsApiServiceUrl | squote }}
- name: THROTTLING_LIMIT
  value: {{ .Values.usersService.config.THROTTLING_LIMIT | squote }}
- name: THROTTLING_TTL
  value: {{ .Values.usersService.config.THROTTLING_TTL | squote }}
- name: JWT_SECRET
  value: {{ .Values.usersService.config.JWT_SECRET | squote }}
- name: JWT_EXPIRES_IN
  value: {{ .Values.usersService.config.JWT_EXPIRES_IN | squote }}
- name: REDIS_SENTINEL_NAMESPACE
  value: {{ .Values.redisSentinels.namespace | squote }}
- name: REDIS_SENTINEL_HOST
  value: {{ .Values.redisHosts }}
- name: REDIS_SENTINEL_HOST_LOOKUPS
  value: {{ .Values.redisHostsLookups }}
- name: REDIS_SENTINEL_ENABLED
  value: {{ .Values.redisSentinels.enabled | squote }}
- name: REDIS_SENTINEL_PORT
  value: {{ .Values.redisSentinels.port | squote }}
- name: REDIS_SENTINEL_PASSWORD
  valueFrom:
    secretKeyRef:
      name: {{ index .Values "cache-redis" "redis" "auth" "existingSecret" }}
      key: {{ index .Values "cache-redis" "redis" "auth" "existingSecretPasswordKey" }}
- name: CORS_URLS
  value: {{ toJson .Values.corsUrls | squote }}
{{- end }}

{{- define "chart.transfersService.envVars" }}
- name: TRANSFERS_SERVICE_URL
  value: {{ .Values.transfersServiceUrl.url | squote }}
- name: API_ADAPTER_SERVICE_URL
  value: {{ .Values.apiAdapterServiceUrl.url | squote }}
- name: LOOKUPS_API_SERVICE_URL
  value: {{ .Values.global.lookupsApiServiceUrl | squote }}
- name: THROTTLING_LIMIT
  value: {{ .Values.transfersService.config.THROTTLING_LIMIT | squote }}
- name: THROTTLING_TTL
  value: {{ .Values.transfersService.config.THROTTLING_TTL | squote }}
- name: JWT_SECRET
  value: {{ .Values.transfersService.config.JWT_SECRET | squote }}
- name: JWT_EXPIRES_IN
  value: {{ .Values.transfersService.config.JWT_EXPIRES_IN | squote }}
- name: REDIS_SENTINEL_NAMESPACE
  value: {{ .Values.redisSentinels.namespace | squote }}
- name: REDIS_SENTINEL_HOST
  value: {{ .Values.redisHosts }}
- name: REDIS_SENTINEL_HOST_LOOKUPS
  value: {{ .Values.redisHostsLookups }}
- name: REDIS_SENTINEL_ENABLED
  value: {{ .Values.redisSentinels.enabled | squote }}
- name: REDIS_SENTINEL_PORT
  value: {{ .Values.redisSentinels.port | squote }}
- name: REDIS_SENTINEL_PASSWORD
  valueFrom:
    secretKeyRef:
      name: {{ index .Values "cache-redis" "redis" "auth" "existingSecret" }}
      key: {{ index .Values "cache-redis" "redis" "auth" "existingSecretPasswordKey" }}
- name: CORS_URLS
  value: {{ toJson .Values.corsUrls | squote }}
{{- end }}

{{- define "chart.secureMailService.envVars" }}
- name: API_ADAPTER_SERVICE_URL
  value: {{ .Values.apiAdapterServiceUrl.url | squote }}
- name: LOOKUPS_API_SERVICE_URL
  value: {{ .Values.global.lookupsApiServiceUrl | squote }}
- name: PORT
  value: {{ .Values.secureMailService.config.PORT | squote }}
- name: THROTTLING_LIMIT
  value: {{ .Values.secureMailService.config.THROTTLING_LIMIT | squote }}
- name: THROTTLING_TTL
  value: {{ .Values.secureMailService.config.THROTTLING_TTL | squote }}
- name: JWT_SECRET
  value: {{ .Values.secureMailService.config.JWT_SECRET | squote }}
- name: JWT_EXPIRES_IN
  value: {{ .Values.secureMailService.config.JWT_EXPIRES_IN | squote }}
- name: REDIS_SENTINEL_NAMESPACE
  value: {{ .Values.redisSentinels.namespace | squote }}
- name: REDIS_SENTINEL_HOST
  value: {{ .Values.redisHosts }}
- name: REDIS_SENTINEL_HOST_LOOKUPS
  value: {{ .Values.redisHostsLookups }}
- name: REDIS_SENTINEL_ENABLED
  value: {{ .Values.redisSentinels.enabled | squote }}
- name: REDIS_SENTINEL_PORT
  value: {{ .Values.redisSentinels.port | squote }}
- name: REDIS_SENTINEL_PASSWORD
  valueFrom:
    secretKeyRef:
      name: {{ index .Values "cache-redis" "redis" "auth" "existingSecret" }}
      key: {{ index .Values "cache-redis" "redis" "auth" "existingSecretPasswordKey" }}
- name: CORS_URLS
  value: {{ toJson .Values.corsUrls | squote }}
{{- end }}





{{- define "chart.depositsService.envVars" }}
- name: ACCOUNTS_SERVICE_URL
  value: {{ .Values.accountsServiceUrl.url | squote }}
- name: API_ADAPTER_SERVICE_URL
  value: {{ .Values.apiAdapterServiceUrl.url | squote }}
- name: LOOKUPS_API_SERVICE_URL
  value: {{ .Values.global.lookupsApiServiceUrl | squote }}
- name: THROTTLING_LIMIT
  value: {{ .Values.depositsService.config.THROTTLING_LIMIT | squote }}
- name: THROTTLING_TTL
  value: {{ .Values.depositsService.config.THROTTLING_TTL | squote }}
- name: JWT_SECRET
  value: {{ .Values.depositsService.config.JWT_SECRET | squote }}
- name: JWT_EXPIRES_IN
  value: {{ .Values.depositsService.config.JWT_EXPIRES_IN | squote }}
- name: REDIS_SENTINEL_NAMESPACE
  value: {{ .Values.redisSentinels.namespace | squote }}
- name: REDIS_SENTINEL_HOST
  value: {{ .Values.redisHosts }}
- name: REDIS_SENTINEL_HOST_LOOKUPS
  value: {{ .Values.redisHostsLookups }}
- name: REDIS_SENTINEL_ENABLED
  value: {{ .Values.redisSentinels.enabled | squote }}
- name: REDIS_SENTINEL_PORT
  value: {{ .Values.redisSentinels.port | squote }}
- name: REDIS_SENTINEL_PASSWORD
  valueFrom:
    secretKeyRef:
      name: {{ index .Values "cache-redis" "redis" "auth" "existingSecret" }}
      key: {{ index .Values "cache-redis" "redis" "auth" "existingSecretPasswordKey" }}
- name: CORS_URLS
  value: {{ toJson .Values.corsUrls | squote }}
{{- end }}


{{- define "chart.cardsService.envVars" }}
- name: ACCOUNTS_SERVICE_URL
  value: {{ .Values.accountsServiceUrl.url | squote }}
- name: API_ADAPTER_SERVICE_URL
  value: {{ .Values.apiAdapterServiceUrl.url | squote }}
- name: LOOKUPS_API_SERVICE_URL
  value: {{ .Values.global.lookupsApiServiceUrl | squote }}
- name: THROTTLING_LIMIT
  value: {{ .Values.cardsService.config.THROTTLING_LIMIT | squote }}
- name: THROTTLING_TTL
  value: {{ .Values.cardsService.config.THROTTLING_TTL | squote }}
- name: JWT_SECRET
  value: {{ .Values.cardsService.config.JWT_SECRET | squote }}
- name: JWT_EXPIRES_IN
  value: {{ .Values.cardsService.config.JWT_EXPIRES_IN | squote }}
- name: REDIS_SENTINEL_NAMESPACE
  value: {{ .Values.redisSentinels.namespace | squote }}
- name: REDIS_SENTINEL_HOST
  value: {{ .Values.redisHosts }}
- name: REDIS_SENTINEL_HOST_LOOKUPS
  value: {{ .Values.redisHostsLookups }}
- name: REDIS_SENTINEL_ENABLED
  value: {{ .Values.redisSentinels.enabled | squote }}
- name: REDIS_SENTINEL_PORT
  value: {{ .Values.redisSentinels.port | squote }}
- name: REDIS_SENTINEL_PASSWORD
  valueFrom:
    secretKeyRef:
      name: {{ index .Values "cache-redis" "redis" "auth" "existingSecret" }}
      key: {{ index .Values "cache-redis" "redis" "auth" "existingSecretPasswordKey" }}
- name: CORS_URLS
  value: {{ toJson .Values.corsUrls | squote }}
{{- end }}
