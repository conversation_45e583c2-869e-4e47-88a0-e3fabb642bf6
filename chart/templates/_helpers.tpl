{{/*
Expand the name of the chart.
*/}}
{{- define "chart.name" -}}
{{- default .Chart.Name .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create a default fully qualified app name.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
If release name contains chart name it will be used as a full name.
*/}}
{{- define "chart.fullname" -}}
{{- if .Values.fullnameOverride }}
{{- .Values.fullnameOverride | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- $name := default .Chart.Name .Values.nameOverride }}
{{- if contains $name .Release.Name }}
{{- .Release.Name | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" }}
{{- end }}
{{- end }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "chart.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "chart.labels" -}}
helm.sh/chart: {{ include "chart.chart" . }}
{{ include "chart.common.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- end }}

{{/*
Selector labels
*/}}
{{- define "chart.common.selectorLabels" -}}
app.kubernetes.io/name: {{ include "chart.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}

{{/*
users service API Selector labels
*/}}
{{- define "chart.usersService.selectorLabels" -}}
{{ include "chart.common.selectorLabels" . }}
app.kubernetes.io/app: users-service
{{- end }}

{{/*
shared service API Selector labels
*/}}
{{- define "chart.sharedService.selectorLabels" -}}
{{ include "chart.common.selectorLabels" . }}
app.kubernetes.io/app: shared-service
{{- end }}

{{/*
auth API Selector labels
*/}}
{{- define "chart.authService.selectorLabels" -}}
{{ include "chart.common.selectorLabels" . }}
app.kubernetes.io/app: auth-service
{{- end }}

{{/*
api adapter selector API Selector labels
*/}}
{{- define "chart.apiAdapterService.selectorLabels" -}}
{{ include "chart.common.selectorLabels" . }}
app.kubernetes.io/app: api-adapterservice
{{- end }}

{{/*
account management API Selector labels
*/}}
{{- define "chart.accountsService.selectorLabels" -}}
{{ include "chart.common.selectorLabels" . }}
app.kubernetes.io/app: accounts-service
{{- end }}

{{/*
transfers  API Selector labels
*/}}
{{- define "chart.transfersService.selectorLabels" -}}
{{ include "chart.common.selectorLabels" . }}
app.kubernetes.io/app: transfers-service
{{- end }}


{{/*
Secure Mail API Selector labels
*/}}
{{- define "chart.secureMailService.selectorLabels" -}}
{{ include "chart.common.selectorLabels" . }}
app.kubernetes.io/app: secure-mail-service
{{- end }}


{{/*
deposits API Selector labels
*/}}
{{- define "chart.depositsService.selectorLabels" -}}
{{ include "chart.common.selectorLabels" . }}
app.kubernetes.io/app: deposits-service
{{- end }}



{{/*
cards API Selector labels
*/}}
{{- define "chart.cardsService.selectorLabels" -}}
{{ include "chart.common.selectorLabels" . }}
app.kubernetes.io/app: cards-service
{{- end }}



{{/*
Create the name of the service account to use
*/}}
{{- define "chart.serviceAccountName" -}}
{{- if .Values.serviceAccount.create }}
{{- default (include "chart.fullname" .) .Values.serviceAccount.name }}
{{- else }}
{{- default "default" .Values.serviceAccount.name }}
{{- end }}
{{- end }}


