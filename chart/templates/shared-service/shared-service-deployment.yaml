{{- $fullName := printf "%s-%s" (include "chart.fullname" .) "shared-api-service" -}}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ $fullName }}
  labels:
    {{- include "chart.labels" . | nindent 4 }}
spec:
  {{- if not .Values.sharedService.autoscaling.enabled }}
  replicas: {{ .Values.sharedService.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "chart.sharedService.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.sharedService.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "chart.sharedService.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "chart.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.sharedService.podSecurityContext | nindent 8 }}
      topologySpreadConstraints:
        - maxSkew: 1
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: ScheduleAnyway
          labelSelector:
            matchLabels:
              {{- include "chart.sharedService.selectorLabels" . | nindent 14 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.sharedService.securityContext | nindent 12 }}
          image: "{{ .Values.sharedService.image.repository }}:{{ .Values.sharedService.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.sharedService.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.sharedService.service.port }}
              protocol: TCP
          env:
          {{ include "chart.sharedService.envVars" . | indent 10 }}
          resources:
            {{- toYaml .Values.sharedService.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
