{{- $fullName := printf "%s-%s" (include "chart.fullname" .) "shared-api-service" -}}
{{- if .Values.sharedService.autoscaling.enabled }}
apiVersion: autoscaling/v2beta1
kind: HorizontalPodAutoscaler
metadata:
  name: {{ $fullName }}
  labels:
    {{- include "chart.labels" . | nindent 4 }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ $fullName }}
  minReplicas: {{ .Values.sharedService.autoscaling.minReplicas }}
  maxReplicas: {{ .Values.sharedService.autoscaling.maxReplicas }}
  metrics:
    {{- if .Values.sharedService.autoscaling.targetCPUUtilizationPercentage }}
    - type: Resource
      resource:
        name: cpu
        targetAverageUtilization: {{ .Values.sharedService.autoscaling.targetCPUUtilizationPercentage }}
    {{- end }}
    {{- if .Values.sharedService.autoscaling.targetMemoryUtilizationPercentage }}
    - type: Resource
      resource:
        name: memory
        targetAverageUtilization: {{ .Values.sharedService.autoscaling.targetMemoryUtilizationPercentage }}
    {{- end }}
{{- end }}
