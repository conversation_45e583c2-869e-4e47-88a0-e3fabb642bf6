{{- $fullName := printf "%s-%s" (include "chart.fullname" .) "shared-api-service" -}}
apiVersion: v1
kind: Service
metadata:
  name: {{ $fullName }}
  labels:
    {{- include "chart.labels" . | nindent 4 }}
spec:
  type: {{ .Values.sharedService.service.type }}
  ports:
    - port: {{ .Values.sharedService.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "chart.sharedService.selectorLabels" . | nindent 4 }}
