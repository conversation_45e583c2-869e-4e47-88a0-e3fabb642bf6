{{- $fullName := printf "%s-%s" (include "chart.fullname" .) "auth-api-service" -}}
{{- if .Values.authService.autoscaling.enabled }}
apiVersion: autoscaling/v2beta1
kind: HorizontalPodAutoscaler
metadata:
  name: {{ $fullName }}
  labels:
    {{- include "chart.labels" . | nindent 4 }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ $fullName }}
  minReplicas: {{ .Values.authService.autoscaling.minReplicas }}
  maxReplicas: {{ .Values.authService.autoscaling.maxReplicas }}
  metrics:
    {{- if .Values.authService.autoscaling.targetCPUUtilizationPercentage }}
    - type: Resource
      resource:
        name: cpu
        targetAverageUtilization: {{ .Values.authService.autoscaling.targetCPUUtilizationPercentage }}
    {{- end }}
    {{- if .Values.authService.autoscaling.targetMemoryUtilizationPercentage }}
    - type: Resource
      resource:
        name: memory
        targetAverageUtilization: {{ .Values.authService.autoscaling.targetMemoryUtilizationPercentage }}
    {{- end }}
{{- end }}
