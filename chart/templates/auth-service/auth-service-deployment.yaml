{{- $fullName := printf "%s-%s" (include "chart.fullname" .) "auth-api-service" -}}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ $fullName }}
  labels:
    {{- include "chart.labels" . | nindent 4 }}
spec:
  {{- if not .Values.authService.autoscaling.enabled }}
  replicas: {{ .Values.authService.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "chart.authService.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.authService.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "chart.authService.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "chart.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.authService.podSecurityContext | nindent 8 }}
      topologySpreadConstraints:
        - maxSkew: 1
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: ScheduleAnyway
          labelSelector:
            matchLabels:
              {{- include "chart.authService.selectorLabels" . | nindent 14 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.authService.securityContext | nindent 12 }}
          image: "{{ .Values.authService.image.repository }}:{{ .Values.authService.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.authService.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.authService.service.port }}
              protocol: TCP
          env:
          {{ include "chart.authService.envVars" . | indent 10 }}
          resources:
            {{- toYaml .Values.authService.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
