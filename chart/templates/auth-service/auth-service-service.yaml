{{- $fullName := printf "%s-%s" (include "chart.fullname" .) "auth-api-service" -}}
apiVersion: v1
kind: Service
metadata:
  name: {{ $fullName }}
  labels:
    {{- include "chart.labels" . | nindent 4 }}
spec:
  type: {{ .Values.authService.service.type }}
  ports:
    - port: {{ .Values.authService.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "chart.authService.selectorLabels" . | nindent 4 }}
