{{- $fullName := printf "%s-%s" (include "chart.fullname" .) "deposits-service" -}}
{{- if .Values.depositsService.autoscaling.enabled }}
apiVersion: autoscaling/v2beta1
kind: HorizontalPodAutoscaler
metadata:
  name: {{ $fullName }}
  labels:
    {{- include "chart.labels" . | nindent 4 }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ $fullName }}
  minReplicas: {{ .Values.depositsService.autoscaling.minReplicas }}
  maxReplicas: {{ .Values.depositsService.autoscaling.maxReplicas }}
  metrics:
    {{- if .Values.depositsService.autoscaling.targetCPUUtilizationPercentage }}
    - type: Resource
      resource:
        name: cpu
        targetAverageUtilization: {{ .Values.depositsService.autoscaling.targetCPUUtilizationPercentage }}
    {{- end }}
    {{- if .Values.depositsService.autoscaling.targetMemoryUtilizationPercentage }}
    - type: Resource
      resource:
        name: memory
        targetAverageUtilization: {{ .Values.depositsService.autoscaling.targetMemoryUtilizationPercentage }}
    {{- end }}
{{- end }}
