{{- $fullName := printf "%s-%s" (include "chart.fullname" .) "deposits-service" -}}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ $fullName }}
  labels:
    {{- include "chart.labels" . | nindent 4 }}
spec:
  {{- if not .Values.depositsService.autoscaling.enabled }}
  replicas: {{ .Values.depositsService.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "chart.depositsService.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.depositsService.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "chart.depositsService.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "chart.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.depositsService.podSecurityContext | nindent 8 }}
      topologySpreadConstraints:
        - maxSkew: 1
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: ScheduleAnyway
          labelSelector:
            matchLabels:
              {{- include "chart.depositsService.selectorLabels" . | nindent 14 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.depositsService.securityContext | nindent 12 }}
          image: "{{ .Values.depositsService.image.repository }}:{{ .Values.depositsService.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.depositsService.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.depositsService.service.port }}
              protocol: TCP
          env:
          {{ include "chart.depositsService.envVars" . | indent 10 }}
          resources:
            {{- toYaml .Values.depositsService.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
