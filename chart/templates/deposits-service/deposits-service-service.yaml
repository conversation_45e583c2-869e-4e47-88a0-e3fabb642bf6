{{- $fullName := printf "%s-%s" (include "chart.fullname" .) "deposits-service" -}}
apiVersion: v1
kind: Service
metadata:
  name: {{ $fullName }}
  labels:
    {{- include "chart.labels" . | nindent 4 }}
spec:
  type: {{ .Values.depositsService.service.type }}
  ports:
    - port: {{ .Values.depositsService.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "chart.depositsService.selectorLabels" . | nindent 4 }}
