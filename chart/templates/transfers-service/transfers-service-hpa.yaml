{{- $fullName := printf "%s-%s" (include "chart.fullname" .) "transfers-service" -}}
{{- if .Values.transfersService.autoscaling.enabled }}
apiVersion: autoscaling/v2beta1
kind: HorizontalPodAutoscaler
metadata:
  name: {{ $fullName }}
  labels:
    {{- include "chart.labels" . | nindent 4 }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ $fullName }}
  minReplicas: {{ .Values.transfersService.autoscaling.minReplicas }}
  maxReplicas: {{ .Values.transfersService.autoscaling.maxReplicas }}
  metrics:
    {{- if .Values.transfersService.autoscaling.targetCPUUtilizationPercentage }}
    - type: Resource
      resource:
        name: cpu
        targetAverageUtilization: {{ .Values.transfersService.autoscaling.targetCPUUtilizationPercentage }}
    {{- end }}
    {{- if .Values.transfersService.autoscaling.targetMemoryUtilizationPercentage }}
    - type: Resource
      resource:
        name: memory
        targetAverageUtilization: {{ .Values.transfersService.autoscaling.targetMemoryUtilizationPercentage }}
    {{- end }}
{{- end }}
