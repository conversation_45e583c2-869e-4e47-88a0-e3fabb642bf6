{{- $fullName := printf "%s-%s" (include "chart.fullname" .) "transfers-service" -}}
apiVersion: v1
kind: Service
metadata:
  name: {{ $fullName }}
  labels:
    {{- include "chart.labels" . | nindent 4 }}
spec:
  type: {{ .Values.transfersService.service.type }}
  ports:
    - port: {{ .Values.transfersService.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "chart.transfersService.selectorLabels" . | nindent 4 }}
