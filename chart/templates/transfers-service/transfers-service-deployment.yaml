{{- $fullName := printf "%s-%s" (include "chart.fullname" .) "transfers-service" -}}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ $fullName }}
  labels:
    {{- include "chart.labels" . | nindent 4 }}
spec:
  {{- if not .Values.transfersService.autoscaling.enabled }}
  replicas: {{ .Values.transfersService.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "chart.transfersService.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.transfersService.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "chart.transfersService.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "chart.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.transfersService.podSecurityContext | nindent 8 }}
      topologySpreadConstraints:
        - maxSkew: 1
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: ScheduleAnyway
          labelSelector:
            matchLabels:
              {{- include "chart.transfersService.selectorLabels" . | nindent 14 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.transfersService.securityContext | nindent 12 }}
          image: "{{ .Values.transfersService.image.repository }}:{{ .Values.transfersService.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.transfersService.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.transfersService.service.port }}
              protocol: TCP
          env:
          {{ include "chart.transfersService.envVars" . | indent 10 }}
          resources:
            {{- toYaml .Values.transfersService.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
