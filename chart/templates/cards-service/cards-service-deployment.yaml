{{- $fullName := printf "%s-%s" (include "chart.fullname" .) "cards-service" -}}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ $fullName }}
  labels:
    {{- include "chart.labels" . | nindent 4 }}
spec:
  {{- if not .Values.cardsService.autoscaling.enabled }}
  replicas: {{ .Values.cardsService.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "chart.cardsService.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.cardsService.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "chart.cardsService.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "chart.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.cardsService.podSecurityContext | nindent 8 }}
      topologySpreadConstraints:
        - maxSkew: 1
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: ScheduleAnyway
          labelSelector:
            matchLabels:
              {{- include "chart.cardsService.selectorLabels" . | nindent 14 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.cardsService.securityContext | nindent 12 }}
          image: "{{ .Values.cardsService.image.repository }}:{{ .Values.cardsService.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.cardsService.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.cardsService.service.port }}
              protocol: TCP
          env:
          {{ include "chart.cardsService.envVars" . | indent 10 }}
          resources:
            {{- toYaml .Values.cardsService.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
