{{- $fullName := printf "%s-%s" (include "chart.fullname" .) "cards-service" -}}
apiVersion: v1
kind: Service
metadata:
  name: {{ $fullName }}
  labels:
    {{- include "chart.labels" . | nindent 4 }}
spec:
  type: {{ .Values.cardsService.service.type }}
  ports:
    - port: {{ .Values.cardsService.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "chart.cardsService.selectorLabels" . | nindent 4 }}
