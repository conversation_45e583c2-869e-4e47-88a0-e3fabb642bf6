{{- $fullName := printf "%s-%s" (include "chart.fullname" .) "cards-service" -}}
{{- if .Values.cardsService.autoscaling.enabled }}
apiVersion: autoscaling/v2beta1
kind: HorizontalPodAutoscaler
metadata:
  name: {{ $fullName }}
  labels:
    {{- include "chart.labels" . | nindent 4 }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ $fullName }}
  minReplicas: {{ .Values.cardsService.autoscaling.minReplicas }}
  maxReplicas: {{ .Values.cardsService.autoscaling.maxReplicas }}
  metrics:
    {{- if .Values.cardsService.autoscaling.targetCPUUtilizationPercentage }}
    - type: Resource
      resource:
        name: cpu
        targetAverageUtilization: {{ .Values.cardsService.autoscaling.targetCPUUtilizationPercentage }}
    {{- end }}
    {{- if .Values.cardsService.autoscaling.targetMemoryUtilizationPercentage }}
    - type: Resource
      resource:
        name: memory
        targetAverageUtilization: {{ .Values.cardsService.autoscaling.targetMemoryUtilizationPercentage }}
    {{- end }}
{{- end }}
