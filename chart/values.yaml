serviceAccount:
  create: false


redisHosts: "core-banking-services-redis-node-0.core-banking-services-redis-headless.dxp-bb-dev.svc.cluster.local,core-banking-services-redis-node-1.core-banking-services-redis-headless.dxp-bb-dev.svc.cluster.local,core-banking-services-redis-node-2.core-banking-services-redis-headless.dxp-bb-dev.svc.cluster.local"
redisHostsLookups: core-banking-services-redis-headless.dxp-bb-dev.svc.cluster.local
redisSentinels:
  namespace: "mymaster"
  enabled: true
  port: 26379

global:
 lookupsApiServiceUrl: https://dashboard-gateway.apps.ocp-nonprod-01.hodomain.local


cibProxy:
  host: cibproxy.hodomain.local
  port: 8080
  
corsUrls:
  - "http://localhost:3000"
  - "http://localhost:5003"
  - "http://localhost:4997"
  - "http://localhost:8080"

apiAdapterServiceUrl:
  url: http://core-banking-services-bb-api-adapter-service:3000

accountsServiceUrl:
  url: http://core-banking-services-bb-account-management-service:3000

authServiceUrl:
  url: http://core-banking-services-bb-auth-service:3000

sharedServiceUrl:
  url: http://core-banking-services-bb-shared-service:3000

usersServiceUrl:
  url: http://core-banking-services-bb-users-service:3000

transfersServiceUrl:
  url: http://core-banking-services-bb-transfers-service:3000

apiAdapterService:
  replicaCount: 1

  image:
    repository: docker-registry.apps.ocp-nonprod-01.hodomain.local/dp/core-banking-services-bb/api-adapter-service
    pullPolicy: IfNotPresent
    tag: latest

  resources:
    limits:
      cpu: 500m
      memory: 256Mi
    requests:
      cpu: 50m
      memory: 256Mi

  service:
    type: ClusterIP
    port: 3000

  ingress:
    enabled: false

  autoscaling:
    enabled: false
  config:
    esburl: http://consumerservice-toolkit-http-cp4iuat.apps.nprodcloudpak.hodomain.local:80
    fccurl: http://localhost:8080/com.misys.portal.client.cib
    THROTTLING_LIMIT: 2
    THROTTLING_TTL: 10
    JWT_SECRET: super_secret_321
    JWT_EXPIRES_IN: 1h
    FASTIFY_BODYLIMIT: 10
    NODE_TLS_REJECT_UNAUTHORIZED: 0
accountsService:
  replicaCount: 1

  image:
    repository: docker-registry.apps.ocp-nonprod-01.hodomain.local/dp/business-banking/core-banking-services/accounts-service
    pullPolicy: IfNotPresent
    tag: latest

  resources:
    limits:
      cpu: 500m
      memory: 256Mi
    requests:
      cpu: 50m
      memory: 256Mi

  service:
    type: ClusterIP
    port: 3000

  ingress:
    enabled: false

  autoscaling:
    enabled: false
  config:
    THROTTLING_LIMIT: 2
    THROTTLING_TTL: 10
    JWT_SECRET: super_secret_321
    JWT_EXPIRES_IN: 1h
    
authService:
  replicaCount: 1

  image:
    repository: docker-registry.apps.ocp-nonprod-01.hodomain.local/dp/business-banking/core-banking-services/auth-service
    pullPolicy: IfNotPresent
    tag: latest

  resources:
    limits:
      cpu: 500m
      memory: 256Mi
    requests:
      cpu: 50m
      memory: 256Mi

  service:
    type: ClusterIP
    port: 3000

  ingress:
    enabled: false

  autoscaling:
    enabled: false
  config:
    fccurl: http://localhost:8080/com.misys.portal.client.cib
    THROTTLING_LIMIT: 2
    THROTTLING_TTL: 10
    NODE_TLS_REJECT_UNAUTHORIZED: 0
    JWT_SECRET: super_secret_321
    JWT_EXPIRES_IN: 1h
    ISAM_BASE_URL: https://isam-digital-test.hodomain.local:448/FBCCBB
    IGNORE_SSL_VALIDATION: 'true'
    CAPTCHA_TTL: 300
    CAPTCHA_SIZE: 6
    CAPTCHA_NOISE: 2
    CAPTCHA_COLOR: false
    AUTH_MAX_ATTEMPTS: 5
    AUTH_CAPTCHA_THRESHOLD: 3
    AUTH_ATTEMPT_TTL: 86400


sharedService:
  replicaCount: 1

  image:
    repository: docker-registry.apps.ocp-nonprod-01.hodomain.local/dp/business-banking/core-banking-services/shared-service
    pullPolicy: IfNotPresent
    tag: latest

  resources:
    limits:
      cpu: 500m
      memory: 256Mi
    requests:
      cpu: 50m
      memory: 256Mi

  service:
    type: ClusterIP
    port: 3000

  ingress:
    enabled: false

  autoscaling:
    enabled: false
  config:
    THROTTLING_LIMIT: 2
    THROTTLING_TTL: 10
    JWT_SECRET: super_secret_321
    JWT_EXPIRES_IN: 1h


usersService:
  replicaCount: 1

  image:
    repository: docker-registry.apps.ocp-nonprod-01.hodomain.local/dp/core-banking-services-bb/users-service
    pullPolicy: IfNotPresent
    tag: latest

  resources:
    limits:
      cpu: 500m
      memory: 256Mi
    requests:
      cpu: 50m
      memory: 256Mi

  service:
    type: ClusterIP
    port: 3000

  ingress:
    enabled: false

  autoscaling:
    enabled: false
  config:
    THROTTLING_LIMIT: 2
    THROTTLING_TTL: 10
    JWT_SECRET: super_secret_321
    JWT_EXPIRES_IN: 1h

transfersService:
  replicaCount: 1

  image:
    repository: docker-registry.apps.ocp-nonprod-01.hodomain.local/dp/business-banking/core-banking-services/users-service
    pullPolicy: IfNotPresent
    tag: latest

  resources:
    limits:
      cpu: 500m
      memory: 256Mi
    requests:
      cpu: 50m
      memory: 256Mi

  service:
    type: ClusterIP
    port: 3000

  ingress:
    enabled: false

  autoscaling:
    enabled: false
  config:
    THROTTLING_LIMIT: 2
    THROTTLING_TTL: 10
    JWT_SECRET: super_secret_321
    JWT_EXPIRES_IN: 1h


cache-redis:
  redis:
    persistence:
      size: 2GB
    auth:
      username: core-banking-services
      existingSecret: core-banking-services-redis-credentials
      existingSecretPasswordKey: password
    master:
      service:
        ports:
          redis: 6379





secureMailService:
  replicaCount: 1

  image:
    repository: docker-registry.apps.ocp-nonprod-01.hodomain.local/dp/business-banking/core-banking-services/users-service
    pullPolicy: IfNotPresent
    tag: latest

  resources:
    limits:
      cpu: 500m
      memory: 256Mi
    requests:
      cpu: 50m
      memory: 256Mi

  service:
    type: ClusterIP
    port: 3000

  ingress:
    enabled: false

  autoscaling:
    enabled: false
    
  config:
    THROTTLING_LIMIT: 2
    THROTTLING_TTL: 10
    JWT_SECRET: super_secret_321
    JWT_EXPIRES_IN: 1h
    PORT: 3007



depositsService:
  replicaCount: 1

  image:
    repository: docker-registry.apps.ocp-nonprod-01.hodomain.local/dp/business-banking/core-banking-services/accounts-service
    pullPolicy: IfNotPresent
    tag: latest

  resources:
    limits:
      cpu: 500m
      memory: 256Mi
    requests:
      cpu: 50m
      memory: 256Mi

  service:
    type: ClusterIP
    port: 3000

  ingress:
    enabled: false

  autoscaling:
    enabled: false
  config:
    THROTTLING_LIMIT: 2
    THROTTLING_TTL: 10
    JWT_SECRET: super_secret_321
    JWT_EXPIRES_IN: 1h
    

cardsService:
  replicaCount: 1

  image:
    repository: docker-registry.apps.ocp-nonprod-01.hodomain.local/dp/business-banking/core-banking-services/cards-service
    pullPolicy: IfNotPresent
    tag: latest

  resources:
    limits:
      cpu: 500m
      memory: 256Mi
    requests:
      cpu: 50m
      memory: 256Mi

  service:
    type: ClusterIP
    port: 3000

  ingress:
    enabled: false

  autoscaling:
    enabled: false
  config:
    THROTTLING_LIMIT: 2
    THROTTLING_TTL: 10
    JWT_SECRET: super_secret_321
    JWT_EXPIRES_IN: 1h
    